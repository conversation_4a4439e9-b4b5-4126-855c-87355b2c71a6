package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.CirclePeopleRelationDTO;
import net.summerfarm.model.domain.CirclePeopleRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface CirclePeopleRelationMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CirclePeopleRelation record);

    int insertSelective(CirclePeopleRelation record);

    CirclePeopleRelation selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CirclePeopleRelation record);

    int updateByPrimaryKey(CirclePeopleRelation record);

    String selectNameOrIdByActId(@Param("id") Integer id, @Param("type") Integer type);

    void deletByTypeId(@Param("id") Integer id, @Param("type") Integer type);

    List<Long> selectExistById(@Param("id") Integer id, @Param("ruleId") Integer ruleId, @Param("type") Integer type);

    int selectExist(@Param("id") Integer id, @Param("ruleId") Integer ruleId, @Param("areaNos") List<Integer> areaNo, @Param("type") Integer type, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    int selectByRuleId(Integer id);

    int selectByTypeIds(@Param("typeIds") List<Integer> typeIds, @Param("type") Integer type);

    List<CirclePeopleRelationDTO> selectTakeEffectByEntity(@Param("ruleId") Integer ruleId, @Param("type") Integer type, @Param("senderType") Integer senderType);
}