package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.SettlementDetail;
import net.summerfarm.model.input.SettlementQuery;
import net.summerfarm.model.vo.SettlementDetailVO;
import net.summerfarm.model.vo.SettlementPaymentRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SettlementDetailMapper {
    int insert(SettlementDetail record);

    int insertSelective(SettlementDetail record);

    @RequiresDataPermission(originalField = "p.area_no")
    List<SettlementPaymentRecordVO> select(SettlementQuery query);

    List<SettlementDetail> selectBySettlementId(Integer settlementId);

    /**
     * 查询已付款的采购项
     * @param purchaseNo
     * @return
     */
    List<SettlementDetailVO> selectByPurchaseNo(String purchaseNo);

    /**
     * 根据采购单号和采购计划id查询结算单
     * @param purchaseNo
     * @param purchasePlanId
     * @return
     */
    List<SettlementDetailVO> selectSettlement(@Param("purchaseNo") String purchaseNo, @Param("purchasePlanId") Integer purchasePlanId);

    /**
     * 根据采购计划查询结算单
     * @param planId
     * @return
     */
    Integer selectSettleIdByPlanId(Integer planId);
}
