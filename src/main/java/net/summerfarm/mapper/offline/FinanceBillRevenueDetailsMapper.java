package net.summerfarm.mapper.offline;

import net.summerfarm.model.DTO.finance.FinanceAccountingStoreDetailDTO;
import net.summerfarm.model.DTO.finance.FinanceOrderDTO;
import net.summerfarm.model.domain.FinanceAccountingStore;
import net.summerfarm.model.domain.offline.FinanceBillRevenueDetails;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【finance_bill_revenue_details(财务口径账期收入明细表)】的数据库操作Mapper
 * @createDate 2023-04-03 18:24:58
 * @Entity net.summerfarm.model.domain.offline.FinanceBillRevenueDetails
 */
@Repository
public interface FinanceBillRevenueDetailsMapper {

    /**
     * 门店账单信息
     *
     * @param mId           门店id
     * @param startDatetime 开始日期时间
     * @param endDatetime 结束日期时间
     * @return {@link FinanceAccountingStore}
     */
    FinanceAccountingStore storeBillInfo(@Param("mId") Integer mId, @Param("startDatetime") LocalDateTime startDatetime, @Param("endDatetime") LocalDateTime endDatetime);


    /**
     * 门店账单信息
     *
     * @param mId           门店id
     * @param startDatetime 开始日期时间
     * @param endDatetime 结束日期时间
     * @return {@link FinanceAccountingStore}
     */
    List<FinanceAccountingStoreDetailDTO> storeBillDetail(@Param("mId") Integer mId, @Param("startDatetime") LocalDateTime startDatetime, @Param("endDatetime") LocalDateTime endDatetime);

    /**
     * 选择时间范围内的单店
     *
     * @param startDatetime 开始日期时间
     * @param endDatetime   结束日期时间
     * @return {@link List}<{@link FinanceAccountingStoreDetailDTO}>
     */
    List<Integer> selectTimeRangeMId(@Param("startDatetime") LocalDateTime startDatetime, @Param("endDatetime") LocalDateTime endDatetime);

    /**
     *  查询账期订单明细
     *
     * @param orderNo
     * @return {@link List}<{@link FinanceAccountingStoreDetailDTO}>
     */
    List<FinanceOrderDTO> selectByOrderNo(@Param("orderNo") List<String> orderNo);

    /**
     * 根据订单查询账期订单
     *
     * @param orderNO
     * @return {@link FinanceBillRevenueDetails}
     */
    List<FinanceBillRevenueDetails> selectItemByOrderNo(@Param("orderNo")String orderNO);
}




