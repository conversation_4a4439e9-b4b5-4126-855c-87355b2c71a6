package net.summerfarm.dao.defectConfig.dataobject;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.service.defectConfig.enums.DefectConfigStateEnum;
import net.summerfarm.service.defectConfig.enums.DefectTypeEnum;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DefectConfigDO {

    Long id;

    /**
     * 缺陷类型
     *
     * @see DefectTypeEnum
     */
    Integer defectType;

    /**
     * 缺陷描述
     */
    String defectDesc;

    /**
     * 状态 0-删除，1-正常
     *
     * @see DefectConfigStateEnum
     */
    Integer state;

    /**
     * 删除时间
     */
    Long deletedAt;

    /**
     * 操作人
     */
    String operator;

    /**
     * 创建时间
     */
    Date createdTime;

    /**
     * 创建时间
     */
    Date updatedTime;
}
