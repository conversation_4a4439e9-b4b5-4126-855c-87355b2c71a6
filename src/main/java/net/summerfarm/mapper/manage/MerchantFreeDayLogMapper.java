package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MerchantFreeDayLog;

import java.util.List;

/**
 * 客户免邮日志mapper
 */
public interface MerchantFreeDayLogMapper {
    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);
    /**
     * 新增客户免邮日志
     * @param record 客户免邮日志
     * @return
     */
    int insert(MerchantFreeDayLog record);
    /**
     * 新增客户免邮日志
     * @param record 客户免邮日志
     * @return
     */
    int insertSelective(MerchantFreeDayLog record);
    /**
     * 根据id查看
     * @param id
     * @return
     */
    MerchantFreeDayLog selectByPrimaryKey(Long id);
    /**
     * 更新客户免邮日志
     * @param record 客户免邮日志
     * @return
     */
    int updateByPrimaryKeySelective(MerchantFreeDayLog record);
    /**
     * 更新客户免邮日志
     * @param record 客户免邮日志
     * @return
     */
    int updateByPrimaryKey(MerchantFreeDayLog record);

    /**
     * 根据MID查询
     * @return 用户免邮调整日志表
     */
    MerchantFreeDayLog selectByMid(Long mId);

    /**
     * 查看待生效的免邮日日志
     */
    List<MerchantFreeDayLog> selectByEffect();
}