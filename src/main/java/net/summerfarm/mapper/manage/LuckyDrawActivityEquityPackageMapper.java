package net.summerfarm.mapper.manage;


import net.summerfarm.model.domain.LuckyDrawActivityEquityPackage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface LuckyDrawActivityEquityPackageMapper {

    int deleteByPrimaryKey(Long id);

    int insert(LuckyDrawActivityEquityPackage record);

    int insertSelective(LuckyDrawActivityEquityPackage record);

    LuckyDrawActivityEquityPackage selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LuckyDrawActivityEquityPackage record);

    int updateByPrimaryKey(LuckyDrawActivityEquityPackage record);

    List<LuckyDrawActivityEquityPackage> selectByActivityId(Long activityId);

    int batchDeleteById(@Param("list") List<Long> equityPackageIds);

    int selectCountByEntity(@Param("list") Set<String> equityPackageNames, @Param("activityId") Long activityId);
}