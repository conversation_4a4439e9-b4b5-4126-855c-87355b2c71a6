package net.summerfarm.dao.skushare;

import net.summerfarm.biz.skushare.UpdateOrderSkuShare;
import net.summerfarm.biz.skushare.UpdateSkuShare;
import net.summerfarm.dao.skushare.dataobject.SkuShareDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SkuShareDAO {
    int deleteByPrimaryKey(Long id);

    int insert(SkuShareDO record);

    int insertSelective(SkuShareDO record);

    SkuShareDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SkuShareDO record);

    int updateByPrimaryKey(SkuShareDO record);

    SkuShareDO selectByWarehouseAndSku(@Param("warehouseNo") Integer warehouseNo, @Param("sku") String sku);

    List<SkuShareDO> selectByWarehouseAndSkus(@Param("warehouseNo") Integer warehouseNo, @Param("skus") List<String> skus);

    int updateSkuShare(UpdateSkuShare updateSkuShare);

}