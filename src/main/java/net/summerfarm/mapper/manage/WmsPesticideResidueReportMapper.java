package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.WmsPesticideResidueReport;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> ct
 * create at:  2022/4/27  15:28
 */
@Repository
public interface WmsPesticideResidueReportMapper {

    /**
     * 新增报告
     * @param residueReport
     * @return
     */
    int savePesticideResidueReport(WmsPesticideResidueReport residueReport);

    /**
     * 修改报告
     * @param residueReport
     * @return
     */
    int updatePesticideResidueReport(WmsPesticideResidueReport residueReport);

    /**
     * 查询报告信息
     * @param residueReport
     * @return
     */
    WmsPesticideResidueReport queryResidueReport(WmsPesticideResidueReport residueReport);

    /**
     * 更新状态
     * @param id
     * @param status
     * @return
     */
    int updateStatus(@Param("id") Integer id, @Param("status") Integer status);
}
