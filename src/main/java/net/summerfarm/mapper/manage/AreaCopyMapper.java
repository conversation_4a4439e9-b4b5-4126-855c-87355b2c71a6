package net.summerfarm.mapper.manage;

import java.util.List;

import net.summerfarm.model.DTO.areacopy.AreaCopyListDTO;
import net.summerfarm.model.DTO.areacopy.AreaCopyListQueryDTO;
import net.summerfarm.model.domain.AreaCopy;
import org.apache.ibatis.annotations.Param;

public interface AreaCopyMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(AreaCopy record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(AreaCopy record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    AreaCopy selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(AreaCopy record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(AreaCopy record);

    int batchInsert(@Param("list") List<AreaCopy> list);

    List<AreaCopy> selectByTargetAreaNoAndStatus(@Param("targetAreaNo") Integer targetAreaNo,
                                                 @Param("statusList") List<Integer> statusList);


    int updateStatusById(@Param("copyId") Long copyId, @Param("status") Integer status);

    List<AreaCopyListDTO> selectList(AreaCopyListQueryDTO areaCopyListQueryDTO);
}