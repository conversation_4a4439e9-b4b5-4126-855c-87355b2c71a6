package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.domain.MerchantPoolTagConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantPoolTagConfigMapper {

    int deleteById(Long id);

    int insert(MerchantPoolTagConfig merchantPoolTagConfig);

    MerchantPoolTagConfig selectById(Long id);

    int update(MerchantPoolTagConfig merchantPoolTagConfig);

    List<MerchantPoolTagConfig> listByTagId(@Param("tagId") Long tagId);

}