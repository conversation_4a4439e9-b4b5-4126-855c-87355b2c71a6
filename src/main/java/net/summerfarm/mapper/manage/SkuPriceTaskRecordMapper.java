package net.summerfarm.mapper.manage;

import java.time.LocalDate;
import java.util.List;

import net.summerfarm.model.DTO.SkuPriceTaskRecordDTO;
import net.summerfarm.model.DTO.inventory.PriceTaskRecordIntervalDTO;
import net.summerfarm.model.domain.SkuPriceTaskRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface SkuPriceTaskRecordMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(SkuPriceTaskRecord record);

    
    int insertSelective(SkuPriceTaskRecord record);

    
    SkuPriceTaskRecord selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(SkuPriceTaskRecord record);

    
    int updateByPrimaryKey(SkuPriceTaskRecord record);

    PriceTaskRecordIntervalDTO getFromAndToId(@Param("exeDate")LocalDate exeDate);

    List<SkuPriceTaskRecord> pageById(@Param("exeDate")LocalDate exeDate, @Param("startId")Long startId, @Param("pageSize")Integer pageSize);

    SkuPriceTaskRecord selectBySkuAndAreaNo(@Param("exeDate")LocalDate exeDate, @Param("sku") String sku, @Param("areaNo") Integer areaNo);

    List<SkuPriceTaskRecord> listBySkusAndAreaNo(@Param("exeDate")LocalDate exeDate, @Param("skus") List<String> skus, @Param("areaNo") Integer areaNo);

    List<SkuPriceTaskRecord> pageByAreaNo(@Param("exeDate")LocalDate exeDate, @Param("areaNo") Integer areaNo, @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);
    /**
     * 批量insert or update
     * @param batchInsertList
     */
    void batchInsertOrUpdate(List<SkuPriceTaskRecordDTO> batchInsertList);

    void batchUpdateStatusById(@Param("list") List<SkuPriceTaskRecord> list, @Param("status") Integer status);

}