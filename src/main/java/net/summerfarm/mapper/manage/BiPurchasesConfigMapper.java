package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.BiPurchasesConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BiPurchasesConfigMapper {

    int deleteAll();

    int insert(BiPurchasesConfig purchasesConfig);

    BiPurchasesConfig queryQuantity(@Param("storeNo") Integer storeNo, @Param("sku") String sku);
}
