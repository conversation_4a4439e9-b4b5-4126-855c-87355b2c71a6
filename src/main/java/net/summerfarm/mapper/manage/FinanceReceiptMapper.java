package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FinanceReceipt;
import net.summerfarm.model.vo.FinanceReceiptVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 应收核销-收款单
 * @createTime 2021年11月07日 15:33:00
 */
public interface FinanceReceiptMapper {

    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 保存数据
     * @param record
     * @return
     */
    int insert(FinanceReceipt record);

    /**
     * 保存数据
     * @param record
     * @return
     */
    int insertSelective(FinanceReceipt record);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    FinanceReceipt selectByPrimaryKey(Long id);

    /**
     * 查询收款单已经收款多少钱
     * @param financeBankFlowingWaterId
     * @return
     */
    BigDecimal selectByMoney(Long financeBankFlowingWaterId);

    /**
     * 更新数据
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(FinanceReceipt record);

    /**
     * 更新数据
     * @param record
     * @return
     */
    int updateByPrimaryKey(FinanceReceipt record);

    /**
     * 应收核销-收款单-列表
     * @param financeReceiptVO
     * @return
     */
    List<FinanceReceiptVO> listWriteOff(FinanceReceiptVO financeReceiptVO);

    /**
     * 根据收款流水id查询核销单详情
     * @param id
     * @return
     */
    List<FinanceReceiptVO> claimVerification(Long id);

    /**
     * 根据id查询收款单信息
     * @param id
     * @return
     */
    FinanceReceiptVO selectByIdInfo(Long id);

    /**
     * 统计核销超时收款单数量、金额
     * @param financeReceiptVO
     * @return
     */
    List<FinanceReceiptVO> countWriteOffTimeout(FinanceReceiptVO financeReceiptVO);


}