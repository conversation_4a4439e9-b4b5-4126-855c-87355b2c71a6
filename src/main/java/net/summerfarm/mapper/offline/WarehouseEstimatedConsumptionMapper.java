package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.offline.WarehouseEstimatedConsumption;
import net.summerfarm.model.input.purchase.StockDashboardQueryInput;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WarehouseEstimatedConsumptionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WarehouseEstimatedConsumption record);

    int insertSelective(WarehouseEstimatedConsumption record);

    WarehouseEstimatedConsumption selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WarehouseEstimatedConsumption record);

    int updateByPrimaryKey(WarehouseEstimatedConsumption record);

    List<WarehouseEstimatedConsumption> selectList(StockDashboardQueryInput queryInput);

    // 查询未来日期范围内销量总数
    List<WarehouseEstimatedConsumption> selectSumSaleQuantity(StockDashboardQueryInput queryInput);
}