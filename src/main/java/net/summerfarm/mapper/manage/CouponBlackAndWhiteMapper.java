package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.market.CouponBlackAndWhiteDTO;
import net.summerfarm.model.domain.CouponBlackAndWhite;
import net.summerfarm.model.input.CouponBlackAndWhitePageQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CouponBlackAndWhiteMapper {

    int deleteByPrimaryKey(Long id);

    int insert(CouponBlackAndWhite record);

    int insertSelective(CouponBlackAndWhite record);

    CouponBlackAndWhite selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CouponBlackAndWhite record);

    int updateByPrimaryKey(CouponBlackAndWhite record);

    /**
     * 查询卡劵黑白名单
     * @param couponBlackAndWhitePageQuery
     * @return
     */
    List<CouponBlackAndWhiteDTO> getPageBlackAndWhite(CouponBlackAndWhitePageQuery couponBlackAndWhitePageQuery);

    /**
     * 批量插入卡劵黑白名单
     * @param couponBlackAndWhites
     * @return
     */
    int insertBatchBlackAndWhites(@Param("list") List<CouponBlackAndWhite> couponBlackAndWhites);

    /**
     * 批量查询卡劵黑白名单
     * @param couponIds
     * @return
     */
    List<CouponBlackAndWhite> getAllByEntity(@Param("couponIds") List<Long> couponIds);
}