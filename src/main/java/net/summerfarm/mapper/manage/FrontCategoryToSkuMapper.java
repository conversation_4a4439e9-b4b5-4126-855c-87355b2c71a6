package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FrontCategoryToSku;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface FrontCategoryToSkuMapper {

    List<FrontCategoryToSku> queryByFrontCategoryIds(@Param ("frontCategoryIds") Set<Long> frontCategoryIds);

    List<String> queryByFrontCategoryId(@Param ("frontCategoryId") Long frontCategoryId);

    void insertBatch(@Param ("addList") List<FrontCategoryToSku> addList);

    void removeByFrontCategoryId(@Param ("frontCategoryId") Long frontCategoryId);

    List<String> queryByFrontCategoryIdAndSkuList(@Param ("frontCategoryId")Long frontCategoryId, @Param ("skus")List<String> skus);

    List<String> queryByFrontCategoryIdAndPdName(@Param ("frontCategoryId")Long frontCategoryId,@Param ("pdName")String pdName);
}