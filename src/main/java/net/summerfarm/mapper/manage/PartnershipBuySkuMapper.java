package net.summerfarm.mapper.manage;
import java.util.Collection;

import org.apache.ibatis.annotations.Param;

import net.summerfarm.model.DTO.market.PartnershipBuySkuDTO;
import net.summerfarm.model.domain.PartnershipBuySku;

import java.util.List;

public interface PartnershipBuySkuMapper {
    int insert(PartnershipBuySku record);

    int insertSelective(PartnershipBuySku record);

    PartnershipBuySku selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PartnershipBuySku record);

    int updateByPrimaryKey(PartnershipBuySku record);

    List<PartnershipBuySkuDTO> selectByConfigId(@Param("configId") Long configId);

    /**
     * 查询某个营销-多人拼团配置下的所有
     *
     * @param configId
     * @return
     */
    List<PartnershipBuySku> selectAllByConfigId(@Param("configId") Long configId);

    void updateMinSaleNumByConfigIdAndSku(@Param("configId") Long configId,
                                          @Param("sku") String sku,
                                          @Param("adminId") Integer adminId,
                                          @Param("minSaleNum") Integer minSaleNum);

    int updateDeleteFlagByConfigIdAndSkuIn(@Param("deleteFlag")Integer deleteFlag,
                                           @Param("configId") Long configId,
                                           @Param("adminId") Integer adminId,
                                           @Param("skus") Collection<String> skus);


}