package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MsgBody;
import net.summerfarm.model.input.MsgInput;
import net.summerfarm.model.vo.MsgVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MsgBodyMapper {
    int insertSelective(MsgBody record);

    MsgBody selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(MsgBody record);

    List<MsgVO> select(MsgInput selectKeys);

    MsgVO selectOne(MsgInput selectKeys);

    int countUnfinish(@Param("templateId") int templateId, @Param("keyword") String keyword);

    /**
     * 查看所有未处理的售罄和预警消息
     * @return
     */
    List<MsgBody> selectUnfinishMsg();
}