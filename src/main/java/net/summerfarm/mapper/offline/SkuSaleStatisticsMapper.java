package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.offline.SkuSaleStatisticsPO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @desc
 * <AUTHOR>
 * @Date 2022/12/5 11:00
 **/
@Mapper
public interface SkuSaleStatisticsMapper {

    /**
     * 根据日期标识查询sku销量信息
     *
     * <AUTHOR>
     * @Date 2022/12/9 10:24
     * @param dateFlag 日期标识
     **/
    List<SkuSaleStatisticsPO> selectListByDateFlag(String dateFlag);

}
