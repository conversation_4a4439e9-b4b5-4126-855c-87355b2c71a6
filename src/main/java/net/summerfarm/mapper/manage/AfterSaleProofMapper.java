package net.summerfarm.mapper.manage;


import net.summerfarm.model.vo.RejectSaleNeedStockVo;
import net.summerfarm.model.domain.AfterSaleProof;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Repository
public interface AfterSaleProofMapper {


    List<AfterSaleProof> select(String afterSaleOrderNo);

    /**
     * 根据修改时间降序查询一条proof
     * @param afterSaleOrderNo
     * @return
     */
    List<AfterSaleProof> selectDescOne(String afterSaleOrderNo);

     List<AfterSaleProof> selectList(List<String> afterSaleOrderNos);

    void updateById(AfterSaleProof afterSaleProof);

    void insert(AfterSaleProof afterSaleProof);

    Integer selectTimingQuantity(String orderNo);

    Integer selectTimingQuantityByStatus(String orderNo);

    /**
     * 查询售后成功的售后订单总金额
     * @param orderNo
     * @return
     */
    BigDecimal selectSuccessNumByOrderNo(String orderNo);

    /**
     * 查询时间内售后完成的已到货售后订单总金额
     * @param orderNo
     * @return
     */
    BigDecimal selectSuccessNumByOrderNoAndTime(@Param("orderNo") String orderNo, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询时间内售后成功的已到货售后订单总金额
     * @param orderNo
     * @param startTime
     * @param endTime
     * @return
     */
    BigDecimal selectSuccessOrderNo(@Param("orderNo") String orderNo, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询时间内售后成功的未到货售后订单总金额
     * @param orderNo
     * @return
     */
    BigDecimal selectSuccessOrderNoBack(@Param("orderNo") String orderNo);


    /**
     * 根据orderNo和sku查找售后成功订单金额
     * @param orderNo
     * @param sku
     * @return
     */
    BigDecimal  selectSuccessNumByOrderItem(@Param("orderNo") String orderNo, @Param("sku") String sku);

    /**
     * 根据orderNo和sku统计时间范围内售后成功订单金额
     *
     * @param orderNo       订单编号
     * @param sku           sku
     * @param billStartTime 账单时间
     * @param billEndTime   账单结束时间
     * @return {@link BigDecimal}
     */
    BigDecimal selectSuccessNumByOrderItemAndTime(@Param("orderNo") String orderNo, @Param("sku") String sku, @Param("billStartTime") LocalDateTime billStartTime, @Param("billEndTime") LocalDateTime billEndTime);

    /**
     * 根据orderNo和sku查找未到货售后成功订单金额
     * @param orderNo
     * @param sku
     * @return
     */
    BigDecimal  selectSuccessUnDeliveryByOrderItem(@Param("orderNo") String orderNo, @Param("sku") String sku);

    /**
     * 根据orderNo和sku查找售后成功订单金额
     * @param orderNoList
     * @return
     */
    BigDecimal selectSuccessNumByOrderNoList(@Param("orderNoList") List<String> orderNoList);

    List<AfterSaleProof> selectByOrderNo(@Param("orderNo") String orderNo, @Param("sku") String sku);

    /**
    * 根据售后单号更新
    */
    void updateByAfterSalePoof(AfterSaleProof afterSaleProof);

    /**
     * 修改的时候过滤status为4的售后单
     * @param afterSaleProof
     */
    void updateByAfterSalePoofNotReCommit(AfterSaleProof afterSaleProof);


    AfterSaleProof selectLast(String afterSaleOrderNo);

    /**
     * 参数要求非空
     * @param afterSaleOrderNos 非空
     * @return
     */
    @MapKey("afterSaleOrderNo")
    Map<String,AfterSaleProof> selectLastMap(List<String> afterSaleOrderNos);

    List<RejectSaleNeedStockVo> getYesterdayRejectOrderInfo(@Param("yesterday") LocalDateTime yesterday);

    List<RejectSaleNeedStockVo> getRejectOrderInfo(@Param("time") LocalDateTime time);

    int saveBatchAfterSaleProof(List<AfterSaleProof> afterSaleProofs);

}
