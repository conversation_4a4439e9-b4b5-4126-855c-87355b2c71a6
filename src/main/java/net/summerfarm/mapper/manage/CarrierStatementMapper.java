package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CarrierStatement;
import net.summerfarm.model.domain.CarrierStatementExtras;
import net.summerfarm.model.vo.CarrierStatementVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 配送结算单相关.
 */
@Mapper
public interface CarrierStatementMapper {
    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     * @param record
     * @return
     */
    int insert(CarrierStatement record);

    /**
     * 新增
     * @param record
     * @return
     */
    int insertSelective(CarrierStatement record);

    /**
     * 根据id查看
     * @param id
     * @return
     */
    CarrierStatement selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CarrierStatement record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(CarrierStatement record);

    /**
     * 根据条件查看
     * @param carrierStatement
     * @return
     */
    List<CarrierStatementVo> selectCarrierStatement(CarrierStatementVo carrierStatement);

    /**
     * 批量插入
     * @param carrierStatements
     */
    void insertBatch(@Param("carrierStatements") List<CarrierStatement> carrierStatements);

    /**
     * 根据杂费查看
     * @param carrierStatementExtras
     * @return
     */
    List<CarrierStatementVo> selectCarrierStatementByExtra(CarrierStatementExtras carrierStatementExtras);
}