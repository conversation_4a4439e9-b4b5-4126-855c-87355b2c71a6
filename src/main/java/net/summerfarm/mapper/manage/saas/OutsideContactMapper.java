package net.summerfarm.mapper.manage.saas;

import net.summerfarm.model.domain.saas.OutsideContact;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> ct
 * create at:  2022/5/12  19:40*/
@Repository
public interface OutsideContactMapper {

    /**
     * 插入
     * @param outsideContact
     * @return
     */
    int saveOutsideContact(OutsideContact outsideContact);

    /**
     * 查询
     * @param outsideContact
     * @return
     */
    OutsideContact selectOutsideContact(OutsideContact outsideContact);

    /**
     * 更新
     * @param outsideContact
     * @return
     */
    int updateOutsideContact(OutsideContact outsideContact);


    /**
     * 查询
     * @param id
     * @return
     */
    OutsideContact selectOutsideContactById(Integer id);


    OutsideContact selectByOrderNo(String taskNo);

    /**
     * 修改定位
     * @param outsideContact 入参
     */
    void updatePoi(OutsideContact outsideContact);
}
