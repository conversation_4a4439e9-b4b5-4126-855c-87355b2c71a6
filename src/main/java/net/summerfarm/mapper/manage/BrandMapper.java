package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Brand;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BrandMapper {

    int insertSelective(Brand record);

    int updateByPrimaryKeySelective(Brand record);

    List<Brand> select(Brand selectKeys);

    int delete(Integer brandId);

    List<Brand> selectByName(@Param("name") String name);

    Brand selectByBrandId(@Param("brandId")Integer brandId);


    /**
     * 查询所有品牌名称
     * @return 所有品牌数据
     */
    List<Brand> selectAll();
}