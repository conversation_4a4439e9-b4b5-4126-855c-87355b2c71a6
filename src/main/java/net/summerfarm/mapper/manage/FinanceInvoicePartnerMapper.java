package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FinanceInvoicePartner;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FinanceInvoicePartnerMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinanceInvoicePartner record);

    /**
     * 插入新数据
     * @param record
     * @return
     */
    int insertSelective(FinanceInvoicePartner record);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    FinanceInvoicePartner selectByPrimaryKey(Long id);

    /**
     * 查询数据
     * @param supplierId
     * @return
     */
    FinanceInvoicePartner selectSupplierId(Integer supplierId);

    /**
     * 查询合作方数据
     * @return
     * @param supplierName
     */
    List<FinanceInvoicePartner> select(String supplierName);

    int updateByPrimaryKeySelective(FinanceInvoicePartner record);

    int updateByPrimaryKey(FinanceInvoicePartner record);

    List<FinanceInvoicePartner> selectBySupplierIdAndExpenseType(@Param("expenseType") Integer expenseType, @Param("supplierIdList") List<Integer> supplierIdList);

}