package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.FinanceRevenueDocument;
import net.summerfarm.model.input.FinanceRevenueDocumentInput;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface FinanceRevenueDocumentMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinanceRevenueDocument record);

    int insertSelective(FinanceRevenueDocument record);

    FinanceRevenueDocument selectByPrimaryKey(Long id);

    /**
     * 根据收入确认节点和类型确认单据日期及其他信息
     * @param manualCode
     * @param type
     * @param startTime
     * @return
     */
    List<FinanceRevenueDocument> selectByType(@Param("manualCode") Integer manualCode, @Param("type") Integer type, @Param("startTime") LocalDate startTime);

    /**
     * 本期确认收入的订单明细日期
     * @param manualCode
     * @param type
     * @param startTime
     * @return
     */
    List<FinanceRevenueDocument> selectByTypeDetail(@Param("manualCode") Integer manualCode, @Param("type") Integer type, @Param("startTime") LocalDate startTime);

    /**
     * 查询现结/账期单据表信息
     * @param financeRevenueDocument
     * @return
     */
    List<FinanceRevenueDocument> selectByAll(FinanceRevenueDocumentInput financeRevenueDocument);

    /**
     * 查询现结/账期单据表信息数量
     * @param financeRevenueDocument
     * @return
     */
    Integer selectByAllCount(FinanceRevenueDocumentInput financeRevenueDocument);

    /**
     * 运费数据
     * @param financeRevenueDocument
     * @return
     */
    List<FinanceRevenueDocument> selectByFee(FinanceRevenueDocumentInput financeRevenueDocument);

    /**
     * 超时加单
     * @param financeRevenueDocument
     * @return
     */
    List<FinanceRevenueDocument> selectByOutTimesFee(FinanceRevenueDocumentInput financeRevenueDocument);

    /**
     * 单据主表账单
     * @param financeRevenueDocument
     * @return
     */
    List<FinanceRevenueDocument> selectByBill(FinanceRevenueDocumentInput financeRevenueDocument);

    int updateByPrimaryKeySelective(FinanceRevenueDocument record);

    int updateByPrimaryKey(FinanceRevenueDocument record);
}