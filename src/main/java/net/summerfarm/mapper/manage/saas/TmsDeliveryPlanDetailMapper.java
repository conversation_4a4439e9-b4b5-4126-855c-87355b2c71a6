package net.summerfarm.mapper.manage.saas;

import net.summerfarm.model.domain.AfterSaleDeliveryPath;
import net.summerfarm.model.domain.saas.TmsDeliveryPlanDetail;
import net.summerfarm.model.vo.OrderItemVO;
import net.summerfarm.model.vo.TmsDeliveryPlanDetailVO;
import net.summerfarm.model.vo.TmsDeliveryPlanDetailsVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/5/12  19:26
 */
@Repository
public interface TmsDeliveryPlanDetailMapper {


    /**
     * 插入
     * @param planDetail
     * @return
     */
    int saveTmsDeliveryPlanDetail(TmsDeliveryPlanDetail planDetail);

    /**
     * 查询
     * @param planDetail
     * @return
     */
    TmsDeliveryPlanDetail selectTmsDeliveryPlanDetail(TmsDeliveryPlanDetail planDetail);

    /**
     * 更新
     * @param planDetail
     * @return
     */
    int updateTmsDeliveryPlanDetail(TmsDeliveryPlanDetail planDetail);

    /**
     * 批量 新增
     * @param planDetail
     * @return
     */
    int saveBatchTmsDeliveryPlanDetail(List<TmsDeliveryPlanDetail> planDetail);

    /**
     * 根据配送单id查询配送单详情
     * @param deliveryPlanId
     * @return
     */
    List<TmsDeliveryPlanDetail> selectDetailByDeliveryPlanId(Integer deliveryPlanId);

    /**
     *
     * @param deliveryTime
     * @param contactId
     * @return
     */
    List<OrderItemVO> selectByDTAndCId(@Param("deliveryTime") LocalDate deliveryTime,@Param("contactId") Long contactId);

    /**
     * 查询有效的配送单详情
     * @param deliveryPlanId
     * @return
     */
    List<TmsDeliveryPlanDetail> selectEffectiveDetailByPlanId(Integer deliveryPlanId);


    /**
     * 查询有效的配送单详情
     * @param list
     * @return
     */
    List<TmsDeliveryPlanDetail> selectDetailByPlanId(List<Integer> list);


    /**
     * 根据配送单id查询配送单库存信息
     * @param deliveryPlanId
     * @return
     */
    List<TmsDeliveryPlanDetailVO> selectWarehouseDetailByDeliveryPlanId(Integer deliveryPlanId);

    List<OrderItemVO> selectSaasByDpId(Integer dpId);

    /**
     * 查询saas售后数据
     * @param afterSaleDeliveryPath
     * @return
     */
    List<OrderItemVO> selectAfterDetail(AfterSaleDeliveryPath afterSaleDeliveryPath);

    /**
     * 根据城配仓和配送日期查询Saas 有效的配送
     * @param deliveryTime
     * @param storeNo
     * @param contactId
     * @return
     */
    List<TmsDeliveryPlanDetailsVO> selectSendTmsDeliveryPlanDetail(@Param("deliveryTime") LocalDate deliveryTime, @Param("storeNo") Integer storeNo,
                                                                   @Param("contactId")Long contactId);

    /**
     * 根据城配仓和配送日期查询Saas 有效的回收
     * @param deliveryTime
     * @param storeNo
     * @param contactId
     * @return
     */
    List<TmsDeliveryPlanDetailsVO> selectRecycleTmsDeliveryPlanDetail(@Param("deliveryTime") LocalDate deliveryTime,@Param("storeNo") Integer storeNo,
                                                                     @Param("contactId")Long contactId);


    /**
     * 根据配送计划ID查询配送详情
     * @param deliverPlanId
     * @return
     */
    List<TmsDeliveryPlanDetailVO> selectItemByPlanId(@Param("deliverPlanId") Integer deliverPlanId);
}
