package net.summerfarm.service;

import com.cosfo.summerfarm.model.SummerfarmResult;
import com.cosfo.summerfarm.model.input.SummerfarmDeliveryInput;
import com.cosfo.summerfarm.model.input.SummerfarmStockInput;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.tms.ChangeFenceMsg;
import net.summerfarm.model.domain.AdCodeMsg;
import net.summerfarm.model.domain.ChangeFence;
import net.summerfarm.model.domain.Contact;
import net.summerfarm.model.domain.Fence;
import net.summerfarm.model.input.OuterAddressReq;
import net.summerfarm.model.vo.AllEfficientFenceVo;
import net.summerfarm.model.vo.FenceVO;
import net.summerfarm.model.vo.LargeAreaVO;
import net.summerfarm.warehouse.model.vo.WarehouseInventoryMappingVO;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2021/8/10  14:17
 */
public interface FenceService {

    /**
     * 新增围栏
     */
    AjaxResult insertFence(FenceVO fenceVO);

    /**
     * 查询围栏信息
     */
    AjaxResult selectFence(FenceVO fenceVo, Integer pageIndex, Integer pageSize);

    /**
     * 根据id查询围栏详情
     */
    FenceVO selectFenceById(Integer id);

    /**
     * 围栏作废
     */
    AjaxResult deleteFenceById(Integer id);

    /**
     * 修改
     */
    AjaxResult updateFence(FenceVO fenceVO);

    /**
     * 根据poi获取对应运营区域
     */
    Integer  getAreaNo(String poi, Contact contact);


    Integer getAreaNo(String city, String area);

    /**
     * 根据poi获取对应运营区域
     */
    Integer  getStoreNo(String poi, Contact contact);


    /**
     * 根据运营区域获取配送仓信息 运营区域可以对应多个城配仓
     * 城配仓对应的库存使用仓完全一致，且 城配仓对应的sku使用库存仓完全一致
     */
    Integer selectStoreNoByAreaNo(Integer areaNo);


    /**
     * 根据配送仓获取配送仓对应的运营区域列表
     */
    List<Integer> selectAreaNoByStoreNo(Integer storeNo);


    /**
     * 根据 运营区域,sku 获取库存使用仓编号
     */
    Integer selectWarehouseNo(Integer areaNo, String sku);

    /**
     * 获取城市对应的pickId相同的城配仓列表
     */
    List<Integer> selectStoreNoListByAreaNo(Integer areaNo);


    /**
     * 高德新增service
     */
    AjaxResult insertGdService();


    /**
     * 查询已有的城市配送仓信息
     */
    AjaxResult selectCityArea();

    /**
     * 删除所有围栏
     */
    AjaxResult deleteAll();

    /**
     *
     * 根据配送仓获取对应围栏信息
     *
     */
    List<Integer> selectFenceByStoreNo(Integer storeNo);


    /**
     *
     * 根据配送仓获取对应城市
     * @param mId
     * @return
     */
    List<LargeAreaVO> selectAreaByStoreNo(Long mId);

    /**
     * 根据城配仓编号获取所有打包数据
     */
    List<Integer> selectPackAllByStoreNo(Integer storeNo);

    /**
     * 获取要切仓的围栏信息
     *
     */
    List<Fence> selectChangeFence(Integer areaNo);

    /**
     * 围栏切换 城配仓
     */

    int updateStoreNo(Fence fence);

    /**
     * 围栏暂停
     */
    AjaxResult stopFence(FenceVO fenceVO);

    Set<Integer>  selectStoreByAreaNo(Integer areaNo);

    Fence selectFenceDetailById(Integer fenceId);

    /**
     * 当前仓是否在切仓中
     */
    Boolean selectChangeStore(Integer storeNo);

    /**
     * 围栏切换 根据城市维度处理
     */
    void changeFenceByAreaNo(Integer areaNo);

    /**
     * 切仓处理样品单和售后单  根据城市维度处理
     * @param changeFences
     */
    List<ChangeFenceMsg> handleSampleAndAfterSaleByAreaNo(List<ChangeFence> changeFences);

    /**
     * 根据城市和区取围栏
     * @param area 城市
     * @param city 区
     * return 围栏
     */
    FenceVO selectFenceByCityArea(String area, String city);

    /**
     * 根据运营服务区域初始化围栏配送数据接口(会造成测试环境围栏配送数据重复)
     */
    @Deprecated()
    void initFenceDelivery();
    /**
     * 查询配送数据
     * @param contact 联系人信息
     * @return 配送数据
     */

    /*Integer[] selectDeliveryFrequentByFence(Contact contact);*/

    /**
     * 查询当前库存仓下所有的运营区域
     * @param warehouseNo 库存仓编号
     * @return 当前库存仓下所有运营区域
     */
    Set<Integer> selectAreaNosByWareNos(Integer warehouseNo, String sku);

    /**
     * 获取所有有效的围栏信息
     * @return
     */
    List<AllEfficientFenceVo> getAllEfficientFence();


    /**
     * 根据配送周期计算配送日期
     * @param storeNo
     * @param startTime
     * @param deliveryFrequent
     * @return
     */
    LocalDate checkCycleDelivery( Integer storeNo, LocalDateTime startTime, Integer[] deliveryFrequent);

    /**
     *
     * @param fenceVO
     * @return
     */
    Integer[] getDeliveryTimeArray(FenceVO fenceVO);

    /**
     * 根据城市查询围栏信息
     *
     * @param cityName
     * @return
     */
    List<FenceVO> selectByCityName(String cityName);

    /**
     * 根据城市批量查询围栏信息
     *
     * @param cityNames
     * @return
     */
    List<FenceVO> selectByCityNames(List<String> cityNames);

    /**
     * 根据区域查询实际行政城市
     *
     * @param areaNo
     * @return
     */
    List<String> selectCityNameByAreaNo(Integer areaNo);

    List<AdCodeMsg> selectAdCodeMsgByStoreNo(Integer storeNo);

    /**
     * 运营区域围栏packId处理
     */
    void packHandleFix();

    /**
     * 更新围栏组
     * @param fenceIds 围栏Id
     * @param packId 围栏组
     */
    void fencePackUpdate(List<Integer> fenceIds, Integer packId);

    /**
     * 根据配送周期校验配送日期是否有效
     * @param storeNo 城配仓编号
     * @param deliveryTime 配送时间
     * @param deliveryFrequent 配送周期
     * @param closeTime 截单时间
     * @return 是否有效，true:有效，false:无效
     *//*
    boolean checkDeliveryTime(Integer storeNo, LocalDate deliveryTime, Integer[] deliveryFrequent, LocalTime closeTime);*/
}
