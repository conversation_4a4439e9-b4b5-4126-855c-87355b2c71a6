package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.BiStockUpConfig;
import net.summerfarm.model.vo.BiStockUpConfigVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BiStockUpConfigMapper {

    int insert(BiStockUpConfig biStockUpConfig);

    BiStockUpConfig selectOne(BiStockUpConfig biStockUpConfig);

    @RequiresDataPermission(originalField = "bsuc.area_no")
    List<BiStockUpConfigVO> select(BiStockUpConfigVO biStockUpConfigVO);

    int delete(Integer id);

    List<BiStockUpConfigVO> selectAll();


}
