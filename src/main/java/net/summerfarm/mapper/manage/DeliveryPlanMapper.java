package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.UpdateDeliveryDateDto;
import net.summerfarm.model.domain.DeliveryPlan;
import net.summerfarm.model.domain.DeliveryPlanRelationDO;
import net.summerfarm.model.domain.InterestRateConfig;
import net.summerfarm.model.vo.*;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Repository
public interface DeliveryPlanMapper {

    DeliveryPlan selectById(Integer id);

    List<DeliveryPlan> selectByIds(List<Integer> id);

    DeliveryPlan selectOne(DeliveryPlan deliveryPlan);

    /**
     * 查询订单配送计划信息
     * @param orderNo 订单编号
     * @return 订单配送信息
     */
    List<DeliveryPlanVO> selectByOrderNo(String orderNo);

    /**
     * 查询普通+已退款订单配送计划信息
     * @param orderNo 订单编号
     * @return 订单配送信息
     */
    List<DeliveryPlanVO> selectByOrderNoIntercept(String orderNo);
    /**
     * 获取普通订单实际送达时间
     * @param orderNo 订单编号
     * @return 订单实际送达时间信息
     */
    LocalDateTime selectDeliveryTimeByOrderNo(String orderNo);

    /**
     * 获取未配送的配送单
     * @param contactId 地址id
     * @return
     */
    List<DeliveryPlan> selectWaitByContact(@Param("contactId")Long contactId);

    void deleteById(Integer id);

    void updateById(DeliveryPlan plan);

    void updateByorderNo(DeliveryPlan plan);

    int updateDeliveryTime(@Param("orderNo") String orderNo, @Param("deliveryTime") LocalDate deliveryTime, @Param("contactId") Long contactId);

    int insert(DeliveryPlan plan);

    int sumDelivering(@Param("sku") String sku);


    List<DeliveryPlanVO> getDeliveryVO(@Param("date") LocalDate date, @Param("storeNo") Integer storeNo,@Param("allCategoriesDate") LocalDate allCategoriesDate);

    BigDecimal selectGmvByDeliveryPlan(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("delivertTime") LocalDate delivertTime, @Param("startTime") LocalDate startTime,
                                       @Param("endTime") LocalDate endTime, @Param("dataPermission") Set<Integer> dataPermission);

    int sumTimingTotal(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    int sumTimingDelivery(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    //每日配送数量
    List<Integer> deliveryNumByDay(@Param("sku") String sku, @Param("areaNos") List<Integer> areaNos, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    DeliveryPlan queryDeliveryTimeByOrderNo(String orderNo);

    /**
     * 待配送省心送数量
     * @param sku
     * @param startDate
     * @param endDate
     * @return
     */
    List<TimingQuantityVO> queryTimingQuantity(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

   /**
    * 查询库存仓未冻结的配送计划
    * @param warehouseNo
    * @return list
   */
   List<DeliveryPlanVO> selectDeliveryPlan(@Param("warehouseNo") Integer warehouseNo);

   /**
    * 查询时间段内sku省心送未冻结的配送计划
    * @param startDate
    * @param endDate
    * @param sku
    * @param status
    * @param storeNo
    * @return:
   */
   List<DeliveryPlanVO> getSomeTimeUnLockPlan(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("trustStoreNo") Integer trustStoreNo, @Param("status") Integer status);

    List<DeliveryPlan> queryDeliveryPlans(DeliveryPlan deliveryPlan);

    /**
     * 查询已冻结、未配送数量
     * @param sku
     * @param now
     * @param ownAreaNos
     * @return
     */
    Integer lockUnDeliveryQuantity(@Param("sku") String sku, @Param("date") LocalDate now, @Param("list") List<Integer> ownAreaNos);


    List<DeliveryPlanVO>  selectAreaStoreByStoreNo(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("storeNo") Integer storeNo, @Param("status") Integer status);

    List<DeliveryPlan> selectByContactId(@Param("deliveryTime") LocalDate deliveryTime, @Param("contactId") Long contactId);

    List<DeliveryPlan> selectInterceptByContactId(@Param("deliveryTime") LocalDate deliveryTime, @Param("contactId") Long contactId);

    /**
     * 查询待配送的、有效的省心送配送计划
     * @param areaNo 城市编号
     * @param minPlanDate 配送日期
     * @return
     */
    List<DeliveryPlanVO> selectEffectiveTimingPlan(@Param("areaNo") Integer areaNo, @Param("minPlanDate") LocalDate minPlanDate);

    /**
     * 修改省心送的归属仓
     * @param deliveryPlanList 省心送列表
     * @param changeStoreNo 新仓
     */
    void updateOrderStoreNo(@Param("list") List<DeliveryPlanVO> deliveryPlanList, @Param("changeStoreNo") Integer changeStoreNo);

    /**
     * 更新配送状态
     * @param deliveryPlanList 配送计划
     * @param status 状态
     */
    void updateStatusBatch(@Param("list") List<DeliveryPlanVO> deliveryPlanList, @Param("status") Integer status);


    /**
     *
     * 查询未完成配送的省心送订单已经配送完成的数量
     *
    */
    List<DeliveryPlan> selectQuantityByStoreNo(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("areaNo") Integer areaNo);



    int updateDeliveryPlanStoreNo(@Param("orderNoList") List<String> orderNoList, @Param("storeNo") Integer storeNo);

    List<DeliveryPlan> selectByStatusTime(@Param("orderNo") String orderNo, @Param("deliveryTime") LocalDate deliveryTime);

    /**
    * 普通订单
    */
    List<DeliveryPlanVO> selectDeliveryPlanByStoreNo(@Param("storeNo") Integer storeNo, @Param("deliveryTime") LocalDate deliveryTime);

    /**
    * 已经冻结省心送订单
    */
    List<DeliveryPlanVO> selectLockTimingByStoreNo(@Param("storeNo") Integer storeNo, @Param("deliveryTime") LocalDate deliveryTime);

    /**
    * 带配送省心送订单
    */
    List<DeliveryPlanVO> selectUnLockTimingByStoreNo(@Param("storeNo") Integer storeNo, @Param("deliveryTime") LocalDate deliveryTime);

    /**
    * 获取 15天内省心送未冻结订单
     * @param deliveryTime
     * @param storeNo
     * @return
    */
    List<DeliveryPlanVO> selectFifUnLockTiming(@Param("storeNo") Integer storeNo, @Param("deliveryTime") LocalDate deliveryTime);

    /**
     * 获取区间内为冻结的省心送订单
     * @param endDate
     * @param startDate
     * @return
     */
    List<DeliveryPlanVO> selectUnLockTimingByTime(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 获取延期配送的省心送订单
     * @param date
     * @return
    */
    List<DeliveryPlanVO> selectTimingByPutOffTime(LocalDate date);

    /**
     * 根据配送时间和围栏地址/城配仓查询配送信息
     * @param updateDeliveryDateDto
     * @return
     */
    List<BatchUpdateDeliveryDateVo> selectByUpdateDeliveryDateDto(UpdateDeliveryDateDto updateDeliveryDateDto);

    /**
     * 根据配送计划ID和时间范围查询数据
     * @param contactIdList
     * @param nowDeliveryDate
     * @param newDeliveryDate
     * @param dpIdList
     * @return
     */
    List<DeliveryPlanHeartVo> queryBetweenDateByIds(@Param("contactIdList") List<Integer> contactIdList,
                                                    @Param("nowDeliveryDate") LocalDate nowDeliveryDate,
                                                    @Param("newDeliveryDate") LocalDate newDeliveryDate,
                                                    @Param("dpIdList") ArrayList<Integer> dpIdList);

    /**
     * 根据地址ID和时间范围查询数据
     * @param contactId
     * @param nowDeliveryDate
     * @param newDeliveryDate
     * @return
     */
    List<DeliveryPlanHeartVo> queryBetweenDateByContactId(@Param("contactId")Integer contactId,
                                                          @Param("nowDeliveryDate")LocalDate nowDeliveryDate,
                                                          @Param("newDeliveryDate")LocalDate newDeliveryDate);

    /**
     * 批量取消配送计划
     * @param ids
     * @param status
     */
    void cancelBatchUpdate(@Param("ids")List<Integer> ids,@Param("status") int status);

    /**
     * 修改配送计划
     * @param batchUpdateDeliveryDateVo
     */
    void updateDelivery(BatchUpdateDeliveryDateVo batchUpdateDeliveryDateVo);

    void updateDeliveryTimeAndOldDate(@Param("orderNo") String orderNo, @Param("deliveryTime") LocalDate deliveryTime, @Param("contactId") Long contactId,
                                      @Param("oldDeliveryTime")LocalDate oldDeliveryTime);

    /**
     * 根据订单编号集合批量查询配送记录
     * @param orderNos 订单编号集合
     * @return 配送记录集合
     */
    List<DeliveryRecordVO> selectDeliveryRecords(@Param(value = "orderNos") Set<String> orderNos);

    /**
     * 根据配送时间和联系人id和订单编号查询当前点位的订单数据
     * @param deliveryTime
     * @param contactId
     * @param orderNo
     * @return
     */
    List<GetOrderDataVO> getOtherOrderDataByDTAndCIdAndONo(@Param("deliveryTime")LocalDate deliveryTime, @Param("contactId") Integer contactId, @Param("orderNo") String orderNo);

    /**
     * 根据外部订单号和配送时间查询配送详情信息
     * @param orderNo 外部订单号
     * @param deliveryTime 配送时间
     * @return 配送详情信息
     */
    DeliveryDetailVO selectDeliveryDetailByOrderNo(@Param("orderNo") String orderNo, @Param("deliveryTime") LocalDate deliveryTime,@Param("contactId") String contactId);

    /**
     * 根据订单配送仓单号查询配送计划
     *
     * @param orderStoreNoList
     * @return
     */
    List<DeliveryPlan>  selectByOrderStoreNo(@Param("orderStoreNoList") List<Integer> orderStoreNoList,
                                             @Param("startTime") LocalDateTime startTIme,
                                             @Param("endTime") LocalDateTime endTime);

    List<DeliveryPlanVO> selectByOrderNoAndContactId(@Param("orderNo") String orderNo,@Param("contactId") Long contactId);

    List<DeliveryPlan> selectByDeliverTime(@Param("orderNo") String orderNo, @Param("deliveryTime") LocalDate deliveryTime,@Param("contactId") Long contactId);


    /**
     * 查询订单配送计划数量
     * @param storeNo
     * @param contactId
     * @param deliveryTime
     * @return
     */
    List<DeliveryPlan> selectByContactIdStoreNo(@Param("deliveryTime") LocalDate deliveryTime,
                                         @Param("contactId") Long contactId,@Param("storeNo") Integer storeNo);

    /**
     * 查询订单拦截数据
     * @param deliveryTime
     * @param contactId
     * @param storeNo
     * @return
     */
    List<DeliveryPlan> selectInterceptData(@Param("deliveryTime") LocalDate deliveryTime,
                                           @Param("contactId") Long contactId,@Param("storeNo") Integer storeNo);

    /**
     * 查询订单拦截数据
     * @param deliveryTime
     * @param contactId
     * @param orderNo
     * @return
     */
    List<DeliveryPlan> selectTimingOrderPlan(@Param("deliveryTime") LocalDate deliveryTime,
                                           @Param("contactId") Long contactId,@Param("orderNo") String orderNo);

    List<DeliveryPlan> selectSassByContactId(@Param("deliveryTime") LocalDate deliveryTime,
                                             @Param("contactId") Long contactId,@Param("storeNo") Integer storeNo);

    int selectDeliveryListByStoreNo(@Param(value = "orderNos") List<String> orderNos,@Param("storeNo") Integer storeNo,@Param("deliveryPlanId") Integer deliveryPlanId);

    List<DeliveryPlan> listDeliveryPlanByOrderNoList(@Param(value = "orderNos") List<String> orderNos);

    void updateIntereptByIds(@Param(value = "ids")List<Integer> ids);


    List<DeliveryPlanRelationDO> selectByOrderNoList(@Param(value = "orderNoList") List<String> orderNoList);


    List<DeliveryPlan> listDeliveryPlanByOrderNoForceMaster(String orderNo);
}