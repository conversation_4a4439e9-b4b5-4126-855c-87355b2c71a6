package net.summerfarm.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.util.express.DefaultContext;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.enums.DynamicHandlerNameEnum;
import net.summerfarm.mapper.manage.DynamicPriceFieldConfigMapper;
import net.summerfarm.mapper.manage.DynamicPriceFieldMapper;
import net.summerfarm.mapper.manage.DynamicPriceModelConfigMapper;
import net.summerfarm.mapper.manage.DynamicPriceTaskMapper;
import net.summerfarm.mapper.manage.DynamicPriceWhiteListMapper;
import net.summerfarm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.mapper.offline.DynamicPriceStatisticsMapper;
import net.summerfarm.model.DTO.DynamicPriceStatisticsQueryDTO;
import net.summerfarm.model.DTO.inventory.DynamicPriceAdjustDTO;
import net.summerfarm.model.domain.DataSynchronizationInformation;
import net.summerfarm.model.domain.DynamicPriceField;
import net.summerfarm.model.domain.DynamicPriceFieldConfig;
import net.summerfarm.model.domain.DynamicPriceModelConfig;
import net.summerfarm.model.domain.DynamicPriceTask;
import net.summerfarm.model.domain.DynamicPriceWhiteList;
import net.summerfarm.model.domain.offline.DynamicPriceStatistics;
import net.summerfarm.mq.AutoChangePriceV2Listener;
import net.summerfarm.service.bms.ExpressRunnerService;
import net.summerfarm.service.helper.dynamic.DynamicPriceFieldHandler;
import net.summerfarm.service.helper.dynamic.DynamicPriceServiceHelper;
import net.xianmu.common.exception.BizException;
import net.xianmu.task.process.XianMuJavaProcessor;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 自动调价补偿
 *
 * @author: <EMAIL>
 * @create: 2023/2/1
 */
@Slf4j
@Component
public class PriceAdjustCompensationProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private AutoChangePriceV2Listener autoChangePriceV2Listener;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        try {
            String instanceParameterStr = context.getInstanceParameters();
            log.info("自动调价补偿任务开始执行, instanceParameters: 【{}】", JSON.toJSONString(instanceParameterStr));
            if (null == instanceParameterStr) {
                log.info("任务参数为空！！");
                return new ProcessResult(true);
            }
            long start = System.currentTimeMillis();
            List<Long> list = JSON.parseArray(instanceParameterStr, Long.class);
            if (CollUtil.isEmpty(list)) {
                log.info("任务参数为空！！");
                return new ProcessResult(true);
            }
            list.forEach(businessId -> autoChangePriceV2Listener.doPriceAdjust(businessId));
            log.info("自动调价补偿任务执行完成!!耗时：{}ms", System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("定时任务执行失败!", e);
        }
        return new ProcessResult(true);
    }
}
