package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MajorRebate;
import net.summerfarm.model.vo.MajorRebateVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wjd on 2018/7/17.
 */
@Repository
public interface MajorRebateMapper {

    void insert(MajorRebate majorRebate);

    List<MajorRebate> selectGroup(MajorRebate majorRebate);


    List<MajorRebate> selectGroupSku(MajorRebate majorRebate);

    List<MajorRebate> selectGroupAreaNo(MajorRebate majorRebate);

    MajorRebate  selectOne(MajorRebate majorRebate);

    int delete(@Param("sku") String sku, @Param("type") Integer type, @Param("number") Double number, @Param("cate") Integer cate);

    List<MajorRebateVO> selectGroupName(MajorRebate majorRebate);

    /**
     * 查询有效的价格配置
     * @param adminId
     * @param sku
     * @return
     */
    List<MajorRebate> selectValidSkuList(@Param("adminId") Integer adminId, @Param("sku") String sku);

    List<MajorRebate> selectList(@Param("adminId") Integer adminId,
                                 @Param("areaNo") Integer areaNo,
                                 @Param("cate") Integer cate,
                                 @Param("skus") List<String> skus);
}
