package net.summerfarm.dao.skushare.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderSkuShareDO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 转出sku编码
     */
    private String transferOutSku;

    /**
     * 转入sku编码
     */
    private String transferInSku;

    /**
     * 共享的总数量
     */
    private Integer shareTotalQuantity;

    /**
     * 转入sku的共享比例，形如1:2
     */
    private String shareRate;

    /**
     * sku转换比例，形如1:2
     */
    private String transferRate;

    /**
     * 单号
     */
    private String outOrderNo;

    /**
     * 单据类型
     */
    private Integer outOrderType;

    /**
     * 应转出数量
     */
    private Integer shouldTransferOutQuantity;

    /**
     * 已转出数量
     */
    private Integer actualTransferOutQuantity;

    /**
     * 待转出数量
     */
    private Integer remainingTransferOutQuantity;

    /**
     * 应转入数量
     */
    private Integer shouldTransferInQuantity;

    /**
     * 已转入数量
     */
    private Integer actualTransferInQuantity;

    /**
     * 待转入数量
     */
    private Integer remainingTransferInQuantity;

    /**
     * 状态，0：取消，1：正常，2：清零
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

}