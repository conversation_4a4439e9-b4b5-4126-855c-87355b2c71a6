package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.Coupon;
import net.summerfarm.model.domain.MerchantCoupon;
import net.summerfarm.model.input.TaskInput;
import net.summerfarm.model.vo.MerchantCouponVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public interface MerchantCouponMapper {

    int insertSelective(MerchantCoupon record);

    MerchantCoupon select(Integer id);

    int delete(Integer id);

    @RequiresDataPermission(originalField = "m.area_no")
    List<MerchantCouponVO> selectVOs(MerchantCouponVO selectKeys);

    /**
     * 修改优惠券所属
     *
     * @param oldMid 原mid
     * @param newMid 新mid
     * @return
     */
    int changeCouponMerchant(@Param("oldMid") Long oldMid, @Param("newMid") Long newMid);

    List<MerchantCoupon> selectByCouponId(@Param("mId") Long mId, @Param("couponId") Integer couponId);

    List<MerchantCoupon> selectByCouponIdAndMid(@Param("mId") Long mId, @Param("couponId") Integer couponId);

    /**
     * 批量插入优惠券数据
     * @param couponList
     */
    void insertBatch(List<MerchantCoupon> couponList);

    /**
     * 获取商户所有可用的优惠券
     * @param mId 商户id
     * @return 商户所有可用的优惠券
     */
    List<MerchantCouponVO> selectCouponByMid(Long mId);

    /**
     * 获取优惠券发放记录
     * @param couponId 卡券id
     * @return 优惠券发放记录
     */
    List<MerchantCouponVO> statisticsList(int couponId, int startId, int offset);

    /**
     * 查询任务卡券的商户使用数量
     * @param couponId 卡券id
     * @param adminId 登录bd的id
     * @param used 是否使用
     * @return 使用数量
     */
    List<Long> taskCoupon(@Param("couponId") int couponId, @Param("adminId") Integer adminId, @Param("used") Integer used);

    /**
     * 查询登录用户的当日任务
     * @param adminId 登录用户id
     * @param startTime 任务时间
     * @return 选中当日的任务
     */
    List<Coupon> taskNameDay(@Param("adminId") Integer adminId, @Param("startTime") LocalDateTime startTime);

    /**
     * 查询任务详情,客户维度
     * @param taskInput 查询条件
     * @return 任务详情,客户维度
     */
    List<MerchantCouponVO> taskDetail(TaskInput taskInput);

    /**
     * 该任务下商户是否填写备注
     * @param id 任务id
     * @param mId 商户id
     * @return 是否填写备注
     */
    int noteState(@Param("id") Integer id, @Param("mId") Long mId);

    /**
     * 查询今日所有的任务ids
     * @param mId 商户id
     * @return 任务ids
     */
    List<Integer> taskIdList(Long mId);

    /**
     * 审核通过后通过发放记录id批量插入商户券
     * @param sendId 发放记录id
     */
    void insertBatchBySendId(Long sendId);

    /**
     * 通过发放记录id查询商户券
     * @param sendId 发放记录id
     * @return 商户券集合
     */
    List<MerchantCoupon> selectBySendId(Long sendId);

    /**
     * 通过发放记录id查询商户券--强制走主库
     * @param sendId 发放记录id
     * @return 商户券集合
     */
    List<MerchantCoupon> selectBySendIdForceMaster(Long sendId);

    /**
     * 通过发放记录id删除商户优惠券
     * @param sendId 发放记录id
     */
    int recall(Long sendId);

    Set<Long> selectByCouponIdAndMids(@Param("list") List<Long> mIds, @Param("couponId") Integer couponId);

    int updateByPrimaryKeySelective(MerchantCoupon record);

}