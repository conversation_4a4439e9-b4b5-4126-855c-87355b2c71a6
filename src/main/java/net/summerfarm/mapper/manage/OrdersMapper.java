package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.DTO.BillOrderDeliveryInfoDTO;
import net.summerfarm.model.DTO.OrderAndItemDTO;
import net.summerfarm.model.DTO.finance.FinanceOrderDTO;
import net.summerfarm.model.domain.FruitSales;
import net.summerfarm.model.domain.OrderItem;
import net.summerfarm.model.domain.Orders;
import net.summerfarm.model.domain.StockTaskPick;
import net.summerfarm.model.input.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.vo.finance.AdminOrderExportVo;
import net.summerfarm.model.vo.finance.FinancePeriodOrderItemExportVO;
import net.summerfarm.tms.client.dist.req.standard.DistOrderBatchQueryStandardReq;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Repository
public interface OrdersMapper {

    int insertSelective(Orders record);

    @RequiresDataPermission(originalField = "m.area_no")
    List<OrderVO> select(OrderVO selectKeys);

    /**
     * 根据订单号查询订单信息
     * @param orderNo 订单编号
     * @return 订单信息
     */
    @RequiresDataPermission(originalField = "o.area_no")
    OrderVO selectByOrderyNo(String orderNo);

    OrderVO selectByOrderyNoNoPermission(String orderNo);

    List<ClosingOrder> selectClosingOrder(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("areaNo") Integer areaNo, @Param("parentNo") Integer parentNo, @Param("orderNo")String orderNo, @Param("deliverytype")Boolean deliverytype);

    List<ClosingOrder> selectClosingOrderNew(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("areaNo") Integer areaNo, @Param("parentNo") Integer parentNo, @Param("orderNo")String orderNo, @Param("deliverytype")Boolean deliverytype,
                                             @Param("interceptFlag")Integer interceptFlag);

    List<ClosingOrder> selectClosingOrderWithOrderNo(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("areaNo") Integer areaNo, @Param("parentNo") Integer parentNo, @Param("orderNo")String orderNo, @Param("deliverytype")Boolean deliverytype,
                                                     @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("orderList") List<String> orderList, @Param("inOrOut") Boolean inOrOut,Integer type);

    List<ClosingOrder> selectClosingOrderWithAreaNo (@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("areaNoList") List<Integer> areaNoList, @Param("parentNo") Integer parentNo, @Param("orderNo")String orderNo, @Param("deliverytype")Boolean deliverytype);

    // 销售出库任务查询(与截单略微不同)
    List<OrderItem> selectSaleOutOrder(@Param("deliveryDate") LocalDate deliveryDate, @Param("areaNo") Integer areaNo, @Param("trustStoreNo") Integer trustStoreNo, @Param("deliverytype")Boolean deliverytype);

    // 越仓出库任务查询
    List<OrderItem> selectSaleOutOrderForCross(@Param("deliveryDate") LocalDate deliveryDate, @Param("areaNo") Integer areaNo, @Param("trustStoreNo") Integer trustStoreNo, @Param("deliverytype")Boolean deliverytype);

    List<Map> selectClosingOrderPoiNotes(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    int update(Orders order);

    @RequiresDataPermission(originalField = "m.area_no")
    List<Map> selectTimingClosingOrderPoiNotes(@Param("startDate") Date timingStartDate, @Param("endDate") Date timingEndDate);

    Integer count(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,@Param("mId") Long mId);

    /**
     * 查询用户在固定时间内是否有被送达的订单
     * @param startTime
     * @param endTime
     * @param mId
     * @return
     */
    Integer countNum(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,@Param("mId") Long mId);


    int countTimingNotDelivery(@Param("mId") Long mId);

    List<Map<String, Integer>> orderData(@Param("phone") String phone, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime);

    List<Map<String, Integer>> orderMoneyData(@Param("phone") String phone, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime);

    @RequiresDataPermission(originalField = "d.areaNo")
    List<DeliveryOrdersVO> notDeliveryOrders(DeliveryOrdersVO deliveryOrdersVO);

    List<DeliveryOrdersVO> notDeliveryOrdersDetail(DeliveryOrdersVO deliveryOrdersVO);

    //List<DeliveryOrdersVO> skuSales(@Param("pdName") String pdName,@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,@Param("areaNo") Integer areaNo);

    /**
     * 统计下单门店数
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<DataVO> countOrderMerchant(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计下单金额
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<DataVO> countOrderMoney(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<SalePricesVO> selectSalePrice(@Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    BigDecimal selectUnPadiTotal(HashMap map);

    @RequiresDataPermission(originalField = "m.area_no")
    List<ClosingOrder>  getOrdersByBD( @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,@Param("adminIds") List<Integer> adminIds,@Param("bandAdminIds") List<Integer> bandAdminIds,@Param("areaNo") Integer areaNo);

    Double getGMVByBD( @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,@Param("adminId") Integer adminId,@Param("areaNo") Integer areaNo);

    BigDecimal selectTotalPriceByMonth(@Param("mId") Long mId, @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    List<OrderItemVO> selectMajorOrderItem(OrderVO selectKey);

//    @RequiresDataPermission(fieldAlias = "areaNo")
    GmvVO selectTotalGmv(@Param("areaNo") Integer areaNo, @Param("startTime")LocalDateTime startTime, @Param("endTime")LocalDateTime endTime,@Param("dataPermission") Set<Integer> dataPermission);

    Orders  selectLast(Long mId);

    //未冻结省心送数量
    Integer unLockQuantity(@Param("sku")String sku,@Param("areaNos") List<Integer> areaNos);
    //查询gmv
     /**
     * 修改订单归属
     * @param oldMid 原订单mid
     * @param newMid 新订单mid
     * @param accountId 账号id
     */
    int changeOrderMerchant(@Param("oldMid") Long oldMid, @Param("newMid") Long newMid, @Param("accountId") Long accountId);


    BigDecimal selectTotalPaymentGmv(@Param("areaNo") Integer areaNo, @Param("startTime")LocalDateTime startTime, @Param("endTime")LocalDateTime endTime,@Param("dataPermission") Set<Integer> dataPermission);



    /**
     * 查询仓下sku的销售数量
     * @param storeNo 仓库编号
     * @param startTime 开始时间
     * @param endTime 借宿时间
     * @return
     * ok 不用 处理
     */
    @Deprecated
    List<OrderItem> selectStoreSaleAmount(@Param("storeNo") Integer storeNo,@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<OrderItem> selectStoreSaleAmountNew(@Param("storeNo") Integer storeNo,@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
    * 更新订单是否已销售出库
    */
    int updateStockTask(@Param("orderNo") String orderNo, @Param("outStock") Integer outStock);

    List<String> selectSaleOutOrderNo (@Param("deliveryDate") LocalDate deliveryDate, @Param("areaNo") Integer areaNo, @Param("trustStoreNo") Integer trustStoreNo, @Param("deliverytype")Boolean deliverytype);

    List<String> selectSaleOutOrderNoForCross (@Param("deliveryDate") LocalDate deliveryDate, @Param("areaNo") Integer areaNo, @Param("trustStoreNo") Integer trustStoreNo, @Param("deliverytype")Boolean deliverytype);

    /**
    * 查询未完结的的省心送订单
    */

    List<Orders> selectTimingOrder(Integer storeNo);

    /**
     * 查询该sku在该仓是否有未完结的省心送订单
     * @param sku
     * @param storeNo
     * @return list
    */
    List<String> selectTimingOrders(@Param("sku")String sku ,@Param("storeNo") Integer storeNo);

    /**
     * 查询订单列表信息
     * @param selectKeys 查询条件
     * @return 列表信息
     */
    @RequiresDataPermission(originalField = "m.area_no")
    List<OrderVO> selectAllSituation(OrderReq selectKeys);

    GmvVO selectAdvance(@Param("areaNo") Integer areaNo, @Param("startTime")LocalDateTime startTime, @Param("endTime")LocalDateTime endTime,@Param("dataPermission") Set<Integer> dataPermission);

    BigDecimal selectAdvanceTotalPaymentGmv(@Param("areaNo") Integer areaNo, @Param("startTime")LocalDateTime startTime, @Param("endTime")LocalDateTime endTime,@Param("dataPermission") Set<Integer> dataPermission);

    /**
    * 捡货任务 大客户鲜果
    */
    List<StockTaskPick> selectFruitClosingOrder(@Param("deliveryDate") LocalDate deliveryDate, @Param("areaNo") Integer areaNo, @Param("trustStoreNo") Integer trustStoreNo, @Param("deliveryType")Boolean deliveryType,@Param("closeTime") String closeTime);

    @Deprecated
    List<StockTaskPick> selectNotFruitClosingOrder(@Param("deliveryDate") LocalDate deliveryDate, @Param("trustStoreNo") Integer trustStoreNo, @Param("deliveryType")Boolean deliveryType);

    /**
     * 近期下单sku
     * @param orderItemInput 查询条件
     * @return
     */
    List<RecentOrderSkuVO> selectRecentOrderSku(OrderItemInput orderItemInput);

    int countOrderByMId(Long mId);

    /**
     * 查询直发采购订单列表
     * @param selectKeys
     * @return
     */
    List<OrderVO> selectDirectPurchaseOrder(OrderVO selectKeys);

    /**
     * 根据订单号查询订单
     * @param orderNo
     * @return
     */
    Orders queryByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查询从目前为止，下单前7天未收货直发订单都查询出来
     * @param placeOrderDate
     * @return
     */
    List<Orders> selectDirectPurchaseOrderBeforeDate(@Param("placeOrderDate") LocalDate placeOrderDate);

    /**
     * 查询出某个大客户下的某个时间段内的订单总数
     * @param orderQuery
     * @return
     */
    List<OrderVO> selectOrdersByAdminId(OrderVO orderQuery);

    /**
     * 根据筛选条件得到可以展示的order列表, 该sql将订单对应的订单明细收敛至集合内,改动该sql请确认订单与明细之间关系仍为一对多
     * @param financialOrderQuery 查询条件
     * @return order列表
     */
    List<FinancialOrderVO> selectFinancialGroupByOrders(FinancialOrderQuery financialOrderQuery);

    /**
     * 根据筛选条件得到可以展示的order列表, 该sql将订单对应的订单明细收敛至集合内,改动该sql请确认订单与明细之间关系仍为一对多
     * @param financialOrderQuery 查询条件
     * @return order列表
     */
    List<FinancialOrderVO> selectFinancialOrders(FinancialOrderQuery financialOrderQuery);

    /**
     * 根据订单号获取订单及订单详情
     * @param orderNoList 订单号集合
     * @return
     */
    List<OrderAndItemDTO> selectOrderAndItemList(@Param("orderNoList") List<String> orderNoList);

    /**
     * 用于财务结算时后的订单展示
     * @param orderNos
     * @return
     */
    List<FinancialInvoiceOrderItemsVO> selectByOrders(@Param("orderNoList") List<String> orderNos);

    /**
    * 根据配送仓编号 sku 查询省心送未配送完成的订单数量
    */
    List<OrderItem> selectOrderByStoreNoAndSku(@Param("storeNo") Integer storeNo,@Param("sku") String sku,@Param("areaNo") Integer areaNo);

    /**
     * 取时间段下单的用户
     * @param sTime
     * @param eTime
     * @return
     */
    List<Long> selectPeriodMid(@Param("sTime") LocalDate sTime,@Param("eTime") LocalDate eTime);

    BigDecimal selectPeriodGmv(@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime,@Param("mId") Long mid);


    /**
     * 根据用户城市 省市区 配送时间获取订单信息 普通订单 代下单
     */
    List<ClosingOrder> selectOrderByMerchant(MerchantVO merchant);

    /**
     * 更新订单编号
     */
    int updateOrderAreaNo(@Param("orderNoList") List<String> orderNoList,@Param("areaNo") Integer areaNo);

    /**
     * 根据用户城市 省市区 配送时间获取订单信息 普通订单 省心送
     */
    List<String> selectClosingToTimingOrder(MerchantVO merchant);

    /**
     * 查询上次下单时间
     * @param mId 商户id
     * @return 商户上次下单时间
     */
    LocalDateTime selectLatestOrderTime(Long mId);

    /**
     * 获取指定商户指定时间内的有效订单时间集合
     * @param mId 商户id
     * @param startTime 指定开始时间
     * @return 指定商户指定时间内的有效订单时间集合
     */
    List<Date> selectOrderTimeByMId(@Param("mId") Long mId,@Param("startTime") LocalDateTime startTime);

    Long selectTotalQuantity(@Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime,
                                   @Param("areaNo") Integer areaNo,
                                   @Param("sku") String sku
                                   );
    BigDecimal selectTotalAmount(@Param("startTime") LocalDateTime startTime,
                                 @Param("endTime") LocalDateTime endTime,
                                 @Param("payTypes") List<String> payTypes,@Param("accountId") Integer accountId);

    /**
     * crm:跟进指定查询条件获取订单信息列表
     * @param crmOrderListQuery 查询条件:areaNo、mname(商户名称)、orderNo（订单号）、mSize(客户类型)、startTime、endTime
     *                      type:0普通、1省心送、4预售
     *                      status:1待支付、2待配送、3待收货、6已完结、11已撤销
     * @return 订单信息列表
     */
    @RequiresDataPermission(originalField = "m.area_no")
    List<OrderVO> selectForCrmOrderList(CrmOrderListQuery crmOrderListQuery);

    /**
     * 根据订单编号查询运费
     * @param orderNo
     * @return
     */
    BigDecimal selectDeliveryFee(String orderNo);

    /**
     * 查询账期门店订单信息
     * @param mId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderVO> selectOrdersByMId(@Param("mId") Long mId, @Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 查询单店代下单信息
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderVO> selectPlaceAnOrder(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 订单以及订单项数据
     * @param orderNoList
     * @return
     */
    List<OrderVO> selectWithItem(@Param("orderNoList") List<String> orderNoList);

    /**
     * 根据订单查询所属门店
     * @param orderNo
     * @return
     */
    Long selectMidByOrderNo(String orderNo);

    /**
     * 查询下单时间
     * @param orderNo
     * @return
     */
    LocalDateTime selectOrderTime(String orderNo);

    /**
     * 查询订单地址
     * @param orderNo
     * @return
     */
    OrderItemVO selectAddress(String orderNo);

    /**
     * 获取sku 冻结库存数量
     */
    Integer selectSaleLockQuantity(@Param("warehouseNo") Integer warehouseNo,@Param("sku") String sku ,@Param("startDate") LocalDate startDate);

    /**
     * 获取sku 冻结库存数量
     */
    Integer selectSaleLockQuantityByStoreNo(@Param("storeNo") Integer storeNo,@Param("sku") String sku ,@Param("startDate") LocalDate startDate);

    /**
     * 获取冻结订单
     * @param warehouseNo
     * @param sku
     * @param startDate
     * @return
     */
    List<OrderItemVO> selectSaleOrders(@Param("warehouseNo") Integer warehouseNo,@Param("sku") String sku ,@Param("startDate") LocalDate startDate);

    /**
     * 获取城配仓冻结订单
     * @param storeNo
     * @param sku
     * @param startDate
     * @return
     */
    List<OrderItemVO> selectSaleOrdersByStoreNo(@Param("storeNo") Integer storeNo,@Param("sku") String sku ,@Param("startDate") LocalDate startDate);

    /**
     * 获取商户的下单sku个数
     * @param mId 商户id
     * @param startTime 订单开始时间
     * @param endTime 订单结束时间
     * @return 下单sku个数
     */
    int selectSkuNumByMid(@Param("mId") Integer mId,@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);

    /**
     * 查询具有客户大单的订单
     * @param orderNo 订单编号
     * @return 客户大单的订单详情
     */
    List<OrderItemVO> selectOrderReminder(@Param("orderNo") String orderNo);

    /**
     * 取消订单中的开票
     * @param orderNo
     * @return
     */
    int updateOrdersFinancial(String orderNo);

    /**
     * 根据订单号获取截单时间等订单相关信息
     * @param orderNos
     * @param noHeartDeliveryTime
     * @param deliveryPlanId
     * @return
     */
    List<OrderInfoVo> getOrderInfoByOrderNos(@Param("orderNos") List<String> orderNos,
                                             @Param("noHeartDeliveryTime") LocalDate noHeartDeliveryTime,
                                             @Param("deliveryPlanId") Integer deliveryPlanId);

    /**
     * 批量查询省心送订单信息
     * @param orderNos 订单编号集合
     * @return 省心送订单信息集合
     */
    List<TimingOrderVO> selectTimingOrderInfos(@Param(value = "orderNos") Set<String> orderNos);

    void updateOrderStatus(List<String> orderNos, Integer status);

    List<ClosingOrder> selectSassClosingOrder(@Param("deliveryTime")LocalDate deliveryTime,@Param("parentNo") Integer storeNo, @Param("areaNo") Integer areaNo);

    /**
     * 查询Saas截单数据
     * @param deliveryTime 配送日期
     * @param storeNo 城配仓编号
     * @return 结果
     */
    List<ClosingOrder> selectTmsSassClosingOrder(@Param("deliveryTime")Date deliveryTime,@Param("storeNo") Integer storeNo);

    List<ClosingOrder> selectSassClosingOrderByOrder(@Param("orderNo")String orderNo);

    List<String> selectSassOtherOrder(String orderNo);

    /**
     * 查询GMV
     * @param areaNos
     * @param nowStartTime
     * @param now
     * @param dataPermissionsForFilter
     * @return
     */
    BigDecimal queryGmv(@Param("areaNos") String areaNos, @Param("startTime") LocalDateTime nowStartTime, @Param("endTime") LocalDateTime now, @Param("dataPermission") Set<Integer> dataPermissionsForFilter);

    /**
     * 查询实付GMV
     * @param areaNos
     * @param nowStartTime
     * @param now
     * @param dataPermissionsForFilter
     * @return
     */
    BigDecimal queryPaymentGmv(@Param("areaNos") String areaNos, @Param("startTime") LocalDateTime nowStartTime, @Param("endTime") LocalDateTime now, @Param("dataPermission") Set<Integer> dataPermissionsForFilter);


    /**
     * 获取某个时间段内订单数据
     * @param teamDataInput 查询条件
     * @return 商户某个时间段内订单数据
     */
    List<OrderDataVO> selectOrderData(TeamDataInput teamDataInput);


    List<ClosingOrder> selectClosingOrderByMid( @Param("startDate")Date startDate, @Param("endDate") Date endDate, @Param("mId") Integer mId, @Param("deliverytype")Boolean deliverytype);

    /**
     * 查询优惠券金额
     * @param orderNo
     * @return
     */
    BigDecimal selectCouponDeliveryFeeByOrderNo(String orderNo);

    /**
     * 根据订单号查有效订单详情
     * @param orderNo
     * @return
     */
    OrderVO selectOrderByOrderNo(String orderNo);

    /**
     * 查看是否是水果类的item
     * @param orderItemId
     * @return
     */
    FruitSales selectIsFruitByItemId(Long orderItemId);

    /**
     * 根据MId查询
     * @param MId
     * @param now
     * @param beforeTime
     * @return
     */
    String selectMoneyByMId(Long MId, LocalDateTime now, LocalDateTime beforeTime);

    List<OrderVO> selectOrderStatusLimit2(Long MId);

    /**
     * 根据订单号集合查询订单
     * @param orderNoList
     * @return
     */
    List<Orders> queryByOrderNoList(@Param("orderNoList") List<String> orderNoList);

    /**
     * 根据订单号查询订单
     * @param orderNo
     * @return
     */
    Orders selectByOrderNo(String orderNo);

    /**
     * 按订单编号选择订单
     *
     * @param orderNos 订单号
     * @return {@link List}<{@link Orders}>
     */
    List<FinanceOrderDTO> selectOrdersByOrderNos(@Param("orderNos")List<String> orderNos);

    /**
     * 查询tms的订单数据信息
     * @param deliveryTime 配送时间
     * @param storeNo 城配仓编号
     * @return
     */
    List<ClosingOrder> selectTmsClosingOrder(@Param("deliveryTime") Date deliveryTime, @Param("storeNo") Integer storeNo);

    /**
     * 根据订单号查询contactId和配送日期
     *
     * @param orderNos
     * @return {@link List}<{@link DistOrderBatchQueryStandardReq}>
     */
    List<BillOrderDeliveryInfoDTO> selectDeliveryInfo(@Param("orderNos") List<String> orderNos);

    /**
     * 导出订单明细
     *
     * @param orderNo 订单号
     * @return {@link List}<{@link FinancePeriodOrderItemExportVO}>
     */
    List<AdminOrderExportVo> selectExportOrder(@Param("orderNo") String orderNo);

    /**
     * 查询大客户订单
     *
     * @param selectKey 查询条件
     * @return {@link List}<{@link String}>
     */
    List<String> selectMajorOrderNos(OrderVO selectKey);

    /**
     * 统计大客户订单
     *
     * @param selectKey 选择关键
     * @return int
     */
    int countMajorOrderNos(OrderVO selectKey);

    /**
     * 统计返点订单个数
     *
     * @param orderNos 订单
     * @return {@link Integer}
     */
    int countRebateOrderByOrderNos(@Param("orderNos") List<String> orderNos);

    /**
     * 查询省心送订单退款时间
     *
     * @return {@link LocalDate}
     */
    LocalDate selectTimingRefundOrder(@Param("orderNo") String orderNo);

    /**
     * 根据订单号查询订单强制走主库
     * @param orderNo
     * @return
     */
    Orders selectByOrderNoForMaster(String orderNo);

    /**
     * 批量查询省心送订单信息
     * @param orderNos 订单编号集合
     * @return 省心送订单信息集合
     */
    List<TimingOrderVO> selectTimingOrderInfosNew(@Param(value = "orderNos") Set<String> orderNos);

    /**
     * 查询TMS外单
     * @param deliveryTime 配送日期
     * @param storeNo 城配仓
     * @return 结果
     */
    List<ClosingOrder> selectTmsOuterClosingOrder(@Param("deliveryTime")Date deliveryTime, @Param("storeNo") Integer storeNo);


    /**
     * 核验通过更新订单客户标记（用于支持售后）
     */
    int updateOrderSizeForAuditSuccess(@Param("mid")Long mid, @Param("adminId")Integer adminId);

    /**
     * 根据订单号查询销售主体
     * @param orderNos
     * @return
     */
    List<String> querySellingEntityByNos(@Param("orderNos") Collection<String> orderNos);
}
