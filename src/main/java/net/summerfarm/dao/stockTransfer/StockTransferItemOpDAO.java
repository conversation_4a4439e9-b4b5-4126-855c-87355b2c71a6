package net.summerfarm.dao.stockTransfer;

import net.summerfarm.dao.stockTransfer.dataobject.StockTransferItemOpDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StockTransferItemOpDAO {
    int insert(StockTransferItemOpDO stockTransferItemOpDO);

    void batchInsert(@Param("items") List<StockTransferItemOpDO> stockTransferItemOpDos);

    /**
     * 根据itemId查询
     */
    List<StockTransferItemOpDO> listByItemId(@Param("itemIds") List<Long> itemId);

    /**
     * 主键查询
     */
    StockTransferItemOpDO selectById(@Param("id") Long id);

    /**
     * 刷数据用
     */
    void delete(@Param("itemId") Long itemId);

    /**
     * 数据初始化使用
     */
    List<StockTransferItemOpDO> selectByType();
}
