package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.OuterPlatform;
import net.summerfarm.model.vo.OuterPlatformVo;
import java.util.List;
/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 外部对接-外部平台
 * @createTime 2021年10月19日 15:22:00
 */
public interface OuterPlatformMapper {
    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 保存数据
     * @param record
     * @return
     */
    int insert(OuterPlatform record);

    /**
     * 保存数据
     * @param record
     * @return
     */
    int insertSelective(OuterPlatform record);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    OuterPlatform selectByPrimaryKey(Long id);

    /**
     * 更新数据
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OuterPlatform record);

    /**
     * 更新数据
     * @param record
     * @return
     */
    int updateByPrimaryKey(OuterPlatform record);

    /**
     * 查询外部对接平台
     * @return
     */
    List<OuterPlatformVo> selectOuterPlatform();

    /**
     * 根据外部平台id查询
     * @param outerPlatformId
     * @return
     */
    OuterPlatformVo selectOuterPlatformById(Integer outerPlatformId);

    /**
     * 根据外部平台名称查询
     * @param outerPlatformName
     * @return
     */
    OuterPlatformVo selectOuterPlatformByName(String outerPlatformName);

    /**
     * 根据token查询
     * @param token
     * @return
     */
    OuterPlatformVo selectOuterPlatformByToken(String token);
}