package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Carrier;
import net.summerfarm.model.input.CarrierInput;
import net.summerfarm.model.vo.CarrierVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 承运商Mapper
 */
public interface CarrierMapper {
    /**
     * 按照id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(Carrier record);
    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(Carrier record);

    /**
     * 按主键查询
     * @param id
     * @return
     */
    Carrier selectByPrimaryKey(Long id);


    /**
     * 根据id查询承运商信息
     * @param id
     * @return
     */
    CarrierVo selectCarrierInvoice(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(Carrier record);
    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(Carrier record);

    /**
     * 按照条件查看承运商集合
     * @param carrierInput
     * @return
     */
    List<Carrier> selectByCondition(CarrierInput carrierInput);


    /**
     * 根据名称判断是否有多个
     * @param carrierName
     * @return
     */
    int selectByName(@Param("carrierName") String carrierName);

    /**
     * 取出所有得承运商
     * @return
     */
    List<Carrier> selectAll(@Param("carrierName") String carrierName);

    List<CarrierVo> selectAllInvoice();

    /**
     * 根据名称模糊查询承运商
     * @return 承运商列表
     */
    List<CarrierVo> selectByCarrierName(@Param("carrierName") String carrierName);

    /**
     * 根据name查询承运商
     * @param carrierName
     * @return
     */
    CarrierVo queryByName(@Param("carrierName") String carrierName);

    /**
     * 根据name查询承运商
     *
     * @param merchantName
     * @param taxNumber
     * @return
     */
    CarrierVo selectByTaxNumber(@Param("carrierName") String merchantName, @Param("taxNumber") String taxNumber);

    /**
     * id 查询
     * @param idList
     * @return
     */
    List<Carrier> selectByIdList(@Param("idList") List<Long> idList);

}