package net.summerfarm.task.quartz;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.enums.JobGroupType;
import net.summerfarm.model.domain.ScheduleJob;
import net.summerfarm.service.*;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Created by wjd on 2017/9/26.
 */
@Component
@DisallowConcurrentExecution
public class QuartzJobFactory implements Job {
    @Resource
    private ActivityService activityService;
    @Resource
    private PriceAdjustService priceAdjustService;
    @Resource
    private OnSaleAdjustmentService onSaleAdjustmentService;
    @Resource
    private AreaService areaService;
    @Resource
    private ScheduleTaskService scheduleTaskService;
    @Resource
    private MajorPriceService majorPriceService;
    @Resource
    private OrderOuterInfoService orderOuterInfoService;

    private static final Logger logger = LoggerFactory.getLogger(QuartzJobFactory.class);

    @Override
    public void execute(JobExecutionContext context) {
        try {
            ScheduleJob scheduleJob = (ScheduleJob) context.getMergedJobDataMap().get("scheduleJob");
            logger.info(scheduleJob.getJobGroup() + ":" + scheduleJob.getJobName() + "job start");
            switch (scheduleJob.getJobGroup()) {
                case JobGroupType.ACTIVITY_SERVICE:
//                    activityService.exe(scheduleJob);
                    break;
//                case JobGroupType.PRICE_ADJUST:
//                    priceAdjustService.executePriceAdjust(Integer.valueOf(scheduleJob.getJobName()));
//                    break;
                case JobGroupType.ON_SALE_ADJUST:
                    onSaleAdjustmentService.execute(Integer.valueOf(scheduleJob.getJobName()));
                    break;
                case JobGroupType.CHANGE_STORE:
//                    String jobName = scheduleJob.getJobName();
//                    String[] args = jobName.split("_");
//                    areaService.runTask(Integer.parseInt(args[0]), Integer.parseInt(args[1]));
                    break;
                case JobGroupType.COMMON_TASK:
                    scheduleTaskService.execute(Integer.valueOf(scheduleJob.getJobName()));
                    break;
//                case JobGroupType.COST_INVERSION:
//                    majorPriceService.executeCostInversion(Integer.valueOf(scheduleJob.getJobName()));
//                    break;
//                case JobGroupType.MAJOR_PRICE_VALID_INVALID_TIME:
//                    String majorPriceJobName = scheduleJob.getJobName();
//                    String[] majorPriceData = majorPriceJobName.split("_");
//                    orderOuterInfoService.executeMajorPriceValidInvalidTime(Integer.parseInt(majorPriceData[0]),Integer.parseInt(majorPriceData[1]),majorPriceData[2]);
//                    break;
                case JobGroupType.REPUSH_DELIVERY_NOTICE:
                    String deliveryJobName = scheduleJob.getJobName();
                    String[] deliveryData = deliveryJobName.split("_");
                    JSONObject deliveryJson = new JSONObject();
                    deliveryJson.put("orderNo", deliveryData[0]);
                    deliveryJson.put("pushTimes", deliveryData[1]);
                    String deliveryContent = deliveryJson.toJSONString();
                    orderOuterInfoService.pushDeliveryNotice(deliveryContent);
                    break;
                case JobGroupType.REPUSH_RECEIPT_NOTICE:
                    String receiptJobName = scheduleJob.getJobName();
                    String[] receiptData = receiptJobName.split("_");
                    JSONObject receiptJson = new JSONObject();
                    receiptJson.put("orderItemList", receiptData[0]);
                    receiptJson.put("shortSkuList", receiptData[1]);
                    receiptJson.put("pushTimes", receiptData[2]);
                    String receiptContent = receiptJson.toJSONString();
                    orderOuterInfoService.pushReceiptNotice(receiptContent);
                    break;
                default: break;
            }
            logger.info(scheduleJob.getJobGroup() + ":" + scheduleJob.getJobName() + "job end");
        } catch (Exception e) {
            logger.error("调度任务执行异常：{}", e.getMessage(), e);
        }
    }


}
