package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DatabaseHandleRecord;
import org.apache.ibatis.annotations.Param;

public interface DatabaseHandleRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(DatabaseHandleRecord record);

    int insertSelective(DatabaseHandleRecord record);

    DatabaseHandleRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DatabaseHandleRecord record);

    int updateByPrimaryKey(DatabaseHandleRecord record);

    void executeSql(@Param("sql") String sql);
}