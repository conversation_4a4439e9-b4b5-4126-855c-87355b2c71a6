package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.ProductsProperty;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

public interface ProductsPropertyMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ProductsProperty record);

    int insertSelective(ProductsProperty record);

    ProductsProperty selectByPrimaryKey(Integer id);

    ProductsProperty selectByName(String nme);

    int updateByPrimaryKeySelective(ProductsProperty record);

    int updateByPrimaryKey(ProductsProperty record);

    List<ProductsProperty> selectByType(@Param("type") Integer type);

    /**
     * 查询唯一信息
     * @param record
     * @return
     */
    ProductsProperty selectProductsProperty(ProductsProperty record);
}