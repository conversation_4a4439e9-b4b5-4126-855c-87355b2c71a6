package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.StockTaskProcess;
import net.summerfarm.model.vo.StockTaskProcessVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Repository
public interface StockTaskProcessMapper {
    int insert(StockTaskProcess stockTaskProcess);

    /**
     * 直发采购专有逻辑
     * 不适用其他
     * @param taskId
     * @param updateTime
     */
    void updateTimeByTaskId(@Param("taskId") Long taskId, @Param("updateTime") Date updateTime);

    @RequiresDataPermission(originalField = "st.area_no")
    List<StockTaskProcessVO> selectStockTaskProcessVO(@Param("list") List<Integer> types, @Param("type") Integer type, @Param("areaNo") Integer areaNo, @Param("stockTaskId") Integer stockTaskId,
                                                      @Param("pdName") String pdName, @Param("dimension") Integer dimension, @Param("supplierId") Integer supplierId, @Param("sku") String sku,
                                                      @Param("tenantId") Long tenantId, @Param("startAddTime") LocalDateTime startAddTime, @Param("endAddTime") LocalDateTime endAddTime);

    StockTaskProcessVO selectByPrimaryKey(Integer id);

    List<StockTaskProcessVO> selectStockTaskProcessVO(List<Integer> types, Integer type, Integer areaNo, Integer stockTaskId, String pdName, Integer dimension);

    /**
     * 根据任务编号查询入库单
     * @param listNo
     * @return
     */
    List<StockTaskProcess> selectByTaskNo(String listNo);

    /**
     * 根据任务编号查询入库单
     * @param taskId
     * @return
     */
    Long selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务编号查询入库单
     * @param stockTaskId
     * @return
     */
    List<StockTaskProcess> selectByTaskIdNew(Integer stockTaskId);

    StockTaskProcess selectByInboundOrderId(Long inboundId);
}
