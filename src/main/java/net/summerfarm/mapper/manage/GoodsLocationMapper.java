package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.GoodsLocation;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/3/23  16:41
 */
@Repository
public interface GoodsLocationMapper {

    /**
     * 插入
     * @param goodsLocation
     * @return
    */
    int insertGoodsLocation(GoodsLocation goodsLocation);

    /**
    * 查询货位信息
     * @param storeNo
     * @return
    */

    List<GoodsLocation> selectGoodsLocations(Integer storeNo);

    /**
     * 查询货位
     * @param storeNo
     * @param glNo
     * @return
    */
    GoodsLocation selectByGlNo(Integer storeNo, String glNo);

    /**
     * 查询温区暂存位货位信息
     * @param storeNo
     * @return
     */

    GoodsLocation selectByTemperature(@Param("storeNo") Integer storeNo, @Param("temperature") Integer temperature);
}
