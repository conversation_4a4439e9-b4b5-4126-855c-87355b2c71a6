package net.summerfarm.mapper.offline;


import net.summerfarm.model.DTO.FinanceCostAccountDTO;
import net.summerfarm.model.domain.FinanceCostAccountingDetails;
import net.summerfarm.model.input.CostAccountQuery;
import net.summerfarm.model.input.IndustryAndWealthInput;
import net.summerfarm.model.vo.FinanceCostAccountingDetailsVO;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface FinanceCostAccountingDetailsMapper {

    /**
     * 查询成本信息
     * @param id id
     * @return 成本信息
     */
    FinanceCostAccountingDetails selectByPrimaryKey(Long id);

    /**
     * 查询一段时间内各个类型的总成本信息
     * @param industryAndWealthInput 查询条件
     * @return 总成本信息
     */
    List<FinanceCostAccountDTO> selectAllByTime(IndustryAndWealthInput industryAndWealthInput);

    /**
     * 根据时间及类型批量查询成本变动记录
     * @param costAccountQuery 查询条件
     * @return 成本变动记录
     */
    List<FinanceCostAccountingDetails> queryCostAccountingDetails(CostAccountQuery costAccountQuery);
}




