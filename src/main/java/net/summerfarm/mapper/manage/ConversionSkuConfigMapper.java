package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.domain.ConversionSkuConfig;
import net.summerfarm.model.vo.ConversionSkuConfigVO;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> ct
 * create at:  2021/10/18  11:11
 */
@Repository
public interface ConversionSkuConfigMapper {

    /**
     * 插入sku转换配置
     * @Author: ct
     * @param conversionSkuConfig  sku转换配置信息
     * @return
     **/
     int saveConfig(ConversionSkuConfig conversionSkuConfig);

    /**
     *  批量插入
     * @Author: ct
     * @param list  sku转换配置信息列表
     * @return
     **/
    void batchSaveConfig(List<ConversionSkuConfigVO> list);

    /**
     * 列表查询
     * @Author: ct
     * @param vo sku转换配置信息
     * @return
     **/
    List<ConversionSkuConfigVO> selectListConfigVO(ConversionSkuConfigVO vo);

    /**
     * 更新sku转换配置
     * @Author: ct
     * @param conversionSkuConfig sku转换配置信息
     * @return
     **/
    int updateSaveConfig(ConversionSkuConfig conversionSkuConfig);

    /**
     * 查询信息
     * @Author: ct
     * @param config 信息配置
     * @return
     **/
    List<ConversionSkuConfig> selectConfig(ConversionSkuConfig config);

    /**
     * 查询详情
     * @Author: ct
     * @param id
     * @return
     **/
    ConversionSkuConfig selectConfigDetail(Integer id);

}
