package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockAllocationItemQualityPeriod;

import java.util.List;

public interface StockAllocationItemQualityPeriodMapper {
    int insert(StockAllocationItemQualityPeriod record);

    int insertSelective(StockAllocationItemQualityPeriod record);

    StockAllocationItemQualityPeriod selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StockAllocationItemQualityPeriod record);

    int updateByPrimaryKey(StockAllocationItemQualityPeriod record);

    List<StockAllocationItemQualityPeriod> queryAllByStockAllocationItemId(List<Integer> list);
}