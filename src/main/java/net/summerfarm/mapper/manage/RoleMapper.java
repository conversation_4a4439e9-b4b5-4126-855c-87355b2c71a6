package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Role;
import net.summerfarm.model.vo.RoleVO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface RoleMapper {


    int insertSelective(Role record);

    Role selectByPrimaryKey(Integer roleId);

    /**
     * 批量查询角色
     * @param roleVOIds
     * @return
     */
    List<Role> selectRole(Set roleVOIds);

    /**
     * 查询角色信息及拥有的权限
     * @param roleId
     * @return
     */
    RoleVO selectPurviews(Integer roleId);

    List<Role> select(Role selectKeys);


    int count(String realname);

    /**
     * 根据id修改角色名称和备注
     * @param record
     * @return
     */
    int update(Role record);
}