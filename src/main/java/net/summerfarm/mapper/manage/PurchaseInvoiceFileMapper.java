package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PurchaseInvoiceFile;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface PurchaseInvoiceFileMapper {

    /**
     * 查询是否有附件
     * @param id
     * @return
     */
    int selectFile(Integer id);

    int deleteByPrimaryKey(Integer id);

    /**
     * 插入采购发票地址信息
     * @param record
     * @return
     */
    int insert(PurchaseInvoiceFile record);

    /**
     * 删除文件地址
     * @param updater
     * @param id
     * @return
     */
    int delete(String updater, Integer id);

    int insertSelective(PurchaseInvoiceFile record);

    PurchaseInvoiceFile selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PurchaseInvoiceFile record);

    int updateByPrimaryKey(PurchaseInvoiceFile record);
}