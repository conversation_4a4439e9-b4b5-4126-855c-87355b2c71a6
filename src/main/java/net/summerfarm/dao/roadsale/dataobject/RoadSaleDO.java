package net.summerfarm.dao.roadsale.dataobject;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ma
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RoadSaleDO implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 在途可售库存
     */
    private Integer roadSaleQuantity;

    /**
     * 剩余在途可售库存
     */
    private Integer remainingRoadSaleQuantity;

    /**
     * 添加时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

    private static final long serialVersionUID = 1L;
}