package net.summerfarm.mapper.manage;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import net.summerfarm.model.domain.LandPageRelation;
import org.apache.ibatis.annotations.Param;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

public interface LandPageRelationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LandPageRelation record);

    int insertSelective(LandPageRelation record);

    LandPageRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LandPageRelation record);

    int updateByPrimaryKey(LandPageRelation record);

    int updateByKeys(LandPageRelation update);

    int updateByItemSonId(LandPageRelation landPageRelation);
}