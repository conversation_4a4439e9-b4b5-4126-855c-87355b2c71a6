package net.summerfarm.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.SpringContextUtil;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.mapper.manage.ActivityBasicInfoMapper;
import net.summerfarm.mq.AutoChangePriceV2Listener;
import net.summerfarm.service.ActivityNewService;
import net.summerfarm.service.MajorPriceService;
import net.xianmu.redis.support.util.XmRedisUtils;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;

/**
 * 活动过期处理
 *
 * @author: <EMAIL>
 * @create: 2023/2/1
 */
@Slf4j
@Component
public class ActivityPriceExpireProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private MajorPriceService majorPriceService;
    @Resource
    private ActivityNewService activityNewService;
    @Resource
    private ActivityBasicInfoMapper activityBasicInfoMapper;
    @Resource
    private XmRedisUtils xmRedisUtils;
    @Resource
    private DynamicConfig dynamicConfig;
    @Resource(name = "fastExecutor")
    private Executor fastExecutor;

    private static final String VALID_ACTIVITY_ID_LIST_KEY = "MANAGE:VALID_ACTIVITY_ID_LIST";
    private static final String NOT_EFFECTIVE_ACTIVITY_ID_LIST_KEY = "MANAGE:NOT_EFFECTIVE_ACTIVITY_ID_LIST";

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        try {
            log.info("[活动生效/过期后触发低价监控]任务开始执行, context: 【{}】", JSON.toJSONString(context));
            long start = System.currentTimeMillis();
            // 1.处理  生效--->失效
            // 查询当前生效的活动id
            List<Long> basicIdList = activityBasicInfoMapper.listValidByNowTime();
            log.info("当前周期生效的活动：basicIdList：{}", JSON.toJSONString(basicIdList));
            // 对比上个周期查到的
            Object o = xmRedisUtils.get(VALID_ACTIVITY_ID_LIST_KEY);
            log.info("上周期生效的活动：oldBasicIdList：{}", JSON.toJSONString(o));
            List<Long> oldBasicIdList = JSON.parseArray(JSON.toJSONString(o), Long.class);
            // 处理失效的
            List<Long> invalidBasicId = new ArrayList<>();
            if (CollUtil.isNotEmpty(oldBasicIdList)) {
                invalidBasicId.addAll(oldBasicIdList);
                invalidBasicId.removeAll(basicIdList);
            }

            // 2.处理  未生效--->生效
            // 处理生效的
            List<Long> validBasicId = new ArrayList<>();
            if(CollUtil.isNotEmpty(basicIdList)) {
                validBasicId.addAll(basicIdList);
                if(CollUtil.isNotEmpty(oldBasicIdList)) {
                    validBasicId.removeAll(oldBasicIdList);
                }
            }

            // 3.先更新当前生效的活动
            xmRedisUtils.set(VALID_ACTIVITY_ID_LIST_KEY, basicIdList, 60 * 60 * 24 * 3);

            // 4.最后集中处理
            this.handle(invalidBasicId, validBasicId);

            log.info("[活动生效/过期后触发低价监控]执行完成!!耗时：{}ms", System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("[活动生效/过期后触发低价监控]执行失败!", e);
        }
        return new ProcessResult(true);
    }


    private void handle(List<Long> invalidBasicId, List<Long> validBasicId) {
        if(!dynamicConfig.getNewLowPriceSwitch()) {
            log.info("开关关闭,活动过期生效不触发大客户低价监控");
            return;
        }
        log.info("开始处理失效/生效的活动, invalidBasicId:{}, validBasicId:{}" , JSON.toJSONString(invalidBasicId), JSON.toJSONString(validBasicId));
        if (CollUtil.isEmpty(invalidBasicId) && CollUtil.isEmpty(validBasicId)) {
            log.info("暂无活动需要处理");
            return;
        }
        List<Long> pendingIdList = new ArrayList<>();
        pendingIdList.addAll(invalidBasicId);
        pendingIdList.addAll(validBasicId);

        Set<String> hashSet = new HashSet<>();
        // 查询所有需要处理的 areaNo+sku
        // 城市维度
        List<String> areaNoSkuDetail = activityBasicInfoMapper.listAreaNoByBasicId(pendingIdList);
        // 大区维度
        List<String> largeAreaNoSkuDetail = activityBasicInfoMapper.listLargeAreaNoByBasicId(pendingIdList);
        hashSet.addAll(areaNoSkuDetail);
        hashSet.addAll(largeAreaNoSkuDetail);

        if(CollUtil.isEmpty(hashSet)) {
            log.info("暂无sku-城市需要处理");
            return;
        }

        ActivityPriceExpireProcessor bean = SpringContextUtil.getBean("activityPriceExpireProcessor", this.getClass());
        // todo 数据量过大的时候开多线程处理？
        Integer threadNum = dynamicConfig.getLowPriceTaskThreadNum();
        int taskSize = hashSet.size() % threadNum == 0 ? hashSet.size() / threadNum : hashSet.size() / threadNum + 1;
        List<List<String>> split = CollUtil.split(hashSet, taskSize);
        split.forEach(skuAreaNos -> fastExecutor.execute(() -> batchLowPriceWhenPriceChange(skuAreaNos, bean)));
    }

    private void batchLowPriceWhenPriceChange(List<String> skuAreaNos, ActivityPriceExpireProcessor bean) {
        skuAreaNos.forEach(bean::lowPriceWhenPriceChange);
    }


    @Transactional(rollbackFor = Exception.class)
    public void lowPriceWhenPriceChange(String skuAreaNo) {
        try {
            if (StringUtils.isBlank(skuAreaNo)) {
                log.warn("sku + areaNo 数据异常.data:{}", skuAreaNo);
                return;
            }
            String[] split = skuAreaNo.split("-");
            if (split.length != 2 || split[0] == null || split[1] == null) {
                log.warn("sku + areaNo 数据异常.data:{}", skuAreaNo);
                return;
            }
            String sku = split[0];
            Integer areaNo = Integer.valueOf(split[1]);
            //低价监控
            majorPriceService.lowPriceWhenPriceChange(areaNo, sku);
        } catch (Exception e) {
            log.warn("低价监控异常，skuAreaNo:{}", skuAreaNo, e);
        }
    }
}
