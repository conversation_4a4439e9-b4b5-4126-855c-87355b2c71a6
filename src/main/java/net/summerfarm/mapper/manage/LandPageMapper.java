package net.summerfarm.mapper.manage;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import net.summerfarm.model.domain.LandPage;
import net.summerfarm.model.vo.LandPageVO;
import org.apache.ibatis.annotations.Param;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

public interface LandPageMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LandPage record);

    int insertSelective(LandPage record);

    LandPage selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LandPage record);

    int updateByPrimaryKey(LandPage record);

    /**
     * 获取落地页列表(未删除的）
     * @param selectKey
     * @return
     */
    List<LandPageVO> selectByKeys(LandPageVO selectKey);

    /**
     * 根据落地页名称来查询
     * @param landPageName
     * @return
     */
    List<LandPage> queryByName(@Param("landPageName") String landPageName);

    /**
     * 根据落地页id获取
     * @param landPageId
     * @return
     */
    LandPageVO selectById(@Param("landPageId") Long landPageId);
}