package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AreaSku;
import net.summerfarm.model.domain.OnSaleAdjustment;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OnSaleAdjustmentMapper {

    int insert(OnSaleAdjustment insert);

    List<OnSaleAdjustment> selectList(OnSaleAdjustment select);

    OnSaleAdjustment selectByPrimaryKey(Integer id);

    int update(OnSaleAdjustment update);

    /**
     * 根据状态和执行时间查询任务列表
     * @param onSale 类型：0-下架，1-下架
     * @param status 状态：0-待执行，1-已执行，2-已取消
     * @param maxUpTime 生效时间
     * @return 任务列表「含上下架状态」
     */
    List<OnSaleAdjustment> selectListByStatusAndUpTime(@Param("onSale") Integer onSale, @Param("status") Integer status, @Param("maxUpTime") LocalDateTime maxUpTime);
}
