package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.FinanceCashSettlement;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface FinanceCashSettlementMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinanceCashSettlement record);

    int insertSelective(FinanceCashSettlement record);

    /**
     * 根据时间的单单据数据
     * @param startTime
     * @return
     */
    List<FinanceCashSettlement> selectByDate(@Param("startTime") LocalDate startTime);

    /**
     * 查询指定日期现结资金情况
     * @param startTime
     * @return
     */
    List<FinanceCashSettlement> selectByAll(@Param("startTime") LocalDate startTime);

    int updateByPrimaryKeySelective(FinanceCashSettlement record);

    int updateByPrimaryKey(FinanceCashSettlement record);
}