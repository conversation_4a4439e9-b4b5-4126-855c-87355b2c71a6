package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.CrmBdBigCustMonthGmv;
import org.apache.ibatis.annotations.Param;

public interface CrmBdBigCustMonthGmvMapper {

    /**
     * 查询bd的大客户业绩
     * @param adminId bdId
     * @param monthTah 时间标记
     * @return 大客户业绩
     */
    CrmBdBigCustMonthGmv selectByAdminId(@Param("adminId") Integer adminId,@Param("monthTah") Integer monthTah);

}