package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.ContactAdjust;
import net.summerfarm.model.vo.ContactAdjustVO;
import net.summerfarm.model.vo.MerchantVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/6/28  10:41
 */
@Repository
public interface ContactAdjustMapper {

    /**
     * 查询
     * @param
     * @return
    */
    List<MerchantVO> selectContactAdjustList(MerchantVO merchantVO);


    /**
    * 查询详情
    */
    ContactAdjustVO selectContactAdjustDetail(Integer contactAdjustId);


    /**
    * 更新信息
    */
    int updateContactAdjustStatus(ContactAdjust contactAdjust);


    int updateStatus();

}
