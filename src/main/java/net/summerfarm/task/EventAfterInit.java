package net.summerfarm.task;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import java.util.concurrent.TimeUnit;
import net.summerfarm.common.redis.*;
import net.summerfarm.common.util.Prompt;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.StoreRecordType;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.AreaStoreMapper;
import net.summerfarm.model.domain.Area;
import net.summerfarm.model.domain.AreaStore;
import net.summerfarm.service.*;
import net.summerfarm.warehouse.enums.WarehouseLogisticsCenterStatus;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.model.vo.WarehouseLogisticsCenterVO;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import org.apache.dubbo.common.utils.NamedThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executors;

/**
 * spring事件监听器
 */
@Component
public class EventAfterInit implements ApplicationListener<ContextRefreshedEvent> {

    protected final Logger logger = LoggerFactory.getLogger(EventAfterInit.class);

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private ScheduleTaskService scheduleTaskService;

    @Resource
    private MerchantLifecycleService merchantLifecycleService;

    @Resource
    private RedisTemplate<String,String> redisTemplate;

    @Resource(name = "delKeyScript")
    private RedisScript<Boolean> delKeyScript;

    @Resource(name = "delKeysScript")
    private RedisScript<Boolean> delKeysScript;

    @Resource
    private WarehouseStorageService warehouseStorageService;

    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;

    private static final Integer[] IN_OUT_TYPES = {
            StoreRecordType.STORE_ALLOCATION_IN.getId(), StoreRecordType.PURCHASE_IN.getId(), StoreRecordType.AFTER_SALE_IN.getId(),
            StoreRecordType.RETURN_IN.getId(), StoreRecordType.STORE_ALLOCATION_NOT_IN.getId(), StoreRecordType.TRANSFER_IN.getId(),
            StoreRecordType.STORE_ALLOCATION_OUT.getId(), StoreRecordType.SALE_OUT.getId(), StoreRecordType.DEMO_OUT.getId(),
            StoreRecordType.DAMAGE_OUT.getId(), StoreRecordType.TRANSFER_OUT.getId(), StoreRecordType.PURCHASES_BACK.getId(),
            StoreRecordType.AFTER_SALE_IN_NEW.getId()
    };

    // 省心送冻结运行线程池
    private static final ExecutorService EXECUTOR_SERVICE = new ThreadPoolExecutor(1, 1, 0, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(1),
        new NamedThreadFactory("timing-order-locking-thread"), new CallerRunsPolicy());

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if(event.getApplicationContext().getParent() != null){
            logger.info("防止重复执行");
            return;
        }

        //商城调用api地址初始化
        Global.apiInit();
        logger.info("--------------使用的域名为：{}", Global.DOMAIN_NAME);
        logger.info("--------------使用的域名为：{}", Global.DOMAIN_NAME);
        logger.info("--------------使用的域名为：{}", Global.DOMAIN_NAME);

        //加载通用返回信息
        Prompt.processProperties();

        //删除之前未释放的锁(虽然设置了过期时间,暂时是为了更快地释放锁)
        List<String> keys = new ArrayList<>();
        keys.add(TimingOrderLockImpl.KEY_FORMAT.format(new String[]{"timingOrderLock"}));
        redisTemplate.execute(delKeyScript,keys);

        //释放一类锁
        /*keys.clear();
        keys.add(StockTaskLockImpl.KEY_ID);
        keys.add(StockTakingLockImpl.KEY_ID);
        keys.add(TransferLockImpl.KEY_ID);
        keys.add(PurchasesBackLockImpl.KEY_ID);
        redisTemplate.execute(delKeysScript,keys);*/

        //普通出入库类型
        Global.storeRecordTypeSet.addAll(Arrays.asList(IN_OUT_TYPES));

        EXECUTOR_SERVICE.execute(() -> {
            logger.info("初始化省心送冻结>>>");
            merchantLifecycleService.timingOrderLock();
        });

        // 已经废弃
//        Executors.newSingleThreadExecutor().execute(() -> {
//            logger.info("初始化定时任务数据>>>");
//            scheduleTaskService.init();
//        });

        logger.info("初始化城市信息, 城市信息已经切换成了内存缓存>>>");
        // 城市信息已经切换成了内存缓存
        //        List<Area> treeNodes = areaMapper.selectAll(new Area());
        //        for (Area treeNode : treeNodes) {
        //            Global.areaMap.put(treeNode.getAreaNo(), treeNode.getAreaName());
        //        }
        logger.info("初始化库存仓信息>>>");
        List<WarehouseStorageCenter> storageCenters = warehouseStorageService.selectAllData(new WarehouseStorageCenter());
        storageCenters.forEach(storageCenter -> Global.warehouseMap.put(storageCenter.getWarehouseNo(),storageCenter.getWarehouseName()));

        logger.info("初始化配送仓信息>>>");
        List<WarehouseLogisticsCenterVO> centerVOS = warehouseLogisticsService.selectLogisticsAll(WarehouseLogisticsCenterStatus.VALID.ordinal());
        centerVOS.forEach(centerVO -> Global.storeMap.put(centerVO.getStoreNo(),centerVO.getStoreName()));

        logger.info("容器启动事件执行完成。。。。。");
    }
}
