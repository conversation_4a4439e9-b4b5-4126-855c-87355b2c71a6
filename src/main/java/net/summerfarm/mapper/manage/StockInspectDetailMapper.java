package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.StockInspectDetail;
import net.summerfarm.model.vo.StockInspectDetailVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-01
 */
@Repository
public interface StockInspectDetailMapper {

    @RequiresDataPermission(originalField = "sid.area_no")
    List<StockInspectDetailVO> select(StockInspectDetail detail);

    StockInspectDetail selectByPrimaryKey(Integer id);

    void update(StockInspectDetail selectKey);

    List<StockInspectDetail> selectByCondition(StockInspectDetail inspectDetail);

    void insert(StockInspectDetail stockInspectDetail);

    List<StockInspectDetail> selectByTaskId(Integer stockTaskProcessId, String sku);
}
