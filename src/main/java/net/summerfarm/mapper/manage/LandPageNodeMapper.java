package net.summerfarm.mapper.manage;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import net.summerfarm.model.domain.LandPageNode;
import net.summerfarm.model.vo.LandPageNodeProduct;
import org.apache.ibatis.annotations.Param;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.ArrayList;
import java.util.List;

public interface LandPageNodeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LandPageNode record);

    int insertSelective(LandPageNode record);

    LandPageNode selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LandPageNode record);

    int updateByPrimaryKey(LandPageNode record);

    /**
     * 根据
     * @param id
     * @return
     */
    List<LandPageNode> queryNodeByItemParentId(@Param("itemParentId") Long itemParentId);

    List<LandPageNodeProduct> queryNodeSkuByParentId(@Param("itemParentId") Long id);

    List<LandPageNodeProduct> queryNodeSuitByParentId(@Param("itemParentId") Long id);

    void logicalDelByParentId(@Param("itemParentId") Long id);
}