package net.summerfarm.mapper.manage;

import net.summerfarm.model.vo.DataVO;
import net.summerfarm.model.vo.MerchantLifecycleVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface MerchantLifecycleMapper {

    int insertLifecycle();

    /**
     * 获取用户最新生命周期
     * @param mId 商户id
     * @return  生命周期，0新注册，1首单，2非稳，3稳定
     */
    Integer selectLast(@Param("mId")Long mId);

    List<DataVO> lifecycleData(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end, @Param("areaNo") Integer areaNo, @Param("adminId") Integer adminId);

    /**
     * 删除时间之前的数据
     * @param lastNow 时间
     */
    void deleteLifecycleByTime(@Param("Lifecycle") LocalDateTime lastNow);
}