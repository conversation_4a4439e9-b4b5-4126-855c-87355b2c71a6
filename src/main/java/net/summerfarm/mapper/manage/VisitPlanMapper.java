package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.VisitPlan;
import net.summerfarm.model.vo.VisitPlanVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface VisitPlanMapper {
   /**
    * 跟进id查询拜访计划
    * @param id 拜访计划id
    * @return 拜访计划列表
    */
   VisitPlanVO queryVisitPlanOne(Integer id);

   /**
    * 查询拜访计划列表
    * @param visitPlanVO 查询条件
    * @return 拜访计划列表
    */
   @RequiresDataPermission(originalField = "if(vp.type=1, vp.area_no, m.area_no)")
   List<VisitPlanVO> queryVisitPlanList(VisitPlanVO visitPlanVO);

   /**
    * 更新拜访计划
    * @param visitPlan 更新计划信息
    * @return 0或1
    */
   int updateVisitPlan(VisitPlan visitPlan);

   int deleteVisitPlan(VisitPlan visitPlan);

   /**
    * 插入拜访计划
    * @param visitPlan 拜访计划内容
    * @return 0或1
    */
   int insertVisitPlan(VisitPlan visitPlan);

   @RequiresDataPermission(originalField = "m.area_no")
   List<VisitPlanVO> queryVisitPlanNumber(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("adminId") Integer adminId);

   /**
    * 查询拜访计划
    * @param query 查询条件:adminId,date,mId,status,areaNo
    * @return 拜访计划
    */
   List<VisitPlanVO> selectList(VisitPlanVO query);

   int updateUnHandlePlan(LocalDateTime time);

   /**
    * 跟进id查询拜访计划
    * @param id 拜访计划id
    * @return 拜访计划信息
    */
    VisitPlan selectById(Integer id);

   /**
    * 查询相关陪访记录
    * @param visitPlan 查询条件
    * @return 陪访记录
    */
   VisitPlan selectByEscort(VisitPlan visitPlan);
}
