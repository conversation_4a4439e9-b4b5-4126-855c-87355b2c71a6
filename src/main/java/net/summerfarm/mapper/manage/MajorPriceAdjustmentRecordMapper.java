package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MajorPriceAdjustmentRecord;

public interface MajorPriceAdjustmentRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(MajorPriceAdjustmentRecord record);

    int insertSelective(MajorPriceAdjustmentRecord record);

    MajorPriceAdjustmentRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(MajorPriceAdjustmentRecord record);

    int updateByPrimaryKey(MajorPriceAdjustmentRecord record);
}