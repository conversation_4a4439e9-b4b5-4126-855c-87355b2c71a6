package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FinanceAccountingStore;
import net.summerfarm.model.vo.FinanceAccountingStoreVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> 2021-12-08
 */
@Repository
public interface FinanceAccountingStoreMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinanceAccountingStore record);

    /**
     * 插入账期订单门店订单表
     * @param record
     * @return
     */
    int insertSelective(FinanceAccountingStore record);

    /**
     * 查询门店的订单信息统计
     * @param id
     * @return
     */
    FinanceAccountingStore selectByPrimaryKey(@Param("id") Long id);

    /**
     * 修改
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(FinanceAccountingStore record);

    /**
     * 合并信息
     * @param financeOrderId
     * @param id
     * @return
     */
    int updateBySelective(@Param("financeOrderId") Long financeOrderId, @Param("id") Long id);

    /**
     * 查询门店运营服务区域
     *
     * @param mId
     * @return
     */
    List<FinanceAccountingStoreVO> selectAreaNo(@Param("mId") Long mId);

    /**
     * 账单门店详情
     * @param financeOrderId
     * @return
     */
    List<FinanceAccountingStoreVO> selectAll(@Param("financeOrderId") Long financeOrderId);

    /**
     * 账单门店详情
     * @param financeOrderId
     * @param id
     * @return
     */
    List<FinanceAccountingStoreVO> selectByAll(@Param("financeOrderId") Long financeOrderId, @Param("id") Long id);

    /**
     * 修改
     * @param record
     * @return
     */
    int updateByPrimaryKey(FinanceAccountingStore record);

    /**
     * 查询门店信息
     * @param id
     * @return
     */
    List<FinanceAccountingStore> selectByStore(@Param("id") Long id);

    /**
     * 查询门店订单的账期信息
     *
     * @param orderNo 订单编号
     * @return
     */
    FinanceAccountingStore selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查询单家门店信息
     * @param id
     * @return
     */
    FinanceAccountingStore selectOne(@Param("id") Long id);

}