package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.MerchantSituation;
import net.summerfarm.model.vo.MerchantSituationVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface MerchantSituationMapper {

    MerchantSituation querySituation(@Param("msId") Integer msId);

    @RequiresDataPermission(originalField = "m.area_no")
    List<MerchantSituationVO> querySituationList(@Param("msVO") MerchantSituationVO merchantSituationVO, @Param("keyword") String keyword);

    int updateSituation(MerchantSituation merchantSituation);

    int insertMerchantSituation(MerchantSituation merchantSituation);

    int updateSituationALl(MerchantSituation merchantSituation);

    /**
     * 查询客户客情申请情况
     * @param merchantSituationVO 查询条件
     * @return 客户客情申请情况
     */
    List<MerchantSituation> querySituationListTime(MerchantSituationVO merchantSituationVO);

    /**
     * 跟进客户id 查询客户发放卡券情况
     * @param merchantSituationVO 查询条件
     * @return 客户卡券发放情况
     */
    List<MerchantSituationVO> selectCouponByMid(MerchantSituationVO merchantSituationVO);

    /**
     * 根据客户券 id 查询客情记录
     *
     * @param merchantCouponId 商户优惠券id
     * @return {@link MerchantSituation}
     */
    MerchantSituation selectByMerchantCouponId(@Param("merchantCouponId")Integer merchantCouponId);
}
