package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PCDailyQuantity;
import net.summerfarm.model.vo.PCOrderQuery;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Deprecated
public interface PCDailyQuantityMapper {
    int insert(PCDailyQuantity record);

    int insertSelective(PCDailyQuantity record);

    int delete(@Param("storeNo") Integer areaNo, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    List<PCDailyQuantity> selectList(PCOrderQuery query);

    /**
     * 平均每小时销量
     * @param storeNo
     * @param sku
     * @param startDate
     * @param endDate
     * @return
     */
    Integer selectAmountPerHour(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
}