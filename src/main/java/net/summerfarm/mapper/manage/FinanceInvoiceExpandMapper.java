package net.summerfarm.mapper.manage;

import net.summerfarm.biz.finance.vo.FinancialInvoiceMoneyVO;
import net.summerfarm.model.domain.FinanceInvoiceExpand;

/**
* <AUTHOR>
* @description 针对表【finance_invoice_expand(票据红冲信息拓展表)】的数据库操作Mapper
* @createDate 2022-09-17 22:20:51
*/
public interface FinanceInvoiceExpandMapper {

    /**
     * 插入操作
     * @param record 拓展信息
     * @return ok
     */
    int insertSelective(FinanceInvoiceExpand record);

    /**
     * 根据主键查询
     * @param id 拓展信息
     * @return 票据拓展信息
     */
    FinanceInvoiceExpand selectByPrimaryKey(Long id);

    /**
     * 根据主键id更新操作
     * @param record 拓展信息
     * @return ok
     */
    int updateByPrimaryKeySelective(FinanceInvoiceExpand record);

    /**
     * 查询发票关联的拓展信息
     * @param financialInvoiceId 发票id
     * @return 发票关联的拓展信息
     */
    FinanceInvoiceExpand selectByFinancialInvoiceId(Long financialInvoiceId);
}
