package net.summerfarm.mapper.manage.saas;

import cn.hutool.core.date.DateTime;
import net.summerfarm.model.domain.OrderItem;
import net.summerfarm.model.domain.saas.TmsDeliveryPlan;
import net.summerfarm.model.vo.saas.OutsideAfterSaleVO;
import net.summerfarm.model.vo.saas.TmsDeliveryPlanVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.plugin.Intercepts;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/5/12  19:24
 */
@Repository
public interface TmsDeliveryPlanMapper {

    /**
     * 插入
     * @param tmsDeliveryPlan
     * @return
     */
    int saveTmsDeliveryPlan(TmsDeliveryPlan  tmsDeliveryPlan);

    /**
     * 查询
     * @param tmsDeliveryPlan
     * @return
     */
    TmsDeliveryPlan  selectTmsDeliveryPlan(TmsDeliveryPlan tmsDeliveryPlan);

    /**
     * 更新
     * @param tmsDeliveryPlan
     * @return
     */
    int updateTmsDeliveryPlan(TmsDeliveryPlan tmsDeliveryPlan);


    /**
     * 查询外部待配送订单
     * @param deliveryDate
     * @param trustStoreNo
     * @param areaNo
     * @param deliveryType
     * @return
     */
    List<OrderItem> selectOuterSaleOutOrder(@Param("deliveryDate") LocalDate deliveryDate, @Param("storeNo") Integer trustStoreNo, @Param("warehouseNo") Integer areaNo, @Param("deliveryType")Boolean deliveryType);

    /**
     * 查询外部待配送订单（越库出库任务）
     * @param deliveryDate
     * @param trustStoreNo
     * @param areaNo
     * @param deliveryType
     * @return
     */
    List<OrderItem> selectOuterSaleOutOrderForCross(@Param("deliveryDate") LocalDate deliveryDate, @Param("storeNo") Integer trustStoreNo, @Param("warehouseNo") Integer areaNo, @Param("deliveryType")Boolean deliveryType);

    Integer selectSkuNumByOrderNoSku(@Param("orderNo")String orderNo,@Param("sku") String sku,@Param("storeId") Long storeId,@Param("tenantId") Long tenantId);

    /**
     * 外部配送单信息
     * @param deliveryDate
     * @param storeNo
     * @return
     */
    List<TmsDeliveryPlanVO> selectPlanVO(@Param("deliveryDate") LocalDate deliveryDate, @Param("storeNo") Integer storeNo);

    /**
     * 批量更新城配仓
     * @param ids
     * @param storeNo
     * @return
     */
    int updateStoreNo(@Param("ids")List<Integer> ids ,@Param("storeNo")Integer storeNo);

    /**
     * 获取带配送的数量
     */
    Integer selectSaleLockByStoreNo(@Param("storeNo") Integer storeNo,@Param("sku") String sku ,@Param("startDate") LocalDate startDate);

    /**
     * 查询saas外部订单未配送数量
     * @param warehouseNo
     * @param sku
     * @param deliveryDte
     * @return
     */
    Integer selectSaasQuantity(@Param("warehouseNo")Integer warehouseNo,@Param("sku") String sku,@Param("deliveryDte") LocalDate deliveryDte);

    /**
     * 根据日期查询售后单没有被取消的数据
      * @param deliveryDate
     * @param storeNo
     * @return
     */
    List<OutsideAfterSaleVO> getAfterSalePassData(@Param("deliveryDate") LocalDate deliveryDate,@Param("storeNo") Integer storeNo);

    /**
     * 根据日期查询售后单没有被取消的数据（越库出库）
     * @param deliveryDate
     * @param storeNo
     * @return
     */
    List<OutsideAfterSaleVO> getAfterSalePassDataForCross(@Param("deliveryDate") LocalDate deliveryDate,@Param("storeNo") Integer storeNo);

    /**
     * 根据售后单号查询有效的数据
     * @param orderNo
     * @return
     */
    TmsDeliveryPlan selectAfterByOrderNo(String orderNo);

    /**
     * 根据日期查询售后单查询回收任务信息
     * @param deliveryDate
     * @param storeNo
     * @retur
     */
    List<OutsideAfterSaleVO> getInAfterSalePassData(@Param("deliveryDate") LocalDate deliveryDate,@Param("storeNo") Integer storeNo);

    /**
     * 根据日期查询售后单查询回收任务信息
     * @param deliveryDate
     * @param storeNo
     * @retur
     */
    List<OutsideAfterSaleVO> getInAfterSalePassDataForCross(@Param("deliveryDate") LocalDate deliveryDate,@Param("storeNo") Integer storeNo);


    /**
     * 获取sku配送数量
     * @param sku
     * @param afterSaleOrderNo
     * @return
     */
    Integer selectSkuNumByAfterOrderNo(@Param("sku")String sku,@Param("afterSaleOrderNo") String afterSaleOrderNo);


    TmsDeliveryPlan selectPlanByOuterNo(String outerNo);

    /**
     * 查询
     * @param orderNos 订单号集合
     * @return 结果
     */
    List<TmsDeliveryPlan> queryListByOrderNos(@Param("orderNos")List<String> orderNos);
}
