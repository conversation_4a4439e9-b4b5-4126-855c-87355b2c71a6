package net.summerfarm.mapper.manage;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import net.summerfarm.common.util.validation.annotation.InRange;
import net.summerfarm.model.domain.DaySaleRank;
import org.apache.ibatis.annotations.Param;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

public interface DaySaleRankMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(DaySaleRank record);

    int insertSelective(DaySaleRank record);

    DaySaleRank selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DaySaleRank record);

    int updateByPrimaryKey(DaySaleRank record);

    /**
     * 将表内所有信息删除
     * @return
     */
    int deleteAll();

    /**
     * 批量插入
     * @param daySaleRankList
     * @return
     */
    int insertBatch(@Param("daySaleRankList") List<DaySaleRank> daySaleRankList);
}