package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.ExpandAreaDto;
import net.summerfarm.model.domain.ExpandArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExpandAreaMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ExpandArea record);

    int insertSelective(ExpandArea record);

    ExpandArea selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ExpandArea record);

    int updateByPrimaryKey(ExpandArea record);

    /**
     * 批量插入区域聚合数据
     *
     * @param expandAreas 区域聚合数据集合
     * @return 变更记录数
     */
    int insertBatch(@Param("expandAreaList") List<ExpandArea> expandAreas);

    /**
     * 批量删除区域聚合数据
     *
     * @param expandId 区域聚合数据集合
     * @return 变更记录数
     */
    int deleteByExpandId(@Param("expandId")Long expandId);

    /**
     * 批量查询区域聚合数据
     *
     * @param expandId
     * @return
     */
    List<ExpandAreaDto> selectByExpandId(@Param("expandId")Long expandId);

}