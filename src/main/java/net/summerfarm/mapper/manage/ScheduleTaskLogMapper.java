package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.ScheduleTaskLog;
import net.summerfarm.model.vo.ScheduleTaskLogVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ScheduleTaskLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ScheduleTaskLog record);

    int insertSelective(ScheduleTaskLog record);

    ScheduleTaskLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ScheduleTaskLog record);

    int updateByPrimaryKey(ScheduleTaskLog record);

    List<ScheduleTaskLogVO> list(@Param("taskId") Integer taskId, @Param("date") LocalDate date);
}