package net.summerfarm.task;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.common.util.SpringContextUtil;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.enums.CommonStatus;
import net.summerfarm.enums.market.activity.ActivityTagEnum;
import net.summerfarm.enums.market.activity.ActivityTypeEnum;
import net.summerfarm.enums.market.activity.PlatformEnum;
import net.summerfarm.enums.market.activity.ScopeTypeEnum;
import net.summerfarm.mapper.manage.ActivityBasicInfoMapper;
import net.summerfarm.mapper.manage.ActivityItemConfigMapper;
import net.summerfarm.mapper.manage.ActivitySceneConfigMapper;
import net.summerfarm.mapper.manage.ActivityScopeConfigMapper;
import net.summerfarm.mapper.manage.ActivitySkuDetailMapper;
import net.summerfarm.mapper.manage.ActivitySkuPriceMapper;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.AreaSkuMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.LargeAreaMapper;
import net.summerfarm.model.DTO.market.ActivityLadderConfigDTO;
import net.summerfarm.model.DTO.market.ActivityLadderPriceDTO;
import net.summerfarm.model.domain.market.ActivityBasicInfo;
import net.summerfarm.model.domain.market.ActivityItemConfig;
import net.summerfarm.model.domain.market.ActivitySceneConfig;
import net.summerfarm.model.domain.market.ActivityScopeConfig;
import net.summerfarm.model.domain.market.ActivitySkuDetail;
import net.summerfarm.model.domain.market.ActivitySkuPrice;
import net.summerfarm.model.vo.AreaSkuVO;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.K;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 根据阶梯价初始化活动
 *
 * @author: <EMAIL>
 * @create: 2023/2/1
 */
@Slf4j
@Component
public class ActivityLadderPriceInitProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private ActivityBasicInfoMapper activityBasicInfoMapper;
    @Resource
    private ActivityItemConfigMapper activityItemConfigMapper;
    @Resource
    private ActivitySkuDetailMapper activitySkuDetailMapper;
    @Resource
    private ActivityScopeConfigMapper activityScopeConfigMapper;
    @Resource
    private ActivitySceneConfigMapper activitySceneConfigMapper;
    @Resource
    private ActivitySkuPriceMapper activitySkuPriceMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private DynamicConfig dynamicConfig;

    public final static Date defaultDate = DateUtil.parseStartDate("20230101", "yyyyMMdd");

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        try {
            log.info("[阶梯价转换为特价活动]任务开始执行, context: 【{}】", JSON.toJSONString(context));
            Integer largeAreaNo = null;
            String instanceParameters = context.getInstanceParameters();
            if(StringUtils.isNotBlank(instanceParameters)) {
                largeAreaNo = Integer.valueOf(instanceParameters);
            }

            // 1.获取所有需要处理的城市
            List<AreaSkuVO> allLadderArea = areaSkuMapper.getAllLadderArea(largeAreaNo);
            if (CollUtil.isEmpty(allLadderArea)) {
                log.info("[阶梯价转换为特价活动]执行完成!!, 暂无需要处理的数据");
                return new ProcessResult(true);
            }

            // 2.按大区分批处理
            this.handle(allLadderArea);

            log.info("[阶梯价转换为特价活动]]执行完成!!");
        } catch (Exception e) {
            log.error("[阶梯价转换为特价活动]]执行失败!", e);
        }
        return new ProcessResult(true);
    }

    private void handle(List<AreaSkuVO> allLadderArea) {
        ActivityLadderPriceInitProcessor bean = SpringContextUtil.getBean("activityLadderPriceInitProcessor", ActivityLadderPriceInitProcessor.class);
        Map<Integer, List<AreaSkuVO>> collect = allLadderArea.stream().collect(Collectors.groupingBy(AreaSkuVO::getLargeAreaNo));
        collect.forEach((k, v) ->{
            try {
                bean.handleSingleLargeArea(k, v);
            } catch (Exception e) {
                log.error("[阶梯价转换为特价活动]]执行失败! 当前在处理的大区：{}, ", k, e);
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleSingleLargeArea(Integer largeAreaNo, List<AreaSkuVO> voList) {
        if(CollUtil.isEmpty(voList)) {
            log.info("[阶梯价转换为特价活动]largeAreaNo：{}对应的区域信息不存在！", largeAreaNo);
            return;
        }
        // 2.按areaNo初始化基本的活动信息
        AreaSkuVO areaSkuVO = voList.get(0);
        ActivityItemConfig itemConfig = initActivity(largeAreaNo, areaSkuVO.getLargeAreaName());
        log.info("大区：{}的活动基本信息完成!", largeAreaNo);


        // 同一个大区下的一个sku只需要生成一条detail数据即可， 前面的sql已经去重了

        // todo 去除鲜果的sku
        List<String> skus = voList.stream().map(AreaSkuVO::getSku).collect(Collectors.toList());
        List<String> fruitSkuList = inventoryMapper.selectFruitSkuBySkus(skus);
        log.info("大区：{}的鲜果列表：fruitSkuList：{}", largeAreaNo, JSON.toJSONString(fruitSkuList));
        List<AreaSkuVO> collect = voList.stream().filter(vo -> !fruitSkuList.contains(vo.getSku())).collect(Collectors.toList());

        collect.forEach(vo -> this.handleSingleSku(vo, itemConfig));
        log.info("大区：{}的sku活动详情初始化完成!", largeAreaNo);
    }

    private void handleSingleSku(AreaSkuVO areaSkuVO, ActivityItemConfig itemConfig) {
        log.info("开始初始化sku活动详情.areaSkuVO:{}", JSON.toJSONString(areaSkuVO));
        Integer largeAreaNo = areaSkuVO.getLargeAreaNo();
        String sku = areaSkuVO.getSku();
        AreaSkuVO areaSku = areaSkuMapper.getPendingLadderPrice(sku, largeAreaNo);
        if (areaSku == null) {
            log.info("[阶梯价转换为特价活动]当前区域：largeAreaNo:{}, sku:{}, 暂无需要初始化的数据", largeAreaNo, sku);
            return;
        }
        // 3.初始化活动阶梯配置 (目前单个areaNo下配置了阶梯价的sku数量不超过200)
        List<ActivitySkuDetail> activitySkuDetails = this.initActivitySkuDetail(areaSku, itemConfig.getId());
        // 4.初始化价格
        this.initActivitySkuPrice(largeAreaNo, areaSku, activitySkuDetails, itemConfig.getBasicInfoId());
    }

    private ActivityItemConfig initActivity(Integer largeAreaNo, String largeAreaName) {
        log.info("[阶梯价转换为特价活动]活动基本信息初始化开始：largeAreaNo：{}.", largeAreaNo);
        //生成基本信息
        ActivityBasicInfo basicInfo = new ActivityBasicInfo();
        basicInfo.setName(largeAreaName + "-阶梯价默认活动");
        basicInfo.setType(ActivityTypeEnum.SPECIAL_PRICE.getCode());
        basicInfo.setIsPermanent(CommonStatus.YES.getCode());
        basicInfo.setNeedPre(CommonStatus.NO.getCode());
        basicInfo.setStatus(CommonStatus.NO.getCode());
        basicInfo.setTag(ActivityTagEnum.SLOW_SALE_PROMOTION.getCode());
        basicInfo.setRemark("系统创建。" + basicInfo.getName());
        basicInfo.setStartTime(new Date());
        basicInfo.setEndTime(DateUtil.parseEndDate(basicInfo.getStartTime(), 30));
        basicInfo.setCreateTime(defaultDate);
        basicInfo.setCreatorId(0);
        activityBasicInfoMapper.insertSelective(basicInfo);
        Long basicInfoId = basicInfo.getId();

        //生成活动场景配置
        ActivitySceneConfig sceneConfig = new ActivitySceneConfig();
        sceneConfig.setBasicInfoId(basicInfoId);
        sceneConfig.setPlatform(PlatformEnum.MALL.getCode());
        sceneConfig.setCreateTime(defaultDate);
        activitySceneConfigMapper.insertSelective(sceneConfig);

        //生成活动商品配置
        ActivityItemConfig itemConfig = new ActivityItemConfig();
        itemConfig.setBasicInfoId(basicInfoId);
        itemConfig.setGoodSelectWay(0);
        itemConfig.setPricingType(0);
        itemConfig.setCreateTime(defaultDate);
        activityItemConfigMapper.insertSelective(itemConfig);

        //生成活动生效范围配置
        ActivityScopeConfig activityScopeConfig = new ActivityScopeConfig();
        activityScopeConfig.setScopeId(largeAreaNo.longValue());
        activityScopeConfig.setScopeType(ScopeTypeEnum.LARGE_AREA.getCode());
        activityScopeConfig.setBasicInfoId(basicInfoId);
        activityScopeConfig.setCreateTime(defaultDate);
        activityScopeConfigMapper.insertSelective(activityScopeConfig);
        log.info("[阶梯价转换为特价活动]活动基本信息初始化结束：areaNo：{}", largeAreaNo);
        return itemConfig;
    }

    private List<ActivitySkuDetail> initActivitySkuDetail(AreaSkuVO areaSku, Long itemId) {
        log.info("[阶梯价转换为特价活动]活动sku配置信息初始化开始，itemId：{}, areaSku:{}", itemId, JSON.toJSONString(areaSku));
        List<ActivitySkuDetail> createDetailList = new ArrayList<>();
        this.buildCreateDetailList(areaSku, createDetailList, itemId);
        if(CollUtil.isNotEmpty(createDetailList)) {
            activitySkuDetailMapper.insertBatch(createDetailList, itemId);
        }
        log.info("[阶梯价转换为特价活动]活动sku配置信息初始化完成，itemId：{}", itemId);
        return createDetailList;
    }

    private void buildCreateDetailList(AreaSkuVO areaSkuVO, List<ActivitySkuDetail> createDetailList, Long itemId) {
        try {
            ActivitySkuDetail skuDetail = new ActivitySkuDetail();
            String ladderPrice = areaSkuVO.getLadderPrice();
            if (StringUtils.isBlank(ladderPrice)) {
                log.warn("当前数据阶梯价异常，不做处理。areaSkuVO:{}", JSON.toJSONString(areaSkuVO));
                return;
            }
            List<ActivityLadderConfigDTO> list = JSON.parseArray(ladderPrice, ActivityLadderConfigDTO.class);
            if (CollUtil.isEmpty(list)) {
                log.warn("当前数据阶梯价异常，不做处理。areaSkuVO:{}", JSON.toJSONString(areaSkuVO));
                return;
            }
            skuDetail.setIsSupportTiming(1);
            skuDetail.setSku(areaSkuVO.getSku());
            skuDetail.setItemConfigId(itemId);
            skuDetail.setAccountLimit(0);
            skuDetail.setLimitQuantity(0);
            skuDetail.setMinSaleNum(0);
            skuDetail.setAccountLimit(0);
            skuDetail.setPlanQuantity(100000);
            skuDetail.setActualQuantity(100000);
            skuDetail.setLadderConfig(JSON.toJSONString(list));
            createDetailList.add(skuDetail);
        } catch (Exception e) {
            log.warn("构建skuDetail信息异常。areaSkuVO:{}", JSON.toJSONString(areaSkuVO), e);
        }
    }

    private void initActivitySkuPrice(Integer largeAreaNo, AreaSkuVO areaSkuVO, List<ActivitySkuDetail> activitySkuDetailList, Long basicId) {
        log.info("[阶梯价转换为特价活动]活动sku价格信息初始化开始, areaSkuVO:{}, basicId:{}", JSON.toJSONString(areaSkuVO), basicId);
        List<ActivitySkuPrice> createDetailList = new ArrayList<>();
        this.buildCreatePriceList(largeAreaNo, areaSkuVO, createDetailList, activitySkuDetailList, basicId);
        if(CollUtil.isNotEmpty(createDetailList)) {
            activitySkuPriceMapper.insertBatch(createDetailList);
        }
        log.info("[阶梯价转换为特价活动]活动sku配置信息初始化完成，basicId：{}", basicId);
    }


    private void buildCreatePriceList(Integer largeAreaNo, AreaSkuVO areaSkuVO, List<ActivitySkuPrice> createPriceList, List<ActivitySkuDetail> activitySkuDetailList, Long basicId) {
        try {
            if (CollUtil.isEmpty(activitySkuDetailList)) {
                log.info("skuDetail信息为空，无需处理价格！");
            }
            Map<String, ActivitySkuDetail> collect = activitySkuDetailList.stream().collect(Collectors.toMap(ActivitySkuDetail::getSku, Function.identity()));
            String ladderPrice = areaSkuVO.getLadderPrice();
            if (StringUtils.isBlank(ladderPrice)) {
                log.warn("当前数据阶梯价异常，不做处理。areaSkuVO:{}", JSON.toJSONString(areaSkuVO));
                return;
            }
            List<ActivityLadderPriceDTO> list = JSON.parseArray(ladderPrice, ActivityLadderPriceDTO.class);
            if (CollUtil.isEmpty(list)) {
                log.warn("当前数据阶梯价异常，不做处理。areaSkuVO:{}", JSON.toJSONString(areaSkuVO));
                return;
            }
            String sku = areaSkuVO.getSku();
            ActivitySkuDetail activitySkuDetail = collect.get(sku);
            if (activitySkuDetail == null) {
                log.warn("当前数据无阶梯配置，不做处理。areaSkuVO:{}", JSON.toJSONString(areaSkuVO));
                return;
            }

            // 根据大区获取所有需要初始化的区域
            List<Integer> areaNos = areaMapper.selectByLargeAreaNosAndFence(Collections.singletonList(largeAreaNo));
            if(CollUtil.isEmpty(areaNos)) {
                log.warn("当前大区无可用运营区域，不做处理。areaSkuVO:{}", JSON.toJSONString(areaSkuVO));
                return;
            }

            for (Integer areaNo : areaNos) {
                ActivitySkuPrice skuPrice = new ActivitySkuPrice();
                skuPrice.setSkuDetailId(activitySkuDetail.getId());
                skuPrice.setBasicInfoId(basicId);
                skuPrice.setSku(sku);
                skuPrice.setAreaNo(areaNo);
                skuPrice.setLadderPrice(JSON.toJSONString(list));
                skuPrice.setSalePrice(areaSkuVO.getPrice());
                skuPrice.setUpdaterId(0);
                createPriceList.add(skuPrice);
            }
        } catch (Exception e) {
            log.warn("构建skuPrice信息异常。areaSkuVO:{}", JSON.toJSONString(areaSkuVO), e);
        }
    }

}
