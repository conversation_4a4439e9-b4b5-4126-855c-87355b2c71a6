package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.MerchantCard;
import net.summerfarm.model.vo.MerchantCardVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MerchantCardMapper {

    @RequiresDataPermission(originalField = "m.area_no")
    List<MerchantCardVO> selectVO(MerchantCardVO merchantCardVO);

    MerchantCard selectByPrimaryKey(Integer id);

}
