package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.ArrivalNotice;
import net.summerfarm.model.vo.ArrivalNoticeVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wjd on 2017/12/20.
 */

@Repository
public interface ArrivalNoticeMapper {

    //根据一级仓分组
    @RequiresDataPermission(originalField = "an.store_no")
    List<ArrivalNoticeVO> select(ArrivalNoticeVO arrivalNoticeVO);

    //根据二级城市分组
    @RequiresDataPermission(originalField = "an.store_no")
    List<ArrivalNoticeVO> selectList(ArrivalNoticeVO selectKey);

    List<ArrivalNotice> selectBySku(ArrivalNotice arrivalNotice);

    List<ArrivalNotice> selectByAreaNo(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    int deleteById(Integer id);

    /**
     * 删除订阅记录
     * @param mId 1、为你上新
     * @param type 0、到货提醒
     * @return
     */
    int deleteByMid(@Param("mId") Long mId, @Param("type") Integer type);

    /**
     * 修改到货提醒
     * @param oldMid
     * @param newMid
     * @return
     */
    int changeNoticeMerchant(@Param("oldMid") Long oldMid, @Param("newMid") Long newMid);

    /**
     * 查询重复订阅
     * @param mId 店铺id
     * @return
     */
    List<ArrivalNotice> selectRepeat(Long mId);


    /**
     * 上新订阅
     * 去除了对活动商品查询（已和业务方确认）
     * @param arrivalNoticeVO 到货提醒
     * @return 到货 list
     */
    List<ArrivalNoticeVO> selectNewSubscription(ArrivalNoticeVO arrivalNoticeVO);
}
