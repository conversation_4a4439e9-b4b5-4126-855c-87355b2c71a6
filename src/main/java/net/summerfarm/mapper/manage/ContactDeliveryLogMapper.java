package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AdCodeMsg;
import net.summerfarm.model.domain.ContactDeliveryLog;
import net.summerfarm.model.vo.ContactVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地址配送周期日志mapper
 */
public interface ContactDeliveryLogMapper {
    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增地址配送周期日志
     * @param record 地址配送周期日志
     * @return
     */
    int insert(ContactDeliveryLog record);
    /**
     * 新增地址配送周期日志
     * @param record 地址配送周期日志
     * @return
     */
    int insertSelective(ContactDeliveryLog record);
    /**
     * 根据id查看
     * @param id
     * @return
     */
    ContactDeliveryLog selectByPrimaryKey(Long id);
    /**
     * 更新地址配送周期日志
     * @param record 地址配送周期日志
     * @return
     */
    int updateByPrimaryKeySelective(ContactDeliveryLog record);
    /**
     * 更新地址配送周期日志
     * @param record 地址配送周期日志
     * @return
     */
    int updateByPrimaryKey(ContactDeliveryLog record);

    /**
     * 根据联系人id查询有效的地址配送周期日志
     * @param contactId 联系人id
     * @param effectFlag 生效标志
     * @return 地址配送周期日志
     */
    ContactDeliveryLog selectByEffectMid(@Param("contactId") Long contactId, @Param("effectFlag") int effectFlag);

    /**
     * 根据地址查看有效的地址配送周期日志
     * @param adCodeMsgs
     * @return
     */
    List<ContactVO> selectByCityArea(@Param("adCodeMsgs") List<AdCodeMsg> adCodeMsgs);
}