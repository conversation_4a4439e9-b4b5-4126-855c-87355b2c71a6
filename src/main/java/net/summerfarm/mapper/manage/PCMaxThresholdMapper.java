package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PCMaxThreshold;
import org.apache.ibatis.annotations.Param;

@Deprecated
public interface PCMaxThresholdMapper {
    int insert(PCMaxThreshold record);

    int insertSelective(PCMaxThreshold record);

    int deleteAll();

    PCMaxThreshold selectOne(@Param("storeNo") Integer areaNo, @Param("sku") String sku, @Param("calcStore") String storeNoStr);

    int updateSelective(PCMaxThreshold maxThreshold);

    PCMaxThreshold selectOneBySoreNo(@Param("storeNo") Integer areaNo, @Param("sku") String sku);

}