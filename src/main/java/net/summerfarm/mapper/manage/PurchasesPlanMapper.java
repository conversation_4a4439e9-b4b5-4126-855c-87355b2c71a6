package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.PurchasesPlan;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.vo.pms.PurchaseSkuDetailVO;
import net.summerfarm.module.pms.model.vo.SkuWeightVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public interface PurchasesPlanMapper {

    /**
     * 添加采购计划
     * @param data
     * @param purchaseNo
     * @return
     */
    int insertBatch(@Param(value = "list") List data, @Param(value = "purchaseNo") String purchaseNo);

    int insertSelective(PurchasesPlan purchasesPlan);

    int updateWithPurchases(PurchasesPlan purchasesPlan);

    int update(PurchasesPlan purchasesPlan);

    List<PurchasesPlan> selectByPurchasesNo(String purchaseNo);

    List<PurchasesPlan> selectByPurchasesNosV2(@Param("purchasesNos") List<String> purchaseNos);

    List<PurchasesPlan> selectWithPurchasesNos(List<String> purchaseNo);

    List<PurchasesPlanVO> selectByMatch(String purchaseNo);

    List<PurchasesPlanVO> selectByPurchases(String purchaseNo);

    List<PurchasesPlanVO> selectByPurchasesPlanIds(@Param(value = "list") List<Integer> list);

    List<PurchasesPlanResultVO> selectVOByPurchasesNo(String purchaseNo);

    PurchasesPlan selectOne(int id);

    PurchasesPlan selectByNoAndSku(@Param("purchaseNo") String purchaseNo, @Param("sku") String sku, @Param("qualityDate") LocalDate qualityDate);

    List<PurchasesPlanVO> selectPurchasesPlans(PurchasesPlanVO selectKey);

    PurchasesPlan selectByPrimaryKey(Integer id);

    List<PurchasesPlan> selectBySku(@Param("purchaseNo") String purchaseNo, @Param("sku") String sku);

    List<PurchasesPlan> selectListByPrimaryKey(Integer id);

    /**
     * 查询最新
     *
     * @param storeNo 仓库编号
     * @param sku     sku
     * @return 采购单
     */
    PurchasesPlan selectNewest(@Param("storeNo") Integer storeNo, @Param("sku") String sku);

    /**
     * 查询上一批次
     *
     * @param storeNo 仓库编号
     * @param sku     sku
     * @return 采购单
     */
    PurchasesPlan selectLast(@Param("storeNo") Integer storeNo, @Param("sku") String sku);

    /**
     * 查询sku最新采购单号的入库批次
     * @param sku sku
     * @return 采购单
     */
    List<PurchasesPlan> selectLastBatch(@Param("sku") String sku);

    PurchasesPlan selectPurchasePrice(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    /**
     * 查询未结算采购项
     * @param query
     * @return
     */
    @RequiresDataPermission(originalField = "p.area_no")
    List<PurchasesPlanVO> selectUnSettle(PurchasesPlan query);

    int countSupplier(List<Integer> planIdList);

    int deleteById(Integer id);

    /**
     * 到帐未结算采购项总计
     * @return
     */
    BigDecimal unSettlePlanTotal();
    /**
     * 现金未结算采购项总计
     * @return
     */
    BigDecimal unSettleCashTotal();
    /**
    *  查询未发起核算单的采购项
     * @param startDate
     * @param endDate
     * @return
    */
    List<PurchasesPlanVO> queryPurchasePlan(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    PurchasesPlan selectOriginBySku(@Param("purchaseNo") String purchaseNo, @Param("sku") String sku);



    List<PurchasesPlan> selectOriginPlan(String purchaseNo);

    /**
     * 查询主采购计划
     * @param purchaseNo
     * @param sku
     * @return
     */
    PurchasesPlan selectSingleOriginPlan(@Param("purchaseNo") String purchaseNo, @Param("sku") String sku);

    /**
     * 用于导出未结算单的采购单明细
     * @Param:
     * @Return: java.util.List<net.summerfarm.model.vo.UnSettlePurchasePlanVO>
     * @Date: 2020/10/13 18:25
     * @Author: <EMAIL>
     */
    List<UnSettlePurchasePlanVO> queryUnSettlePurchasePlan();

    /**
     * 根据添加时间和sku查询有效的采购计划详情
     * @param startTime
     * @param endTime
     * @param skus
     * @return
     */
    List<PurchasesPlanVO> selectValidByAddTimeAndSku(@Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime, @Param("skus") Set<String> skus);

    /**
     * 待匹配采购单
     * @param matchPurchasesVO
     * @return
     */
    List<MatchPurchaseOrderVO> waitForMatch(MatchPurchasesVO matchPurchasesVO);

    /**
     * 待匹配采购单待匹配总额
     * @param matchPurchasesVO
     * @return
     */
    MatchPurchaseOrderVO selectWaitMatchAmount(MatchPurchasesVO matchPurchasesVO);

    /**
     * 根据sku和采购单号查询采购计划id
     * @param sku
     * @param purchaseNo
     * @return
     */
    PurchasesPlan selectId(@Param("sku") String sku, @Param("purchaseNo") String purchaseNo);

    /**
     * 零成本入库查询
     * @param sku
     * @param purchaseNo
     * @return
     */
    PurchasesPlan selectZero(@Param("sku") String sku, @Param("purchaseNo") String purchaseNo);

    /**
     * 根据id查询采购计划的匹配进度
     * @param id
     * @return
     */
    PurchasesPlanVO selectMatchingSchedule(@Param("id") Integer id);

    /**
     * 修改采购计划的供应商
     * @param purchaseNo
     * @param supplier
     * @param supplierId
     * @return
     */
    int updateSupplier(@Param("purchaseNo") String purchaseNo, @Param("supplier") String supplier, @Param("supplierId") Integer supplierId, @Param("sku") String sku);

    /**
     * 查询供应商是否有采购单
     * @param supplierId
     * @return
     */
    int selectExistenceSupplier(Integer supplierId);

    /**
     * 新增结算单，根据供应商id查询采购单详情
     * @param query
     * @return
     */
    List<PurchasesPlanVO> selectUnSettleSheet(PurchaseSettlementVO query);

    /**
     * 根據id更新批次号
     * @param purchaseId
     * @param newPurchasesNo
     */
    void updatePurchaseNoById(@Param("id") Integer purchaseId, @Param("purchaseNo") String newPurchasesNo);

    void updatePlanStatusByNo(PurchasesPlan pp);

    /**
     * 批量查询采购计划
     */
    List<PurchasesPlan> selectByPurchasesNos(@Param("list") Set<String> purchaseNos, @Param("sku") String sku);

    /**
     * 批量查询采购计划
     */
    List<PurchasesPlan> selectByPurchasesNosAndSkus(@Param("list") List<String> purchaseNos, @Param("skus") List<String> sku);

    /**
     * 查询采购计划
     */
    PurchasesPlan selectBySequential(@Param("purchaseNo") String listNo, @Param("sku") String sku);

    /**
     * 查询所有未履约核销的采购计划
     */
    List<PurchasesPlanVO> selectNonPerformance();

    /**
     * 根据供应商id和采购单号查询采购计划
     *
     * @param purchaseNo
     * @param supplierId
     * @return
     */
    List<PurchasesPlan> selectBySupplierAndNo(@Param("purchaseNo") String purchaseNo, @Param("supplierId") Integer supplierId);

    /**
     * 查询预付的采购计划
     * @param purchaseNo
     * @param supplierId
     * @param settleFlag
     * @return
     */
    List<PurchasesPlanVO> queryAdvanceAble(@Param("purchaseNo") String purchaseNo, @Param("supplierId") Integer supplierId, @Param("settleFlag") Integer settleFlag);

    /**
     * 查询可预约的采购计划
     */
    List<PurchasesPlanResultVO> selectArrange(String purchasesNo);

    /**
     * 根据采购单号和供应商id查询可预约的采购计划
     */
    List<PurchasesPlanResultVO> selectArrangeByPurchasesNoAndSupplierId(String purchaseNo, Integer supplierId);

    /**
     * 修改sku默认可预约数量
     * @param purchaseNo
     */
    void updateDefaultArrQuantity(String purchaseNo);

    /**
     * 修改sku可预约数量
     * @param purchaseNo
     */
    void updateArrangeQuantity(@Param("purchaseNo") String purchaseNo, @Param("sku") String sku, @Param("outQuantity") Integer outQuantity);

    /**
     * 根据采购编号及sku修改可预约数量
     * @param plan
     */
    void updateArrangeQuantityByNoAndSku(PurchasesPlanVO plan);

    /**
     * 根据库存仓查询采购单
     * @param warehouseNo
     * @return
     */
    List<PurchasesPlan> selectByAreaNo(@Param("warehouseNo") Integer warehouseNo, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 根据采购单号和sku查询
     * @param purchaseNo
     * @param sku
     * @return
     */
    PurchasesPlan queryByNoAndSku(@Param("purchaseNo") String purchaseNo, @Param("sku") String sku);


    /**
     * 查询相关采购计划
     * @param sku
     * @param latestArrivalDate
     * @param warehouseNo
     * @return
     */
    List<PurchasesPlan> queryBySkuAndLatestArrivalDateAndWarehouseNo(@Param("sku")String sku,@Param("latestArrivalDate")LocalDateTime latestArrivalDate,@Param("warehouseNo")Integer warehouseNo);

    /**
     * 根据采购单号查询SKU数量，采购总数以及总价格
     */
    PurchasesResultVO selectSkuNumTotalQuantityTotalPriceByPurchaseNo(String purchaseNo);

    /**
     * chaxun
     * @param purchaseNo
     * @param skuList
     * @return
     */
    List<Integer> selectPlanIds(@Param("purchaseNo") String purchaseNo,@Param("skuList")List<String> skuList);

    List<Long> selectSupplierByPurchaseNoAndSku(@Param("purchaseNo") String purchaseNo, @Param("sku") String sku);
    /**
     * 获取采购详情
     **/
    List<PurchaseSkuDetailVO> selectByPurchasesNoList(@Param("purchaseNoList") List<String> purchaseNoList);
    void updateToNewPurchaseNoBySupplierId(@Param("beforePurchaseNo") String beforePurchaseNo, @Param("purchaseNo") String purchaseNo, @Param("supplierId") Integer supplierId);

    void deleteByPurchaseNo(String purchaseNo);

    void updateZeroToArrQuantity(String purchaseNo);

    Integer getSupplierIdByBatchAndSku(@Param("batch") String batch, @Param("sku") String sku);

    List<SkuWeightVO> selectSkuNetWeight(@Param("skuList") List<String> skuList, @Param("warehouseNo") Integer warehouseNo, @Param("tenantId") Long tenantId);

}
