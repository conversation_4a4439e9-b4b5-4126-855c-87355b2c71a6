package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SettlementPaymentRecord;

import java.math.BigDecimal;
import java.util.List;

public interface SettlementPaymentRecordMapper {
    int insert(SettlementPaymentRecord record);

    int insertSelective(SettlementPaymentRecord record);

    int updateSelective(SettlementPaymentRecord record);

    List<SettlementPaymentRecord> selectBySettlementId(Integer settlementId);

    SettlementPaymentRecord selectById(Integer recordId);

    BigDecimal inSettleTotal();

    /**
     * 查询每日新增的待处理的结算单
     * @return
     */
    List<SettlementPaymentRecord> selectRemainNewHandle();

    List<SettlementPaymentRecord> selectInSettle();

    /**
     * 查询结算单结算已打款金额
     * @param settlementId
     * @return
     */
    BigDecimal selectStatus(Integer settlementId);
}