package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MsgTemplate;
import net.summerfarm.model.vo.MsgTemplateVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MsgTemplateMapper {

    List<MsgTemplate> select(MsgTemplate selectKeys);

    int updateByPrimaryKeySelective(MsgTemplate record);

    MsgTemplateVO selectOne(MsgTemplate selectKeys);
}