package net.summerfarm.mapper.product;


import net.summerfarm.model.ProductsSaleRule;
import net.summerfarm.model.param.ProductsSaleRuleQueryParam;
import net.summerfarm.model.vo.ProductsSaleRuleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-11-21 15:54:58
 * @version 1.0
 *
 */
@Mapper
public interface ProductsSaleRuleMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(ProductsSaleRule record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(ProductsSaleRule record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    ProductsSaleRule selectById(@Param("id") Long id);


    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    ProductsSaleRule selectByPdId(@Param("pdId") Long pdId);


    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<ProductsSaleRuleVO> getPage(ProductsSaleRuleQueryParam param);
}

