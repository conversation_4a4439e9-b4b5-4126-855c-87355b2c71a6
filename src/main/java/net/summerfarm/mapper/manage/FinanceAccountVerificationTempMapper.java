package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FinanceAccountVerification;
import net.summerfarm.model.vo.FinanceAccountTypeAmountVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface FinanceAccountVerificationTempMapper {

  int insertBatch(@Param("list") List<FinanceAccountVerification> list);


  void truncateTable();


  List<FinanceAccountTypeAmountVO> queryAmountByPurchaseListAndSupplier(@Param("list")List<String> purchaseNoList, @Param("supplierId")Integer supplierId, @Param("outType") Integer outType);

}
