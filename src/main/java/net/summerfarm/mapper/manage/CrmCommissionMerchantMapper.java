package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CrmCommissionMerchant;
import net.summerfarm.model.input.BatchModifyMerchantInput;
import net.summerfarm.model.vo.CrmCommissionMerchantVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmCommissionMerchantMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CrmCommissionMerchant record);

    int insertSelective(CrmCommissionMerchant record);

    CrmCommissionMerchant selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(BatchModifyMerchantInput record);

    int updateByPrimaryKey(CrmCommissionMerchant record);

    List<CrmCommissionMerchantVo> selectMerchant(@Param("zoneName") String zoneName);

    void copyMerchant(@Param("copyInfo") String copyInfo, @Param("info") String info, @Param("adminId") Integer adminId);

    CrmCommissionMerchant selectByZoneName(String zoneName);

    List<CrmCommissionMerchant> selectByPrimaryKeyList(@Param("ids") List<Integer> ids);

    void deleteByZoneName(String zoneName);
}