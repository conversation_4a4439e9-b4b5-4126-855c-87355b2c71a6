package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.GroupBuyOrderRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface GroupBuyOrderRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(GroupBuyOrderRecord record);

    int insertSelective(GroupBuyOrderRecord record);

    GroupBuyOrderRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GroupBuyOrderRecord record);

    int updateByPrimaryKey(GroupBuyOrderRecord record);

    /**
     * 查看已截单待更新任务
     * @param infoId 场次id
     * @return 场次信息
     */
    List<GroupBuyOrderRecord> selectByInfoId(@Param("infoId") Long infoId);

    /**
     * 统计团购金额
     * @param infoId infoId
     * @return 团购金额
     */
    BigDecimal sumGroupBuyInfo(@Param("infoId") Long infoId);
}