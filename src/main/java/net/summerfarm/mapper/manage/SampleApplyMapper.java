package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.DTO.tms.OldDistOrder;
import net.summerfarm.model.domain.SampleApply;
import net.summerfarm.model.domain.SampleSku;
import net.summerfarm.model.domain.StockTaskPick;
import net.summerfarm.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/4/24  10:57
 */
@Repository
public interface SampleApplyMapper {

    /**
    * 查询申请列表
    */
    @RequiresDataPermission(originalField = "sa.area_no")
    List<SampleApplyVO> selectSampleApplies(@Param("sa") SampleApply sampleApply, @Param("keyword") String keyword);

    int insertSampleApply(SampleApply sampleApply);

    int updateSampleApply(SampleApply sampleApply);

    SampleApply selectSampleById(Integer sampleId);

    List<SampleApplyVO> selectByAddTime(@Param("addTime") LocalDateTime addTime, @Param("endTime") LocalDateTime endTime);

    List<ClosingOrder> closingOrderBySample(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("areaNo") Integer areaNo, @Param("parentNo") Integer parentNo);

    List<ClosingOrder> closingOrderBySampleStatus(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("areaNo") Integer areaNo, @Param("parentNo") Integer parentNo);

    List<SampleSku> selectByDeliveryTime(@Param("areaNo") Integer areaNo, @Param("deliveryTime") LocalDate deliveryTime, @Param("trustStoreNo") Integer trustStoreNo);

    /**
    * 根据地址id 配送时间查询 且转换为订单项
    */
    List<OrderItemVO> selectByContactId(@Param("contactId") Long contactId, @Param("deliveryTime") LocalDate deliveryTime);


    /**
     * 取消样品申请
     * @param sampleId
     * @return
     */
    int cancelSampleApply(Integer sampleId);
    /**
     * 关闭样品申请
     * @param sampleId
     * @return
     */
    int closeSampleApply(Integer sampleId);
    /**
     * 获取出样捡货信息
     */
    List<StockTaskPick> selectSampleByType(@Param("areaNo") Integer areaNo, @Param("deliveryTime") LocalDate deliveryTime,
                                           @Param("trustStoreNo") Integer trustStoreNo, @Param("closeTime") String closeTime);

    List<ClosingOrder> selectSampleByAreaNo(MerchantVO vo);

    int updateSampleStoreNo(@Param("idList") List<Long> idList, @Param("storeNo") Integer storeNo);

    List<Integer> querySituationListTime(LocalDateTime endTime);

    /**
     * 查询出样出库冻结数量
     * @param warehouseNo
     * @param deliveryDate
     * @param sku
     * @return
     */
    Integer selectSampleQuantity(@Param("warehouseNo") Integer warehouseNo, @Param("deliveryDate") LocalDate deliveryDate,
                                 @Param("sku") String sku);

    /**
     * 查询出样出库冻结数量
     * @param storeNo
     * @param deliveryDate
     * @param sku
     * @return
     */
    Integer selectSampleQuantityByStoreNo(@Param("storeNo") Integer storeNo, @Param("deliveryDate") LocalDate deliveryDate,
                                 @Param("sku") String sku);

    /**
     * 查询出样出库冻结订单
     * @param warehouseNo
     * @param deliveryDate
     * @param sku
     * @return
     */
    List<SampleSkuVO> selectSampleOrders(@Param("warehouseNo") Integer warehouseNo, @Param("deliveryDate") LocalDate deliveryDate, @Param("sku") String sku);

    /**
     * 根据城配仓查询出样出库冻结订单
     * @param storeNo
     * @param deliveryDate
     * @param sku
     * @return
     */
    List<SampleSkuVO> selectSampleOrdersByStoreNo(@Param("storeNo") Integer storeNo, @Param("deliveryDate") LocalDate deliveryDate, @Param("sku") String sku);


    /**
     * 更新出样申请归属mid
     * @param oldMId
     * @param newMId
     * @return
     */
    int updateSampleByMId(@Param("oldMId") Long oldMId, @Param("newMId") Long newMId, @Param("mName") String mName);

    /**
     * 根据配送路线查询样品单
     * @param deliveryPathId
     * @return
     */
    List<LackGoodsOrderInfoVO> selectSampleByPathId(Integer deliveryPathId);

    /**
     * 根据配送时间和联系人id查询样品id集合
     * @param deliveryTime
     * @param contactId
     * @return
     */
    List<SampleSkuVO> getIdByDTAndCId(@Param("deliveryTime") LocalDate deliveryTime, @Param("contactId") Integer contactId);

    List<ClosingOrder> closingOrderBySampleByMid(Date startDate, Date endDate, Integer mId);

    /**
     * 查询样品截单数据信息
     * @param deliveryTime 配送时间
     * @param storeNo 城配仓编号
     * @return
     */
    List<ClosingOrder> tmsClosingOrderBySample(@Param("deliveryTime")Date deliveryTime,@Param("storeNo") Integer storeNo);

    /**
     * 查询指定配送日期之后的样品配送计划
     * @param storeNo 城配仓编号
     * @param deliveryDate 配送日期
     * @return 老模型委托单DTO集合
     */
    List<OldDistOrder> selectSampleDeliveryPlanByStoreNo(@Param("storeNo") Integer storeNo, @Param("deliveryDate") LocalDate deliveryDate);

    /**
     * 更新样品配送计划城配仓编号
     * @param sampleDeliveryPlanIds 样品配送计划ID
     * @param oldStoreNo 原城配仓编号
     * @param newStoreNo 新城配仓编号
     * @return 更新条数
     */
    int updateSampleDeliveryPlanStoreNo(@Param("sampleDeliveryPlanIds") List<Integer> sampleDeliveryPlanIds, @Param("oldStoreNo") Integer oldStoreNo, @Param("newStoreNo") Integer newStoreNo);

    /**
     * 更新样品运营服务区
     * @param sampleId 样品id
     * @param areaNo 新运营服务区编号
     */
    int updateSampleApplyArea(@Param("sampleId") Integer sampleId, @Param("areaNo") Integer areaNo);
}
