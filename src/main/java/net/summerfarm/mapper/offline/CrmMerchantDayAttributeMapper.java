package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.CrmMerchantDayAttribute;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface CrmMerchantDayAttributeMapper {

    /**
     * 查询商户属性信息
     * @param mId 商户id
     * @param dayTag 日期标记
     * @return 商户属性信息
     */
    CrmMerchantDayAttribute selectByPrimaryKey(@Param("mId") Long mId,@Param("dayTag") Integer dayTag);

}




