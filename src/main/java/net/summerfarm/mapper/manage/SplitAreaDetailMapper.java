package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SplitAreaDetail;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SplitAreaDetailMapper {

    /**
    * 插入
    */
    int saveSplitAreaDetail(SplitAreaDetail splitAreaDetail);

    /**
    * 修改
    */
    int updateSplitAreaDetail(SplitAreaDetail splitAreaDetail);


    /**
    *   批量插入
    */
    int saveBathSplitAreaDetail(List<SplitAreaDetail> list);

    /**
    * 查询详情
    */

    List<SplitAreaDetail> selectDetailBySplitId(Integer splitAreaId);


    /**
    * 批量删除（逻辑删除）
    */
    int deleteDetailList(List<Integer> list);
}
