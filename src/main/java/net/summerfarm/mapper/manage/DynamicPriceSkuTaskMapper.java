package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.domain.DynamicPriceSkuTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DynamicPriceSkuTaskMapper {
    
    int deleteByPrimaryKey(Long id);
    
    int insert(DynamicPriceSkuTask record);
    
    int insertSelective(DynamicPriceSkuTask record);
    
    DynamicPriceSkuTask selectByPrimaryKey(Long id);
    
    int updateByPrimaryKeySelective(DynamicPriceSkuTask record);
    
    int updateByPrimaryKey(DynamicPriceSkuTask record);

    List<DynamicPriceSkuTask> listByTaskId(@Param("taskId") Long taskId);
}