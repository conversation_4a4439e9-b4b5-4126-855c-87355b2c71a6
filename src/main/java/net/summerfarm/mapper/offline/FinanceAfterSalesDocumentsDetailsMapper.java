package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.FinanceAfterSalesDocumentsDetails;
import net.summerfarm.model.input.FinanceAfterSalesDocumentsDetailsInput;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface FinanceAfterSalesDocumentsDetailsMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinanceAfterSalesDocumentsDetails record);

    int insertSelective(FinanceAfterSalesDocumentsDetails record);

    FinanceAfterSalesDocumentsDetails selectByPrimaryKey(Long id);

    /**
     * 查询售后明细
     * @param financeAfterSalesDocumentsDetailsInput
     * @return
     */
    List<FinanceAfterSalesDocumentsDetails> selectByAll(FinanceAfterSalesDocumentsDetailsInput financeAfterSalesDocumentsDetailsInput);

    /**
     * 查询售后明细数据数量
     * @param financeAfterSalesDocumentsDetailsInput
     * @return
     */
    Integer selectByAllCount(FinanceAfterSalesDocumentsDetailsInput financeAfterSalesDocumentsDetailsInput);

    /**
     * 概况统计售后数据
     * @param financeAfterSalesDocumentsDetailsInput
     * @return
     */
    FinanceAfterSalesDocumentsDetails selectAfter(FinanceAfterSalesDocumentsDetailsInput financeAfterSalesDocumentsDetailsInput);

    int updateByPrimaryKeySelective(FinanceAfterSalesDocumentsDetails record);

    int updateByPrimaryKey(FinanceAfterSalesDocumentsDetails record);
}