package net.summerfarm.module;

import lombok.*;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * 商品对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Product {
    /**
     * 商品id
     */
    Long id;

    /**
     * 商品名称
     */
    String pdName;

    /**
     * sku
     */
    String sku;

    /**
     * 规格
     */
    String specification;

    /**
     * 类目id
     */
    Long categoryId;

    /**
     * 类目类型
     */
    Integer categoryType;

    /**
     * 三级类目名称
     */
    String category;

    /**
     * 二级类目名称
     */
    String secondCategory;

    /**
     * 一级类目名称
     */
    String firstCategory;

    /**
     * 储藏区域，温区
     */
    Integer temperature;

    /**
     * 包装
     */
    String packaging;

    /**
     * 保质期时长
     */
    Integer effectiveTime;

    /**
     * 保质期时长单位
     */
    String timeUnit;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;

    /**
     * 产地
     */
    String origin;

    /**
     * 是否为国产
     */
    Integer isDomestic;

    /**
     * 保质期类型
     */
    Integer qualityTimeType;

    /**
     * inventory表中的商品图片
     */
    String skuPic;

    /**
     * products表中的商品图片
     */
    String picPath;

    /**
     * 自营-代仓
     */
    Integer skuType;

    /**
     * inventory表的adminId
     */
    Long inventoryAdminId;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    Integer skuExeType;

    public String getPic() {
        if (StringUtils.isEmpty(skuPic)) {
            if (StringUtils.isNotEmpty(picPath)) {
                return picPath;
            }
            return "";
        }
        return skuPic;
    }

    /**
     * 体积
     */
    String volume;

    /**
     * 重量
     */
    BigDecimal weightNum;

    /**
     * 临保告警时长
     */
    Integer warnTime;

    /**
     * 贮存方式
     */
    private String storageMethod;

    /**
     * 商品介绍
     */
    private String slogan;

    /**
     * 其他介绍
     */
    private String otherSlogan;

    /**
     * 首页缩略图
     */
    private String picturePath;

    /**
     * 贮存区域
     */
    private Integer storageLocation;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 标记位-过时的spu  1代表过时，商品被删除
     */
    private Integer outdated;

    /**
     * 商品介绍信息
     */
    private String productIntroduction;

    private String refundType;

    /**
     * 保质期时长
     */
    private Integer qualityTime;

    /**
     * 保质期时长单位
     */
    private String qualityTimeUnit;

    /**
     * 创建人adminId
     */
    private Integer creator;

    /**
     * 上新类型：0、平台 1、大客户 2、帆台上新
     */
    private Integer createType;

    /**
     * 上新备注
     */
    private String createRemark;

    /**
     * 上新审核状态：0、待上新 1、上新成功 2、上新失败
     */
    private Integer auditStatus;

    /**
     * 上新审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 商品实物名
     */
    private String realName;

    /**
     * 操作人adminId
     */
    private Integer auditor;

    /**
     * 租户id
     */
    Long tenantId;

    /**
     * 归属
     */
    String belong;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    Integer subType;

    public Integer getEffectiveTimeForDay() {
        if (Objects.isNull(effectiveTime)) {
            return null;
        }
        if ("month".equals(timeUnit)) {
            return effectiveTime * 30;
        }
        return effectiveTime;
    }

    public String queryFirstCategoryName() {
        if (StringUtils.isNotEmpty(firstCategory)) {
            return firstCategory + "-";
        }
        return "";
    }

    public String querySecondCategoryName() {
        if (StringUtils.isNotEmpty(secondCategory)) {
            return secondCategory + "-";
        }
        return "";
    }

    public String queryThirdCategoryName() {
        if (StringUtils.isNotEmpty(category)) {
            return category;
        }
        return "";
    }

}
