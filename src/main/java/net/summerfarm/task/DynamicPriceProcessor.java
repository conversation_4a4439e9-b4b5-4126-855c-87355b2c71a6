package net.summerfarm.task;

import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.util.express.DefaultContext;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.enums.DynamicHandlerNameEnum;
import net.summerfarm.mapper.manage.DynamicPriceFieldConfigMapper;
import net.summerfarm.mapper.manage.DynamicPriceFieldMapper;
import net.summerfarm.mapper.manage.DynamicPriceModelConfigMapper;
import net.summerfarm.mapper.manage.DynamicPriceTaskMapper;
import net.summerfarm.mapper.manage.DynamicPriceWhiteListMapper;
import net.summerfarm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.mapper.offline.DynamicPriceStatisticsMapper;
import net.summerfarm.model.DTO.DynamicPriceStatisticsQueryDTO;
import net.summerfarm.model.DTO.inventory.DynamicPriceAdjustDTO;
import net.summerfarm.model.domain.DataSynchronizationInformation;
import net.summerfarm.model.domain.DynamicPriceField;
import net.summerfarm.model.domain.DynamicPriceFieldConfig;
import net.summerfarm.model.domain.DynamicPriceModelConfig;
import net.summerfarm.model.domain.DynamicPriceTask;
import net.summerfarm.model.domain.DynamicPriceWhiteList;
import net.summerfarm.model.domain.offline.DynamicPriceStatistics;
import net.summerfarm.service.bms.ExpressRunnerService;
import net.summerfarm.service.helper.dynamic.DynamicPriceFieldHandler;
import net.summerfarm.service.helper.dynamic.DynamicPriceServiceHelper;
import net.xianmu.common.exception.BizException;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 动态定价后台定时任务执行逻辑
 *
 * @author: <EMAIL>
 * @create: 2023/2/1
 */
@Slf4j
@Component
public class DynamicPriceProcessor extends XianMuJavaProcessor {

    @Resource
    private DynamicPriceServiceHelper dynamicPriceServiceHelper;

    @Resource
    private DynamicPriceFieldMapper dynamicPriceFieldMapper;

    @Resource
    private DynamicPriceWhiteListMapper dynamicPriceWhiteListMapper;

    @Resource
    private DynamicPriceFieldConfigMapper dynamicPriceFieldConfigMapper;

    @Resource
    private DynamicPriceModelConfigMapper dynamicPriceModelConfigMapper;

    @Resource
    private DynamicPriceTaskMapper dynamicPriceTaskMapper;

    @Resource
    private DynamicPriceStatisticsMapper dynamicPriceStatisticsMapper;

    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    @Resource
    private ExpressRunnerService expressRunnerService;

    @Resource
    private ApplicationContext applicationContext;

    private static final ExecutorService EXECUTOR_SERVICE = new ThreadPoolExecutor(4, 8,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(),
            new NamedThreadFactory("动态定价-", false),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public ProcessResult processResult(JobContext context) {
        try {
            StopWatch stopWatch = StopWatch.createStarted();
            dealWithModel();
            stopWatch.stop();
            log.info("【动态定价】执行定价定时任务完成,总计耗时:{}s", stopWatch.getTime(TimeUnit.SECONDS));
        } catch (Exception e) {
            log.error("【动态定价】执行定价模型异常, cause:{}", Throwables.getStackTraceAsString(e));
            return new ProcessResult(false);
        }
        return new ProcessResult(true);
    }

    public void dealWithModel() throws Exception {
        //获取当前时间点，先查询模型
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:00");
        String hourStr = sdf.format(now);
        List<DynamicPriceModelConfig> modelConfigs = dynamicPriceModelConfigMapper.listByExeTime(
                hourStr, null);
        if (CollectionUtils.isEmpty(modelConfigs) || modelConfigs.size() > 2) {
            log.warn("【动态定价】当前时间点:{}未配置定价模型,或者配置模型数量异常,size:{}", hourStr, modelConfigs.size());
            return;
        }

        //查看是否有当天的执行记录，只要有一条记录，就表示在执行中（或者用redis分布式锁，防止重复执行）
        SimpleDateFormat sdfHour = new SimpleDateFormat("yyyy-MM-dd HH:00:00");

        String nowHourStr = sdfHour.format(now);

        Date nowHour = sdfHour.parse(nowHourStr);
        int count = dynamicPriceTaskMapper.countByTaskExeTime(nowHour);
        if (count > 0) {
            log.warn("【动态定价】当前时间点:{}已经有任务执行记录，不再执行", hourStr);
            return;
        }

        //先判断离线数据是否已经产出
        DataSynchronizationInformation syncInfo = dataSynchronizationInformationMapper.selectByTableName(
                BaseConstant.DYNAMIC_PRICE_STATISTICS);
        //判断时间是否前一天的数据
        Integer dateFlag = syncInfo.getDateFlag();
        String dateStr = DateUtil.formatYmdWithOutSplitDate(LocalDate.now().minusDays(1));
        //信号表时间不是前一天，则跳过同步处理
        if (!Objects.equals(dateStr, String.valueOf(dateFlag))) {
            log.warn("【动态定价】离线表dynamic_price_statistics数据非当天最新数据，稍后重试！");
            return;
        }

        //按鲜果和非鲜果去执行模型
        Map<Integer, DynamicPriceModelConfig> categoryTypeMap = modelConfigs.stream()
                .collect(Collectors.toMap(x -> x.getCategoryType(), Function.identity()));

        //每小时查询执行时间点的任务，需要判断是否有已执行或者在执行中，确保幂等性
        for (Entry<Integer, DynamicPriceModelConfig> entry : categoryTypeMap.entrySet()) {
            dealWithByCategoryType(entry.getValue(), nowHour);
        }


    }

    /**
     * 按类目类型处理
     *
     * @param modelConfig
     */
    private void dealWithByCategoryType(DynamicPriceModelConfig modelConfig, Date nowHour)
            throws Exception {
        Integer categoryType = modelConfig.getCategoryType();
        //获取鲜果或者非鲜果白名单
        List<DynamicPriceWhiteList> whiteLists = dynamicPriceWhiteListMapper.listByCategoryType(
                categoryType);
        Map<Integer, List<String>> warehouseNoSkusMap = whiteLists.stream().collect(
                Collectors.toMap(x -> x.getWarehouseNo(), x -> Lists.newArrayList(x.getSku()),
                        (a, b) -> {
                            a.addAll(b);
                            return a;
                        }));
        Long modelConfigId = modelConfig.getId();
        //获取到当前模型的字段配置公式
        List<DynamicPriceFieldConfig> fieldConfigs = dynamicPriceFieldConfigMapper.listByModelConfigId(
                modelConfigId);
        List<Long> fieldIds = fieldConfigs.stream().map(x -> x.getFieldId()).distinct()
                .collect(Collectors.toList());

        //获取配置字段信息
        List<DynamicPriceField> priceFields = dynamicPriceFieldMapper.listByIds(fieldIds);
        Map<Long, DynamicPriceField> priceFieldMap = priceFields.stream()
                .collect(Collectors.toMap(x -> x.getId(), Function.identity()));

        String formulaStr = Arrays.stream(modelConfig.getFormula().split(","))
                .collect(Collectors.joining());
        //开始计算字段
        //按库存仓维度来处理sku
        for (Entry<Integer, List<String>> entry : warehouseNoSkusMap.entrySet()) {
            Integer warehouseNo = entry.getKey();
            List<String> skus = entry.getValue();
            DynamicPriceTask priceTask = new DynamicPriceTask();
            priceTask.setCategoryType(categoryType);
            priceTask.setStatus(0);
            priceTask.setModelConfigId(modelConfig.getId());
            priceTask.setWarehouseNo(warehouseNo);
            priceTask.setTaskExeTime(nowHour);
            dynamicPriceTaskMapper.insertSelective(priceTask);
            List<Future<Boolean>> listSubmit = Lists.newArrayList();
            for (String sku : skus) {
                DynamicPriceAdjustDTO dynamicPriceAdjustDTO = new DynamicPriceAdjustDTO();
                dynamicPriceAdjustDTO.setPriceFields(priceFields);
                dynamicPriceAdjustDTO.setWarehouseNo(warehouseNo);
                dynamicPriceAdjustDTO.setSku(sku);
                dynamicPriceAdjustDTO.setCategoryType(categoryType);
                dynamicPriceAdjustDTO.setTaskId(priceTask.getId());
                dynamicPriceAdjustDTO.setUpperLimit(modelConfig.getUpperLimit());
                Future<Boolean> submit = EXECUTOR_SERVICE.submit(() -> {
                    try {
                        Map<String, BigDecimal> fieldValue = calFieldValue(fieldConfigs,
                                priceFieldMap, dynamicPriceAdjustDTO);
                        if (fieldValue == null) {
                            return false;
                        }
                        DefaultContext<String, Object> fieldValueMap = new DefaultContext<>();
                        fieldValueMap.putAll(fieldValue);
                        //计算不包含毛利率的公式部分
                        BigDecimal calculation = expressRunnerService.calculation(formulaStr,
                                fieldValueMap);
                        log.info("【动态定价】计算不包含毛利率的公式部分,formulaStr:{},fieldValueMap:{},calculation:{}",
                                formulaStr, fieldValueMap, calculation);
                        if (calculation == null) {
                            calculation = BigDecimal.ONE;
                        }

                        dynamicPriceAdjustDTO.setCalculation(calculation);
                        dynamicPriceServiceHelper.adjustPrice(dynamicPriceAdjustDTO, fieldValue);
                        log.info("【动态定价】系统自动调价完成,dynamicPriceAdjustDTO={},fieldValue={}",
                                JSON.toJSONString(dynamicPriceAdjustDTO), fieldValue);
                        return true;
                    } catch (Exception e) {
                        //不能因为异常影响到其他的调价
                        log.error("【动态定价】系统自动调价异常,dynamicPriceAdjustDTO={},cause={}",
                                JSON.toJSONString(dynamicPriceAdjustDTO),
                                Throwables.getStackTraceAsString(e));
                        return false;
                    }
                });
                listSubmit.add(submit);
            }
            for (Future<Boolean> future : listSubmit) {
                future.get();
            }

            dynamicPriceTaskMapper.updateStatus(1);
        }
    }

    /**
     * 计算字段值
     */
    private Map<String, BigDecimal> calFieldValue(List<DynamicPriceFieldConfig> fieldConfigs,
            Map<Long, DynamicPriceField> priceFieldMap,
            DynamicPriceAdjustDTO dynamicPriceAdjustDTO) {
        Map<String, BigDecimal> factorMap = Maps.newHashMap();
        String sku = dynamicPriceAdjustDTO.getSku();
        Integer warehouseNo = dynamicPriceAdjustDTO.getWarehouseNo();
        DynamicPriceStatisticsQueryDTO queryDTO = new DynamicPriceStatisticsQueryDTO();
        queryDTO.setSku(sku);
        queryDTO.setWarehouseNo(warehouseNo);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateFlagStr = sdf.format(DateUtils.addDays(new Date(), -1));
        queryDTO.setDateFlag(Integer.valueOf(dateFlagStr));
        List<DynamicPriceStatistics> priceStatistics = dynamicPriceStatisticsMapper.selectByQuery(
                queryDTO);
        if (CollectionUtils.isEmpty(priceStatistics)) {
            return null;
        }
        Map<Long, List<DynamicPriceFieldConfig>> fieldConfigListMap = fieldConfigs.stream().collect(
                Collectors.toMap(x -> x.getFieldId(), x -> Lists.newArrayList(x), (a, b) -> {
                    a.addAll(b);
                    return a;
                }));
        for (Entry<Long, List<DynamicPriceFieldConfig>> entry : fieldConfigListMap.entrySet()) {
            //获取对应字段的处理器
            DynamicPriceField priceField = priceFieldMap.get(entry.getKey());

            String handlerName = DynamicHandlerNameEnum.getHandlerName(priceField.getFieldAlias());
            if (handlerName == null) {
                throw new BizException(
                        "【动态定价】获取字段处理器失败,priceField=" + JSON.toJSONString(priceField));
            }
            String[] split = priceField.getFieldAlias().split("_");
            String limitStr = split[split.length - 1].replace("d", "");
            dynamicPriceAdjustDTO.setLimit(Integer.valueOf(limitStr));
            DynamicPriceFieldHandler priceFieldHandler = (DynamicPriceFieldHandler) applicationContext.getBean(
                    handlerName);
            BigDecimal factorValue = priceFieldHandler.handler(priceStatistics,
                    entry.getValue(), dynamicPriceAdjustDTO);
            factorMap.put(priceField.getFieldAlias(),
                    factorValue.setScale(2, RoundingMode.HALF_UP));
        }

        return factorMap;
    }


}
