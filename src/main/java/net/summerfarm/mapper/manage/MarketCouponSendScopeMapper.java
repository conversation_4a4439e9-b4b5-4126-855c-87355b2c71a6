package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MarketCouponSendScope;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MarketCouponSendScopeMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MarketCouponSendScope record);

    int insertSelective(MarketCouponSendScope record);

    MarketCouponSendScope selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MarketCouponSendScope record);

    int updateByPrimaryKey(MarketCouponSendScope record);

    int insertBatch(@Param("list") List<MarketCouponSendScope> marketCouponSendScopes);

    List<MarketCouponSendScope> selectBySendId(Long sendId);
}