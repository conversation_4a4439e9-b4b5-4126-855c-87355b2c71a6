package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.ScheduleJob;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wjd on 2017/9/26.
 */
@Repository
public interface ScheduleJobMapper {

    List<ScheduleJob> select(ScheduleJob job);

    ScheduleJob selectById(int id);

    void insert(ScheduleJob job);

    void update(ScheduleJob job);

}
