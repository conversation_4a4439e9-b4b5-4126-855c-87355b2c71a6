package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DepartmentDetails;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> by 2021/04/28
 */
@Repository
public interface DepartmentDetailsMapper {

    int insert(DepartmentDetails departmentDetails);

    List<DepartmentDetails> selectByDept();

    List<DepartmentDetails> selectByName(Long deptId);

    int update(DepartmentDetails departmentDetails);

    int updateDeptStatus();

    /**
     * 根据部门id找出所属部门所有id
     * @param deptId
     * @return
     */
    List<Long> selectById(Long deptId);

    /**
     * 批量查询
     * @param deptIdList
     * @return
     */
    List<Long> selectByIdList(List<Long> deptIdList);

}
