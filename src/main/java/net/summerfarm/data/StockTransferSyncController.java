package net.summerfarm.data;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.dao.stockTransfer.StockTransferDAO;
import net.summerfarm.dao.stockTransfer.StockTransferItemDAO;
import net.summerfarm.dao.stockTransfer.StockTransferItemOpDAO;
import net.summerfarm.dao.stockTransfer.StockTransferItemOpDetailDAO;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferDO;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferItemDO;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferItemOpDO;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferItemOpDetailDO;
import net.summerfarm.data.dto.StockTaskProcessDetailDTO;
import net.summerfarm.data.dto.TransferSyncDTO;
import net.summerfarm.enums.StockTaskType;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.StockTaskProcessDetailVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/stockTransfer/data")
@Slf4j
public class StockTransferSyncController {

    @Resource
    private StockTaskMapper stockTaskMapper;

    @Resource
    private StockTaskItemMapper stockTaskItemMapper;

    @Resource
    private StockTaskItemDetailMapper stockTaskItemDetailMapper;

    @Resource
    private StockTaskProcessDetailMapper stockTaskProcessDetailMapper;

    @Resource
    private StockTaskProcessMapper stockTaskProcessMapper;

    @Resource
    private StockTransferDAO stockTransferDAO;

    @Resource
    private StockTransferItemDAO stockTransferItemDAO;

    @Resource
    private StockTransferItemOpDAO stockTransferItemOpDAO;

    @Resource
    private StockTransferItemOpDetailDAO stockTransferItemOpDetailDAO;

    @Resource
    private StoreRecordMapper storeRecordMapper;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private StockTakingMapper stockTakingMapper;

    @Resource
    private StockTakingItemMapper stockTakingItemMapper;

    @Resource
    private StockTakingListMapper stockTakingListMapper;

    @Resource
    private StockTakingListDetailMapper stockTakingListDetailMapper;

    @PostMapping(value = "/stocktaking/repair")
    public void stocktaking(){
        StockTaking stockTaking = stockTakingMapper.selectById(2386);
        String stockTakingNo = stockTaking.getStockTakingNo();
        StockTask stockTask = stockTaskMapper.selectOne(stockTakingNo, StockTaskType.PART_STOCK_TAKING.getId());
    }


    @PostMapping(value = "/repair")
    public void repair() {
        new Thread(() -> {
            log.info("开始混合批次日期数据初始化");
            List<StockTransferItemOpDO> opDOS = stockTransferItemOpDAO.selectByType();
            opDOS.forEach(item -> {
                try {
                    log.info("初始化数据id为:{}", item.getId());
                    StockTransferItemDO stockTransferItemDO = stockTransferItemDAO.selectById(item.getStockTransferItemId());
                    StockTransferDO transferDO = stockTransferDAO.selectById(stockTransferItemDO.getStockTransferId());
                    List<StockTransferItemOpDetailDO> opDetailDOS = stockTransferItemOpDetailDAO.listByOpIds(Lists.newArrayList(item.getId()));
                    Set<LocalDate> used = Sets.newConcurrentHashSet();
                    Set<String> usedb = Sets.newConcurrentHashSet();
                    opDetailDOS.forEach(detail -> {
                        List<StoreRecord> storeRecords = storeRecordMapper.selectByTime(detail.getTransferOutBatch(), transferDO.getWarehouseNo().intValue(), detail.getCreatedAt());
                        StoreRecord storeRecord = storeRecords.stream()
                                .filter(storeRecord1 -> !used.contains(storeRecord1.getProductionDate()) || !usedb.contains(detail.getTransferOutBatch()))
                                .findFirst().get();
                        log.info("查询到的记录:{}", JSONObject.toJSONString(storeRecord));
                        stockTransferItemOpDetailDAO.updateForInit(detail.getId(), DateUtil.toMill(storeRecord.getProductionDate()),
                                DateUtil.toMill(storeRecord.getQualityDate()));
                        used.add(storeRecord.getProductionDate());
                        usedb.add(detail.getTransferOutBatch());
                    });
                    log.info("初始化结束数据id为:{}", item.getId());
                } catch (Exception e) {
                    log.info("混合批次初始化异常opId:{}", item.getId(), e);
                }
            });
            log.info("结束混合批次日期数据初始化");
        }).start();


    }

    @PostMapping(value = "/repair/data")
    public void repairData(@RequestBody TransferSyncDTO transferSyncDTO) {
        new Thread(() -> {
            log.info("开始修复数据:{}", transferSyncDTO);
            long maxId = transferSyncDTO.getMaxId();
            Long beginId = transferSyncDTO.getBeginId();
            Long id = transferSyncDTO.getId();
            Integer pageNum = 1;
            while (true) {
                PageInfo<StockTransferDO> page = PageHelper.startPage(pageNum, 10).doSelectPageInfo(() ->
                        stockTransferDAO.repairData(maxId, beginId, id));
                List<StockTransferDO> list = page.getList();
                if (CollectionUtils.isEmpty(list)) {
                    log.info("已无可修复数据,数据修复结束");
                    break;
                }

                list.forEach(item -> {
                    try {
                        Long stockTransferId = item.getId();
                        Date createdAt = item.getCreatedAt();
                        Date updatedAt = item.getUpdatedAt();
                        List<StockTask> stockTasks = stockTaskMapper.selectByTime(createdAt, updatedAt);
                        if (CollectionUtils.isEmpty(stockTasks)) {
                            log.info("根据时间查不到taskid:{},createAt:{},update:{}", item.getId(), createdAt, updatedAt);
                            return;
                        }
                        if (stockTasks.size() > 1) {
                            log.info("该时间查询到多条:{}", stockTasks.stream().map(StockTask::getId).collect(Collectors.toList()));
                            return;
                        }
                        StockTask stockTask = stockTasks.get(0);
                        StockTaskItem stockTaskItem = new StockTaskItem();
                        stockTaskItem.setStockTaskId(stockTask.getId());
                        List<StockTaskItem> select = stockTaskItemMapper.select(stockTaskItem);
                        List<StockTransferItemDO> itemDos = stockTransferItemDAO.listByStockTransferId(stockTransferId);
                        select.forEach(taskItem -> {
                            String inSku = taskItem.getSku();
                            StockTransferItemDO stockTransferItemDO = itemDos.stream().filter(i -> i.getTransferInSku().equals(inSku)).findFirst().orElse(null);
                            if (Objects.isNull(stockTransferItemDO)) {
                                log.info("无sku可匹配上的实例,stockTaskId:{}", taskItem.getStockTaskId());
                                return;
                            }
                            Long itemId = stockTransferItemDO.getId();
                            stockTransferItemOpDAO.delete(itemId);

                            List<StockTaskProcessDetailDTO> stockTaskProcessDetailVOS = stockTaskProcessDetailMapper.selectTransferByProcessIdForSync(taskItem.getStockTaskId());
                            stockTaskProcessDetailVOS.stream()
                                    .filter(detail -> inSku.equals(detail.getSku()))
                                    .forEach(vo -> {
                                        String adminId = null;
                                        Admin admin = adminMapper.selectByRealNameNew(vo.getCreator());
                                        if (Objects.nonNull(admin)) {
                                            adminId = String.valueOf(admin.getAdminId());
                                        }
                                        Date addTime = vo.getCreatedAt();
                                        StockTransferItemOpDO build2 = StockTransferItemOpDO.builder()
                                                .stockTransferItemId(itemId)
                                                .transferOutSku(vo.getTransferSku())
                                                .transferRatio(vo.getTransferScale())
                                                .shelfLife(Objects.isNull(vo.getQualityDate()) ? 0L : DateUtil.toMill(vo.getQualityDate()))
                                                .produceDate(Objects.isNull(vo.getProductionDate()) ? 0L : DateUtil.toMill(vo.getProductionDate()))
                                                .operator(adminId)
                                                .createdAt(Objects.isNull(addTime) ?
                                                        DateUtil.toDate(DateUtil.toLocalDate(1577808000000L)) :
                                                        addTime)
                                                .updatedAt(Objects.isNull(addTime) ?
                                                        DateUtil.toDate(DateUtil.toLocalDate(1577808000000L)) :
                                                        addTime)
                                                .type(0)
                                                .build();
                                        stockTransferItemOpDAO.insert(build2);
                                        stockTransferItemOpDetailDAO.insert(StockTransferItemOpDetailDO.builder()
                                                .stockTransferItemOpId(build2.getId())
                                                .transferInBatch(StringUtils.isEmpty(vo.getTransferListNo()) ? "9999999" : vo.getTransferListNo())
                                                .transferOutBatch(vo.getListNo())
                                                .transferOutNum(vo.getTransferQuantity().longValue())
                                                .createdAt(Objects.isNull(addTime) ?
                                                        DateUtil.toDate(DateUtil.toLocalDate(1577808000000L)) :
                                                        addTime)
                                                .updatedAt(Objects.isNull(addTime) ?
                                                        DateUtil.toDate(DateUtil.toLocalDate(1577808000000L)) :
                                                        addTime)
                                                .build());
                                    });
                        });
                    } catch (Exception e) {
                        log.info("刷新异常id:{}", item.getId());
                    }
                });
                log.info("已经刷完了的id:{}", list.stream().map(StockTransferDO::getId).max(Long::compareTo).get());
                pageNum++;
            }
            log.info("开始修复数据结束");
        }).start();
    }

    @PostMapping(value = "/sync")
    public void sync(@RequestBody TransferSyncDTO transferSyncDTO) {

        new Thread(() -> {
            Integer pageNum = 1;
            while (true) {
                try {
                    PageInfo<StockTask> objectPageInfo = PageHelper.startPage(pageNum, 10).doSelectPageInfo(() ->
                            stockTaskMapper.allData(transferSyncDTO.getIds(), transferSyncDTO.getType(), transferSyncDTO.getBeginId()));
                    List<StockTask> list = objectPageInfo.getList();
                    if (CollectionUtils.isEmpty(list)) {
                        log.info("数据为空,刷新结束");
                        break;
                    }

                    list.forEach(task -> {
                        log.info("老数据id:{}", task.getId());
                        try {
                            StockTransferDO build = StockTransferDO.builder()
                                    .warehouseNo(task.getAreaNo().longValue())
                                    .state(task.getState() == 2 ? 3 : task.getState())
                                    .remark(task.getRemark())
                                    .transferDimension(task.getDimension())
                                    .operator(Objects.nonNull(task.getAdminId()) && task.getAdminId() > 0 ? String.valueOf(task.getAdminId()) : null)
                                    .createdAt(Objects.isNull(task.getAddtime()) ? null : DateUtil.toDate(task.getAddtime()))
                                    .updatedAt(Objects.isNull(task.getUpdatetime()) ? null : DateUtil.toDate(task.getUpdatetime()))
                                    .build();
                            log.info("插入的数据为:{}", build);
                            stockTransferDAO.insertStockTransfer(build);
                            StockTaskItem stockTaskItem = new StockTaskItem();
                            stockTaskItem.setStockTaskId(task.getId());
                            List<StockTaskItem> select = stockTaskItemMapper.select(stockTaskItem);
                            select.forEach(item -> {
                                StockTransferItemDO build1 = StockTransferItemDO.builder()
                                        .stockTransferId(build.getId())
                                        .preTransferInNum(item.getQuantity().longValue())
                                        .transferInSku(item.getSku())
                                        .createdAt(Objects.isNull(task.getAddtime()) ? null : DateUtil.toDate(task.getAddtime()))
                                        .updatedAt(Objects.isNull(task.getUpdatetime()) ? null : DateUtil.toDate(task.getUpdatetime()))
                                        .build();
                                stockTransferItemDAO.batchInsert(Lists.newArrayList(build1));
                                List<StockTaskProcessDetailVO> stockTaskProcessDetailVOS = stockTaskProcessDetailMapper.selectTransferByProcessId(task.getId());
                                stockTaskProcessDetailVOS.forEach(vo -> {
                                    log.info("迁移vo:{}", JSON.toJSONString(vo));
                                    String adminId = null;
                                    Admin admin = adminMapper.selectByRealNameNew(vo.getCreator());
                                    if (Objects.nonNull(admin)) {
                                        adminId = String.valueOf(admin.getAdminId());
                                    }
                                    StockTransferItemOpDO build2 = StockTransferItemOpDO.builder()
                                            .stockTransferItemId(build1.getId())
                                            .transferOutSku(vo.getTransferSku())
                                            .transferRatio(vo.getTransferScale())
                                            .shelfLife(Objects.isNull(vo.getQualityDate()) ? 0L : DateUtil.toMill(vo.getQualityDate()))
                                            .produceDate(Objects.isNull(vo.getProductionDate()) ? 0L : DateUtil.toMill(vo.getProductionDate()))
                                            .operator(adminId)
                                            .createdAt(DateUtil.toDate(vo.getAddTime()))
                                            .updatedAt(DateUtil.toDate(vo.getAddTime()))
                                            .type(0)
                                            .build();
                                    stockTransferItemOpDAO.insert(build2);
                                    stockTransferItemOpDetailDAO.insert(StockTransferItemOpDetailDO.builder()
                                            .stockTransferItemOpId(build2.getId())
                                            .transferInBatch(StringUtils.isEmpty(vo.getTransferListNo()) ? "9999999" : vo.getTransferListNo())
                                            .transferOutBatch(vo.getListNo())
                                            .transferOutNum(vo.getTransferQuantity().longValue())
                                            .createdAt(Objects.isNull(vo.getCreateTime()) ? null : DateUtil.toDate(vo.getCreateTime()))
                                            .updatedAt(Objects.isNull(vo.getCreateTime()) ? null : DateUtil.toDate(vo.getCreateTime()))
                                            .build());
                                });
                                try {
                                    Thread.sleep(200L);
                                } catch (InterruptedException e) {
                                    log.info("sleep异常", e);
                                }
                            });
                        } catch (Exception e) {
                            log.info("单条刷新异常", e);
                        }
                    });
                    log.info("当前页数数量:{},当前页:{}", objectPageInfo.getSize(), pageNum);
                    if (objectPageInfo.getSize() < 10) {
                        break;
                    }
                    log.info("已经刷完了的id:{}", list.stream().map(StockTask::getId).max(Integer::compareTo).get());
                } catch (Exception e) {
                    log.info("部份刷新异常", e);
                }
                pageNum++;
            }
            log.info("刷新结束");

        }).start();
    }
}
