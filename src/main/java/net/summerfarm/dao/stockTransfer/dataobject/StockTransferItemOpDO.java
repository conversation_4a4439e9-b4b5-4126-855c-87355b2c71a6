package net.summerfarm.dao.stockTransfer.dataobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockTransferItemOpDO {
    /**
     * 主键id
     */
    Long id;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;

    /**
     * 关联表id
     */
    Long stockTransferItemId;

    /**
     * 转换类型
     * 0-单一，1-混合
     */
    @Builder.Default
    Integer type = 0;

    /**
     * 转换比例
     */
    String transferRatio;

    /**
     * 转出sku
     */
    String transferOutSku;

    /**
     * 转入批次的生产日期
     */
    Long produceDate;

    /**
     * 转入批次的保质期
     */
    Long shelfLife;

    /**
     * 操作人
     */
    String operator;

//    public String calTransferRatio() {
//        String[] split = transferRatio.split(":");
//        int commonDivisor = getCommonDivisor(Integer.parseInt(split[0]), Integer.parseInt(split[1]));
//        return Integer.parseInt(split[0]) / commonDivisor + ":" + Integer.parseInt(split[1]) / commonDivisor;
//    }
//
//    private int getCommonDivisor(int a, int b) {
//        while (b != 0) {
//            int temp = a % b;
//            a = b;
//            b = temp;
//        }
//        return a;
//    }
}
