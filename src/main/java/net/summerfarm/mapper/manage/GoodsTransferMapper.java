package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.GoodsTransfer;
import net.summerfarm.model.vo.GoodsLocationDetailVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/4/7  17:37
 */
@Repository
public interface GoodsTransferMapper {

    int insertGoodsTransfer(GoodsTransfer goodsTransfer);

    GoodsTransfer selectGoodsTransfer(Integer id);

    List<GoodsTransfer> selectGoodsTransferList(GoodsLocationDetailVO goodsLocationDetailVO);
}
