package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CrmBdConfig;
import net.summerfarm.model.domain.MajorRebate;
import net.summerfarm.model.input.BatchModifyIncentiveIndexInput;
import net.summerfarm.model.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface CrmBdConfigMapper {
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入激励指标
     * @param record 插入信息
     * @return 插入成功与否
     */
    int insertSelective(CrmBdConfig record);

    CrmBdConfig selectByPrimaryKey(Integer id);

    /**
     * 修改激励指标
     * @param record 修改内容
     * @return 成功与否
     */
    int updateByPrimaryKeySelective(BatchModifyIncentiveIndexInput record);

    int updateByPrimaryKey(CrmBdConfig record);

    /**
     * 获取销售激励指标
     * @param area 区域no
     * @param adminName 销售名
     * @return 销售激励指标
     */
    List<CrmBdConfigVo> selectIncentiveIndex(@Param("area") List<Integer> area,@Param("adminName") String adminName);

    /**
     * 复制信息并插入一条新的
     * @param copyIntInfo 被复制人id
     * @param intInfo 新增销售id
     * @param adminId 操作人id
     */
    void copyIncentiveIndex(@Param("copyIntInfo") Integer copyIntInfo,@Param("intInfo") Integer intInfo,@Param("adminId") Integer adminId);

    List<String> queryZoneNameCondition(@Param("tableName") String tableName, @Param("isExist") Boolean isExist,@Param("zoneName") String zoneName);

    List<ValuesVo> queryProductName(@Param("name") String name,@Param("sku") Integer sku);

    List<QuerySkuVo> querySku(@Param("name") String skuName,@Param("id") Integer id);

    List<ValuesVo> queryBdName(@Param("isExist") Boolean isExist,@Param("bdName") String bdName,@Param("baseUserIds") List<Long> baseUserIds);

    List<ZoneNameListVo> selectZoneNameList();

    /**
     * 根据区域id获取下属城市
     * @param id 区域id
     * @return 下属城市列表
     */
    List<ZoneNameListVo> selectselectZoneNameChildrenList(Integer id);

    BigDecimal selectLastMonthGmv(@Param("adminId") Integer adminId,@Param("skuCommission") List<String> skuCommission);

    /**
     * 复制信息并插入一条新的
     * @param copyIntInfo 被复制人id
     * @param intInfo 新增销售id
     * @param adminId 操作人id
     * @param lastMonthNum 上月核心客户数
     */
    void copyIncentiveIndexLastMonthGmv(@Param("copyIntInfo") Integer copyIntInfo,@Param("intInfo") Integer intInfo,@Param("adminId") Integer adminId,@Param("lastMonthNum") Integer lastMonthNum);

    /**
     * 查询销售配置信息
     * @param adminId 销售id
     * @return 销售配置信息
     */
    CrmBdConfig selectByAdminId(Integer adminId);

    List<CrmBdConfig> selectBdConfigByType();

    List<AreaResultVo> selectLogisticsAreaResult();

    List<MajorRebate> selectBdArea(Integer adminId);
    /**
     * 修改激励指标
     * @param record 修改内容
     * @return 成功与否
     */
    int updateByAdminId(BatchModifyIncentiveIndexInput record);
}