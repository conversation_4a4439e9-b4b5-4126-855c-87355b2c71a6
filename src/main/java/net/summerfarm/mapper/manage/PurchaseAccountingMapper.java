package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PurchaseAccounting;
import net.summerfarm.model.vo.PurchaseAccountingVO;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/3/11  10:36
 */
@Repository
public interface PurchaseAccountingMapper {

    /**
     * 新增核算单
     * @param purchaseAccounting
     * @return
    */
    int createPurchaseAccount(PurchaseAccounting purchaseAccounting);
    /**
     * 查询核算单
     * @param id
     * @return
     */
    PurchaseAccounting selectById(Integer id);

    /**
     * 更新核算单信息
     * @param purchaseAccounting
     * @return
    */
    int update(PurchaseAccounting purchaseAccounting);

    /**
     * 查询审核成功，待审核，待审批的 总金额
     * @return
    */
    BigDecimal selectTotalAmount();

    /**
    * 查询
     * @param purchaseAccounting
     * @return
    */
    List<PurchaseAccounting> selectPurchaseAccount(PurchaseAccountingVO purchaseAccounting);


    /**
    * 根据id查询
     * @param id
     * @return
    */
    PurchaseAccountingVO selectByAccountId(Integer id);
}
