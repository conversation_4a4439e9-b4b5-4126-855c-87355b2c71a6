package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MsgAdmin;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MsgAdminMapper {

    int insertSelective(MsgAdmin record);

    MsgAdmin selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(MsgAdmin record);

    List<String> selectCCRealname(int msgBodyId);

    MsgAdmin selectOne(MsgAdmin selectKeys);

    List<MsgAdmin> select(MsgAdmin selectKeys);

    /**
     *  查询所有未处理的售罄及采购预警消息(只查接收人，不查转发人)
     * @return
     */
    List<MsgAdmin> selectUnFinishMsg();
}