package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DeliveryPlanExtend;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeliveryPlanExtendMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DeliveryPlanExtend record);

    int insertSelective(DeliveryPlanExtend record);

    DeliveryPlanExtend selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeliveryPlanExtend record);

    int updateByPrimaryKey(DeliveryPlanExtend record);

    DeliveryPlanExtend selectByDeliveryPlanId(@Param("deliveryPlanId")Integer deliveryPlanId);

    /**
     * 批量插入
     * @param deliveryPlanExtendList
     * @return
     */
    int insertBatch(List<DeliveryPlanExtend> deliveryPlanExtendList);
}