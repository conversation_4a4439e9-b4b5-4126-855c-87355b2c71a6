package net.summerfarm.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.config.model.NotifyConfigBO;
import net.summerfarm.config.model.ProcessMigrateBO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2023/5/16 11:07
 */
@Slf4j
@Component
public class DynamicConfig {

    @NacosValue(value = "${process.migrate:[]}", autoRefreshed = true)
    private String processSwitch;

    @NacosValue(value = "${bms.callback.migrate:[]}", autoRefreshed = true)
    private String bmsCallBackMigrate;

    @NacosValue(value = "${fms.bms.upload.switch:true}", autoRefreshed = true)
    private Boolean uploadSwitch;



    @NacosValue(value = "${fms.payment.create.switch:true}", autoRefreshed = true)
    private Boolean fmsPaymentCreateSwitch;

    public Boolean getFmsPaymentCreateSwitch() {
        return fmsPaymentCreateSwitch;
    }

    @NacosValue(value = "${bms.trunk.offline:true}", autoRefreshed = true)
    private Boolean bmsTrunkSwitch;

    @NacosValue(value = "${common.risk.notify:[{\"key\":\"CALL_BACK_NOTIFY\",\"url\":\"https://open.feishu.cn/open-apis/bot/v2/hook/a7554e82-8ac4-4cde-b7c6-60fefb1d3d41\",\"sign\":\"cwI5uD1Lv1ayFg3dGmSzAd\"}]}", autoRefreshed = true)
    private String notifyUrlText;


    /**
     * 成本价新老模型开关
     */
    @NacosValue(value = "${mall.new.cost.price.switch:false}", autoRefreshed = true)
    private Boolean mallNewCostPriceSwitch;

    /**
     * 乐乐茶定制化配送模板打印 大客户ID
     */
    @NacosValue(value = "${llc.delivery.template.print.admin_ids:[]}", autoRefreshed = true)
    private String llcDeliveryTemplatePrintAdminIds;


    /**
     * 新低价监控入口开关，开启-允许走新入口；关闭-不允许走新入口
     */
    @NacosValue(value = "${new.low.price.switch:true}", autoRefreshed = true)
    private Boolean newLowPriceSwitch;

    /**
     * 新低价监控灰度大客户。配置成[]全部走老入口；配置指定大客户id，则指定大客户走新入口；配置[-1]则全部走新入口
     */
    @NacosValue(value = "${new.low.price.admin.id.list:[-1]}", autoRefreshed = true)
    private String newLowPriceAdminIdList;

    /**
     * 低价监控并发线程数
     */
    @NacosValue(value = "${low.price.task.thread.num:5}", autoRefreshed = true)
    private Integer lowPriceTaskThreadNum;

    @NacosValue(value = "${activity.ladder.price.init.white.list:[]}", autoRefreshed = true)
    private String activityLadderPriceInitWhiteList;


    /**
     * pop 默认运营区域
     */
    @NacosValue(value = "${pop.default.area.no:9999}", autoRefreshed = true)
    private Integer popDefaultAreaNo;

    /**
     * pop 默认运营区域名称
     */
    @NacosValue(value = "${pop.default.area.name:pop商城默认运营服务区}", autoRefreshed = true)
    private String popDefaultAreaName;

    /**
     * pop 默认挂属大客户id
     */
    @NacosValue(value = "${pop.default.admin.id:999999}", autoRefreshed = true)
    private Integer popDefaultAdminId;

    /**
     * pop 默认默认配送周期
     */
    @NacosValue(value = "${pop.default.delivery.rule:}", autoRefreshed = true)
    private String popDefaultDeliveryRule;


    /**
     * 免审用户默认大客户id
     */
    @NacosValue(value = "${no.audit.admin.id:999998}", autoRefreshed = true)
    private Integer noAuditMerchantDefaultAdminId;

    /**
     * 修改门店地址时是否修改老版本的运费规则，true-修改；false-不修改
     */
    @NacosValue(value = "${change.contact.modify.distribution.rules:true}", autoRefreshed = true)
    private Boolean changeContactModifyDistributionRules;

    /**
     * 七分甜定制化配送模板打印 大客户ID
     */
    @NacosValue(value = "${qft.delivery.template.print.admin_ids:[]}", autoRefreshed = true)
    private String qftDeliveryTemplatePrintAdminIds;

    /**
     * 诺尔定制化配送模板打印 大客户ID
     */
    @NacosValue(value = "${nuoer.delivery.template.print.admin_ids:[]}", autoRefreshed = true)
    private String nuoErDeliveryTemplatePrintAdminIds;

    /**
     * 代销不入仓可以切仓的仓库白名单 仓库编号
     */
    @NacosValue(value = "${cutWarehouse.selfSaleNoWarehouseSku.warehouseNoWhiteList:[]}", autoRefreshed = true)
    private String cutWarehouseSelfSaleNoWarehouseSkuWarehouseNoWhiteList;

    /**
     * pop t+2 区域
     */
    @NacosValue(value = "${pop.popT2AreaNoList:[]}", autoRefreshed = true)
    private String popT2AreaNoList;

    /**
     * pop t+2 区域过滤黑名单类目
     */
    @NacosValue(value = "${pop.thirdCategoryBlackList:[]}", autoRefreshed = true)
    private String thirdCategoryBlackList;

    /**
     * pop 可以库存仓编号
     */
    @NacosValue(value = "${pop.popAllWarehouseNoList:[]}", autoRefreshed = true)
    private String popAllWarehouseNoList;


    /**
     * 是否基于飞书机器人发送价格倒挂消息
     */
    @NacosValue(value = "${enableFeiShuRobotForPriceAdjustment:true}", autoRefreshed = true)
    private boolean enableFeiShuRobotForPriceAdjustment;


    /**
     * 是否基于飞书机器人发送价成本波动消息
     */
    @NacosValue(value = "${enableFeiShuRobotForCostFluctuation:true}", autoRefreshed = true)
    private boolean enableFeiShuRobotForCostFluctuation;




    /**
     * 全量接口下线灰度开关
     */
    @NacosValue(value = "${fullApiOfflineSwitch:true}", autoRefreshed = true)
    private boolean fullApiOfflineSwitch;


    /**
     * 获取通知群信息
     *
     * @param key
     * @return
     */
    public Optional<NotifyConfigBO> getRiskNotify(String key) {
        List<NotifyConfigBO> notifyConfigBOS = JSON.parseArray(notifyUrlText, NotifyConfigBO.class);
        for (NotifyConfigBO notifyConfigBO : notifyConfigBOS) {
            if (Objects.equals(key, notifyConfigBO.getKey())
                    && NotifyConfigBO.bodyCheck(notifyConfigBO)) {
                return Optional.of(notifyConfigBO);
            }
        }
        return Optional.empty();
    }



    /**
     * bms 是否监听tms消息
     * true：消费
     * false:不消费
     * @return
     */
    public boolean getBmsTrunkOffline() {
        return bmsTrunkSwitch;
    }

    public Boolean getMallNewCostPriceSwitch() {
        return mallNewCostPriceSwitch;
    }

    /**
     * fms 凭证上传监听开关
     * true：消费
     * false:不消费
     * @return
     */
    public boolean getUploadSwitch() {
        return uploadSwitch;
    }


    /**
     * 在配置中的审批流切到飞书
     *
     * @return
     */
    public List<ProcessMigrateBO> getProcessMigrate() {
        return JSON.parseArray(processSwitch, ProcessMigrateBO.class);
    }


    /**
     * bms 回调接口迁移
     * true：已迁移
     * false:未迁移
     *
     * @return
     */
    public boolean bmsCallBackSwitch(String code) {
        List<String> strings = JSON.parseArray(bmsCallBackMigrate, String.class);
        return strings.contains(code);
    }

    /**
     * 乐乐茶定制化配送模板打印 大客户ID集合
     *
     * @return 大客户ID集合
     */
    public List<Integer> getLlcDeliveryTemplatePrintAdminIds() {
        try {
            return JSON.parseArray(llcDeliveryTemplatePrintAdminIds, Integer.class);
        } catch (Exception e){
            log.error("乐乐茶定制化配送模板打印大客户ID 动态配置解析失败", e);
            return Lists.newArrayList();
        }

    }

    /**
     * 七分甜定制化配送模板打印 大客户ID集合
     *
     * @return 大客户ID集合
     */
    public List<Integer> getQftDeliveryTemplatePrintAdminIds() {
        try {
            return JSON.parseArray(qftDeliveryTemplatePrintAdminIds, Integer.class);
        } catch (Exception e){
            log.error("七分甜定制化配送模板打印大客户ID 动态配置解析失败", e);
            return Lists.newArrayList();
        }

    }

    /**
     * 诺尔定制化配送模板打印 大客户ID集合
     *
     * @return 大客户ID集合
     */
    public List<Integer> getNuoErDeliveryTemplatePrintAdminIds() {
        try {
            return JSON.parseArray(nuoErDeliveryTemplatePrintAdminIds, Integer.class);
        } catch (Exception e){
            log.error("诺尔定制化配送模板打印大客户ID 动态配置解析失败", e);
            return Lists.newArrayList();
        }

    }
    public Boolean getNewLowPriceSwitch() {
        return newLowPriceSwitch;
    }

    public Integer getLowPriceTaskThreadNum() {
        return lowPriceTaskThreadNum;
    }

    public List<Integer> getNewLowPriceAdminIdList() {
        try {
            return JSON.parseArray(newLowPriceAdminIdList, Integer.class);
        } catch (Exception e){
            log.error("新低价监控灰度大客户 动态配置解析失败", e);
            return Lists.newArrayList();
        }
    }

    public List<String> getActivityLadderPriceInitWhiteList() {
        try {
            return JSON.parseArray(activityLadderPriceInitWhiteList, String.class);
        } catch (Exception e){
            log.error("阶梯价初始化白名单 动态配置解析失败", e);
            return Lists.newArrayList();
        }
    }

    /**
     * 代销不入仓可以切仓的仓库白名单
     *
     * @return 仓库编号集合
     */
    public List<Integer> getCutWarehouseSelfSaleNoWarehouseSkuWarehouseNoWhiteList() {
        try {
            return JSON.parseArray(cutWarehouseSelfSaleNoWarehouseSkuWarehouseNoWhiteList, Integer.class);
        } catch (Exception e){
            log.error("代销不入仓可以切仓的仓库白名单 动态配置解析失败", e);
            return Lists.newArrayList();
        }
    }

    public String getPopDefaultDeliveryRule() {
        return popDefaultDeliveryRule;
    }

    public Integer getPopDefaultAreaNo() {
        return popDefaultAreaNo;
    }

    public String getPopDefaultAreaName() {
        return popDefaultAreaName;
    }

    public Integer getPopDefaultAdminId() {
        return popDefaultAdminId;
    }

    public Integer getNoAuditMerchantDefaultAdminId() {
        return noAuditMerchantDefaultAdminId;
    }

    public Boolean getChangeContactModifyDistributionRules() {
        return changeContactModifyDistributionRules;
    }

    /**
     * 对账付款单切换
     * true：新逻辑
     */
    @NacosValue(value = "${fms.pms.statement.switch:false}", autoRefreshed = true)
    private Boolean pmsStatementSwitch;

    public Boolean getPmsStatementSwitch() {
        return pmsStatementSwitch;
    }

    /**
     * 城配计费切换
     * true：新逻辑
     */
    @NacosValue(value = "${bms.settle.cost.switch:true}", autoRefreshed = true)
    private Boolean bmsSettleCostSwitch;

    public Boolean getBmsSettleCostSwitch() {
        return bmsSettleCostSwitch;
    }

    public List<Integer> getPopT2AreaNoList() {
        try {
            return JSON.parseArray(popT2AreaNoList, Integer.class);
        } catch (Exception e){
            log.error("pop t+2区域 动态配置解析失败", e);
            return Lists.newArrayList();
        }
    }

    public List<Integer> getThirdCategoryBlackList() {
        try {
            return JSON.parseArray(thirdCategoryBlackList, Integer.class);
        } catch (Exception e){
            log.error("pop t+2 区域过滤黑名单类目 动态配置解析失败", e);
            return Lists.newArrayList();
        }
    }

    public List<Integer> getPopAllWarehouseNoList() {
        try {
            return JSON.parseArray(popAllWarehouseNoList, Integer.class);
        } catch (Exception e){
            log.error("pop 可以库存仓编号 动态配置解析失败", e);
            return Lists.newArrayList();
        }
    }

    public boolean getEnableFeiShuRobotForPriceAdjustment() {
        return enableFeiShuRobotForPriceAdjustment;
    }

    public boolean isEnableFeiShuRobotForCostFluctuation() {
        return enableFeiShuRobotForCostFluctuation;
    }

    public boolean isFullApiOfflineSwitch() {
        return fullApiOfflineSwitch;
    }
}
