package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.AreaDTO;
import net.summerfarm.model.domain.AreaTop;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AreaTopMapper {

    /**
     * 批量保存城市数据
     * @param areaTopList 城市组集合
     * @return
     */
    int insertBatch(@Param("list") List<AreaTop> areaTopList);

    /**
     * 删除成熟数据
     * @param topId topId
     */
    void deleteByTopId(Integer topId);

    /**
     * 查询购物车置顶城市数据
     * @param topId 购物车数据
     * @return 成数组数据集合
     */
    List<AreaDTO> select(Integer topId);
}