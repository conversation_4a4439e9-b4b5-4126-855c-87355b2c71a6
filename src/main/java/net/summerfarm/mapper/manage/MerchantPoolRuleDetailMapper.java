package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MerchantPoolRuleDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantPoolRuleDetailMapper {

    int deleteById(Long id);

    int insert(MerchantPoolRuleDetail merchantPoolRuleDetail);

    MerchantPoolRuleDetail selectById(Long id);

    int update(MerchantPoolRuleDetail merchantPoolRuleDetail);

    MerchantPoolRuleDetail selectByInfoId(@Param("poolInfoId") Long poolInfoId);

    int deleteByInfoId(@Param("poolInfoId") Long poolInfoId);

    int updateByInfoId(@Param("poolInfoId") Long poolInfoId, @Param("ruleDetail") String ruleDetail);

}