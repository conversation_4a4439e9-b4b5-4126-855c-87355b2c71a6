package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CouponReceiveLog;

public interface CouponReceiveLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CouponReceiveLog record);

    int insertSelective(CouponReceiveLog record);

    CouponReceiveLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CouponReceiveLog record);

    int updateByPrimaryKey(CouponReceiveLog record);
}