package net.summerfarm.service.impl.Fence;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.StringUtil;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.RedissonLockUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.DeliveryLogEnum;
import net.summerfarm.enums.FenceStatusEnum;
import net.summerfarm.enums.OtherStockChangeTypeEnum;
import net.summerfarm.enums.WeekTimeEnum;
import net.summerfarm.enums.tms.FenceEnums;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.saas.TmsDeliveryPlanDetailMapper;
import net.summerfarm.mapper.manage.saas.TmsDeliveryPlanMapper;
import net.summerfarm.model.DTO.tms.ChangeFenceMsg;
import net.summerfarm.model.DTO.tms.OldDistOrder;
import net.summerfarm.model.DTO.tms.OldDistOrderItem;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.saas.TmsDeliveryPlanDetail;
import net.summerfarm.model.input.AdcodeMsgQuery;
import net.summerfarm.model.input.FenceInput;
import net.summerfarm.model.param.ChangeFenceData;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.vo.saas.TmsDeliveryPlanVO;
import net.summerfarm.module.wnc.common.exception.WncBizErrorEnum;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.constant.MessageBusiness;
import net.summerfarm.mq.constant.MessageType;
import net.summerfarm.mq.crm.constant.MessageKeyEnum;
import net.summerfarm.service.AreaStoreService;
import net.summerfarm.service.FenceService;
import net.summerfarm.service.QuantityChangeRecordService;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.model.vo.WarehouseInventoryMappingVO;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.wnc.client.mq.WncMqConstants;
import net.xianmu.common.exception.BizException;
import net.xianmu.redis.support.lock.annotation.XmLock;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.rocketmq.client.producer.MQProducer;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.contexts.Global.SEVEN;

/**
 * <AUTHOR> ct
 * create at:  2021/8/10  14:26
 */
@Service
public class FenceServiceImpl extends BaseService implements FenceService {

    @Resource
    private FenceMapper fenceMapper;
    @Resource
    private AdCodeMsgMapper adCodeMsgMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private WarehouseLogisticsService logisticsService;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    ChangeFenceMapper changeFenceMapper;
    @Resource
    ContactMapper contactMapper;
    @Resource
    DeliveryPlanMapper deliveryPlanMapper ;
    @Resource
    OrdersMapper ordersMapper;
    @Resource
    OrderItemMapper orderItemMapper;
    @Resource
    BaseService baseService;
    @Resource
    @Lazy
    AreaStoreService areaStoreService;
    @Resource
    QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    FenceDeliveryLogMapper fenceDeliveryLogMapper;
    @Resource
    FenceDeliveryMapper fenceDeliveryMapper;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private TmsStopDeliveryMapper tmsStopDeliveryMapper;
    @Resource
    private TmsDeliveryPlanMapper tmsDeliveryPlanMapper;
    @Resource
    private TmsDeliveryPlanDetailMapper tmsDeliveryPlanDetailMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private SampleApplyMapper sampleApplyMapper;
    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    private MqProducer mqProducer;
    private FenceService selfService;

    /**
     * 围栏等级 市
     */
    private static final String CITY = "city";

    /**
     * 围栏等级 区
     */
    private static final String DISTRICT = "district";

    /**
     * 高德调用失败
     */
    private static final String FAIL = "fail";

    private static final String AREA = "AREA";

    /**
     * 一周的日期
     */
    private static final String[] WEEK = {"1","2","3","4","5","6","7"};

    private static final Logger logger = LoggerFactory.getLogger(FenceServiceImpl.class);

    @PostConstruct
    private void setSelf() {
        selfService = getContext().getBean(FenceService.class);
    }

    @Override
    @Transactional
    public AjaxResult insertFence(FenceVO fenceVO) {
        Integer adminId = getAdminId();
        //校验数据
        checkoutFenceVO(fenceVO);
        //校验配送信息
        checkoutDelivery(fenceVO);

        //排序配送周期字段
        this.sortDeliveryFrequent(fenceVO);
        //获取围栏信息
        List<AdCodeMsg> adCodes = fenceVO.getAdCodes();
        Fence fence = createFence(fenceVO);
        //获取打包编号
        Integer packId = selectPackId(fenceVO);
        fence.setPackId(packId);
        //新增围栏信息
        fenceMapper.insertFence(fence);
        Integer fenceId = fence.getId();
        handleAdCodeMsg(adCodes,fenceId);
        //批量插入具体围栏数据
        if(!CollectionUtils.isEmpty(adCodes)){
            adCodeMsgMapper.insertBatchAdCode(adCodes);
        }
        //插入映射
        insertMapping(fenceVO);
        //插入配送表
        fenceVO.setId(fenceId);
        FenceDelivery fenceDelivery = new FenceDelivery();
        BeanUtils.copyProperties(fenceVO,fenceDelivery);
        fenceDelivery.setFenceId(fenceVO.getId());
        fenceDelivery.setCreator(adminId);
        fenceDelivery.setUpdater(adminId);
        fenceDeliveryMapper.insertSelective(fenceDelivery);
        this.handleDeliveryDataLog(fenceVO);

        return AjaxResult.getOK();
    }

    /**
     * 排序配送周期字段
     * @param fenceVO
     */
    private void sortDeliveryFrequent(FenceVO fenceVO) {
        String deliveryFrequent = fenceVO.getDeliveryFrequent();
        if(fenceVO.getFrequentMethod() == 1 && StringUtils.isNotBlank(deliveryFrequent)){
            String[] split = deliveryFrequent.split(Global.SEPARATING_SYMBOL);
            ArrayList<String> deliveryFrequentList = new ArrayList<>(Arrays.asList(split));
            List<String> sortDeliveryFrequentList = deliveryFrequentList.stream().sorted().collect(Collectors.toList());
            String join = org.apache.commons.lang3.StringUtils.join(sortDeliveryFrequentList, Global.SEPARATING_SYMBOL);
            fenceVO.setDeliveryFrequent(join);
        }
    }

    /**
     * 添加到日志表
     * @param fenceVO 围栏信息
     */
    private void handleDeliveryDataLog(FenceVO fenceVO) {
        Integer adminId = getAdminId();
        FenceDeliveryLog fenceDeliveryLog = new FenceDeliveryLog();
        BeanUtils.copyProperties(fenceVO,fenceDeliveryLog);
        fenceDeliveryLog.setFenceId(fenceVO.getId());
        fenceDeliveryLog.setCreator(adminId);
        fenceDeliveryLog.setUpdater(adminId);
        fenceDeliveryLogMapper.insertSelective(fenceDeliveryLog);

    }

    /**
     * 校验配送信息
     * @param fenceVO 围栏信息
     */
    private void checkoutDelivery(FenceVO fenceVO) {
        if(fenceVO.getFrequentMethod() == null){
            throw new BizException("周期方案不能为空");
        }
        if (fenceVO.getNextDeliveryDate() != null && Objects.equals(fenceVO.getFrequentMethod(),FenceEnums.frequentMethod.FREQUENT_WEEK.getValue())) {
            if (!fenceVO.getNextDeliveryDate().isAfter(LocalDate.now())) {
                throw new DefaultServiceException("首次配送时间必须大于当前日期");
            }
            if (StringUtils.isNotBlank(fenceVO.getDeliveryFrequent())) {
                int dayOfWeek = fenceVO.getNextDeliveryDate().getDayOfWeek().getValue();

                String[] deliveryFrequent = fenceVO.getDeliveryFrequent().split(Global.SEPARATING_SYMBOL);
                //不是每天配送 && 配送日和配送周期不匹配
                if (Arrays.stream(deliveryFrequent).noneMatch(el -> {
                    if (Objects.equals(el, "0")) {
                        return true;
                    } else {
                        return Objects.equals(el, Integer.toString(dayOfWeek));
                    }
                })) {
                    /*throw new DefaultServiceException("首次配送时间和配送周期不匹配，"
                            + fenceVO.getNextDeliveryDate().format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT))
                            + "是星期" + StringUtils.int2chineseNum(dayOfWeek));*/
                    throw new BizException("首次配送时间和配送周期不匹配，请将首次配送时间设置为可配送日");
                }
            }
        }
        if(fenceVO.getDeliveryFrequentInterval() != null && Objects.equals(fenceVO.getFrequentMethod(),FenceEnums.frequentMethod.FREQUENT_INTERVAL.getValue())){
            LocalDate beginCalculateDate = fenceVO.getBeginCalculateDate();
            if(beginCalculateDate == null){
                throw new BizException("开始计算日期不能为空");
            }
            if(beginCalculateDate.isAfter(LocalDate.now())){
                throw new BizException("开始计算日期只能当天及当天之前");
            }
            if (fenceVO.getNextDeliveryDate() != null){
                if (!fenceVO.getNextDeliveryDate().isAfter(LocalDate.now())) {
                    throw new BizException("首次配送时间必须大于当前日期");
                }
                //首配日期
                LocalDate nextDeliveryDate = fenceVO.getNextDeliveryDate();
                while(nextDeliveryDate.compareTo(beginCalculateDate) > 0){
                    beginCalculateDate = beginCalculateDate.plusDays(fenceVO.getDeliveryFrequentInterval());
                    if(beginCalculateDate.compareTo(nextDeliveryDate) == 0){
                        return;
                    }
                }
                throw new BizException("首次配送时间和配送周期不匹配，请将首次配送时间设置为可配送日");
            }
        }
    }

    @Override
    public AjaxResult selectFence(FenceVO fenceVo,Integer pageIndex,Integer pageSize) {

        PageHelper.startPage(pageIndex, pageSize);
        Integer status = fenceVo.getStatus();
        if(status != null && status.intValue() == FenceStatusEnum.IN_VALID.ordinal()){
            fenceVo.setStatusList(Arrays.asList(FenceStatusEnum.IN_VALID.ordinal(), FenceStatusEnum.STOP.ordinal()));
            fenceVo.setStatus(null);
        }
        List<FenceVO> fenceVOS = fenceMapper.selectByFence(fenceVo);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(fenceVOS));
    }

    @Override
    public FenceVO selectFenceById(Integer id) {

        FenceVO fenceVO = fenceMapper.selectFenceById(id);
        if(fenceVO == null){
            throw new BizException("不存在此围栏信息");
        }
        List<AdCodeMsg> adCodeMsgs= adCodeMsgMapper.selectByFenceId(id,fenceVO.getStatus());
        //删除状态
        if(Objects.equals(fenceVO.getStatus(),FenceStatusEnum.IN_VALID.ordinal())){
            adCodeMsgs = adCodeMsgMapper.selectByFenceId(id,FenceStatusEnum.DELETE.ordinal());
        }
//        ChangeFence changeFences = changeFenceMapper.selectByFenceId(id);
        Integer taskCount = changeFenceMapper.selectHandlingTasksByFenceId(id);
        Boolean changeStatus  = taskCount == null || taskCount == 0 ? Boolean.FALSE : Boolean.TRUE;
        String handlingAreaStr = changeFenceMapper.selectHandlingAreas();
//        if(changeStatus){
//            String changeMsg = changeFenceMsg(fenceVO, changeFences);
//            fenceVO.setChangeMsg(changeMsg);
//        }
        adCodeMsgs.forEach(e -> e.setSelectFlag(true));
        if (StrUtil.isNotBlank(handlingAreaStr)){
            Set<Integer> handlingAreas = Arrays.stream(handlingAreaStr.split(",")).map(Integer::parseInt).collect(Collectors.toSet());
            for (AdCodeMsg adCodeMsg : adCodeMsgs) {
                if (handlingAreas.contains(adCodeMsg.getId())){
                    adCodeMsg.setSelectFlag(false);
                }
            }
        }
        fenceVO.setChangeStatus(changeStatus);
        fenceVO.setAdCodes(adCodeMsgs);
        //取配送周期数据
        FenceDelivery fenceDelivery = fenceDeliveryMapper.selectByFenceId(id);
        fenceVO.setDeliveryFrequent(fenceDelivery.getDeliveryFrequent());
        fenceVO.setNextDeliveryDate(fenceDelivery.getNextDeliveryDate());
        fenceVO.setFrequentMethod(fenceDelivery.getFrequentMethod());
        fenceVO.setDeliveryFrequentInterval(fenceDelivery.getDeliveryFrequentInterval());
        fenceVO.setBeginCalculateDate(fenceDelivery.getBeginCalculateDate());
        return fenceVO;
    }

    @Override
    @Transactional
    public AjaxResult deleteFenceById(Integer id) {

        FenceVO fenceVO = fenceMapper.selectFenceById(id);
        if (Objects.isNull(fenceVO)) {
            return AjaxResult.getErrorWithMsg("围栏不存在");
        }
        //移除围栏
        Fence updateFence = new Fence();
        updateFence.setId(fenceVO.getId());
        updateFence.setUpdateTime(LocalDateTime.now());
        updateFence.setStatus(FenceStatusEnum.IN_VALID.ordinal());
        Integer adminId = baseService.getAdminId();
        updateFence.setAdminId(adminId);
        fenceMapper.updateFence(updateFence);
        //删除高德
        adCodeMsgMapper.deleteAdCode(id);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional
    public AjaxResult updateFence(FenceVO fenceVO) {

        //校验配送信息
        checkoutDelivery(fenceVO);
        //排序配送周期字段
        this.sortDeliveryFrequent(fenceVO);
        //修改的数据 前端没处理，处理下
        List<AdCodeMsg> adCodes = fenceVO.getAdCodes();
        Map<String, List<AdCodeMsg>> allCodeMsg = adCodes.stream().collect(Collectors.groupingBy(o -> o.getCity() + "_" + o.getArea()));
        //本次新增数据
        List<AdCodeMsg> insertCode = new ArrayList<>();
        //原数据
        List<AdCodeMsg> adCodeMsgList = adCodeMsgMapper.selectAdCodeMsgByFenceId(fenceVO.getId());

        //获取本次新增数据
        Map<String, List<AdCodeMsg>> originCodeMap = adCodeMsgList.stream().collect(Collectors.groupingBy(o -> o.getCity() + "_" + o.getArea()));
        adCodes.stream().forEach(adCodeMsg -> {
            String key = adCodeMsg.getCity() + "_" + adCodeMsg.getArea();
            if(CollectionUtils.isEmpty(originCodeMap.get(key))){
                insertCode.add(adCodeMsg);
            }
        });
        //校验
        checkoutAdCode(insertCode,fenceVO.getId());
        //获取本次删除的数据
        List<AdCodeMsg> deleteCodeMsg = new ArrayList<>();
        adCodeMsgList.stream().forEach(adCodeMsg -> {
            String key = adCodeMsg.getCity() + "_" + adCodeMsg.getArea();
            if(CollectionUtils.isEmpty(allCodeMsg.get(key))){
                deleteCodeMsg.add(adCodeMsg);
            }
        });
        //删除
        if(!CollectionUtils.isEmpty(deleteCodeMsg)){
            deleteAdCodeMsg(deleteCodeMsg);
        }
        //插入
        if(!CollectionUtils.isEmpty(insertCode)){
            handleAdCodeMsg(insertCode,fenceVO.getId());
            adCodeMsgMapper.insertBatchAdCode(insertCode);
        }

        //原围栏信息
        FenceVO fenceOld = fenceMapper.selectFenceById(fenceVO.getId());

        Fence updateFence = new Fence();
        updateFence.setId(fenceVO.getId());
        updateFence.setUpdateTime(LocalDateTime.now());
        Integer adminId = baseService.getAdminId();
        updateFence.setAdminId(adminId);
        fenceMapper.updateFence(updateFence);
        //更新数据
        //同步降级用户地址
        //this.handleContact(fenceVO);
        FenceDelivery fenceDelivery = new FenceDelivery();
        fenceDelivery.setFenceId(fenceVO.getId());
        fenceDelivery.setDeliveryFrequent(fenceVO.getDeliveryFrequent());
        fenceDelivery.setNextDeliveryDate(fenceVO.getNextDeliveryDate());
        fenceDelivery.setFrequentMethod(fenceVO.getFrequentMethod());
        fenceDelivery.setBeginCalculateDate(fenceVO.getBeginCalculateDate());
        fenceDelivery.setDeliveryFrequentInterval(fenceVO.getDeliveryFrequentInterval());
        fenceDeliveryMapper.updateByFenceIdSelective(fenceDelivery);
        //更新配送日志表
        this.handleDeliveryDataLog(fenceVO);
        //更新需要提醒
        if(!Objects.equals(fenceOld.getDeliveryFrequent(),fenceVO.getDeliveryFrequent())
                || !Objects.equals(fenceOld.getDeliveryFrequentInterval(),fenceVO.getDeliveryFrequentInterval())){
            //钉钉提醒通知相关
            deliveryFenceChangeDingTalk(fenceVO,fenceOld);
        }

        return AjaxResult.getOK();
    }

    /**
     * 发送钉钉提醒通知
     */
    private void deliveryFenceChangeDingTalk(FenceVO fenceVO, FenceVO oldFence){
        StringBuilder textSb = new StringBuilder();
        textSb.append("围栏配送周期变更提醒").append("\n\n");
        textSb.append("围栏编号:").append(fenceVO.getId()).append("\n\n");
        textSb.append("围栏名称:").append(oldFence.getFenceName()).append("\n\n");
        if(Objects.equals(fenceVO.getFrequentMethod(),FenceEnums.frequentMethod.FREQUENT_WEEK.getValue())){
            if(Objects.equals("0",fenceVO.getDeliveryFrequent())){
                textSb.append("配送周期:").append( "每天").append("\n\n");
            }else{
                List<String> days = Arrays.asList(fenceVO.getDeliveryFrequent().split(Global.SEPARATING_SYMBOL));
                StringJoiner stringJoiner = new StringJoiner(Global.SEPARATING_SYMBOL);
                days.forEach(day ->{
                    stringJoiner.add(WeekTimeEnum.weekTimeMap.get(day));
                });
                textSb.append("配送周期:"+ stringJoiner).append("\n\n");
            }
        }
        if(Objects.equals(fenceVO.getFrequentMethod(),FenceEnums.frequentMethod.FREQUENT_INTERVAL.getValue())){
            textSb.append("配送周期:")
                    .append("每").append(fenceVO.getDeliveryFrequentInterval()).append("天")
                    .append("(开始计算时间:").append(fenceVO.getBeginCalculateDate()).append(")")
                    .append("\n\n");
        }
        WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsService.selectByStoreNo(oldFence.getStoreNo());
        textSb.append("城配仓:").append(warehouseLogisticsCenter.getStoreName()).append("\n\n");
        List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.selectAdCodeMsgByFenceId(fenceVO.getId());

        StringJoiner contactInfoList = new StringJoiner(",");
        if(!CollectionUtils.isEmpty(adCodeMsgs)){
            List<String> provinceList = adCodeMsgs.stream().map(AdCodeMsg::getProvince).collect(Collectors.toList());
            List<String> cityList = adCodeMsgs.stream().map(AdCodeMsg::getCity).collect(Collectors.toList());
            List<String> areaList = adCodeMsgs.stream().map(AdCodeMsg::getArea).collect(Collectors.toList());

            List<ContactVO> contactVOs = contactMapper.selectDeliveryFrequent(provinceList,cityList,areaList);
            if(!CollectionUtils.isEmpty(contactVOs)){
                for (ContactVO contactVO : contactVOs) {
                    contactInfoList.add(contactVO.getmId()+"-"+contactVO.getMname()+"("+contactVO.getPhone()+")");
                }
            }
        }
        if(StringUtils.isBlank(contactInfoList.toString())){
            return;
        }
        textSb.append("定制化配送门店清单:").append(contactInfoList);

        HashMap<String, String> msgMap = new HashMap<>();
        msgMap.put("text",textSb.toString());
        msgMap.put("title","围栏配送周期变更提醒");

        //机器人url
        Config config = configMapper.selectOne("change_fence_frequent_contact_notice");
        String url = config.getValue();
        DingTalkRobotUtil.sendMsgAndAtAll("markdown", url, () -> msgMap);
    }

    /**
     * 同步围栏下地址的配送时间
     */
    /*private void handleContact(FenceVO fenceVO) {
        //如果是每天配送的话跳过校验，否则校验
        if (fenceVO.getDeliveryFrequent().contains(StringUtils.ZONE)) {
            return;
        }
        //查看原先的围栏配送周期
        FenceDelivery fenceDelivery = fenceDeliveryMapper.selectByFenceId(fenceVO.getId());
        List<String> notExist = new ArrayList<>();
        //取出更改的配送周期比原先配送周期少的元素
        if(StringUtils.ZONE.equals(fenceDelivery.getDeliveryFrequent().split(Global.SEPARATING_SYMBOL)[0])){
            notExist = StringUtils.containsNotExist(fenceVO.getDeliveryFrequent().split(Global.SEPARATING_SYMBOL),WEEK);
        }else{
            notExist = StringUtils.containsNotExist(fenceVO.getDeliveryFrequent().split(Global.SEPARATING_SYMBOL),fenceDelivery.getDeliveryFrequent().split(Global.SEPARATING_SYMBOL));
        }
        //如果新增的配送周期少了原先的配送周期的话处理，否则跳过
        if (CollectionUtils.isEmpty(notExist)) {
            return;
        }
        //取出有效地址中包含少的元素
        List<AdCodeMsg> adCodes = fenceVO.getAdCodes();
        for (AdCodeMsg adCode : adCodes) {
            List<Contact> contactList = contactMapper.selectByAreaDelivery(adCode.getCity(),adCode.getArea(),notExist);
            //去除notExist中存在的值
            for (Contact contact : contactList) {
                String[] contactDeliveryFrequent = contact.getDeliveryFrequent().split(Global.SEPARATING_SYMBOL);
                if(StringUtils.ZONE.equals(contactDeliveryFrequent[NumberUtils.INTEGER_ZERO])){
                    contact.setDeliveryFrequent(fenceVO.getDeliveryFrequent());
                }else{
                    List<String> deliveryFrequentList = new ArrayList<>(Arrays.asList(contactDeliveryFrequent));
                    deliveryFrequentList.removeAll(notExist);
                    contact.setDeliveryFrequent(org.apache.commons.lang.StringUtils.join(deliveryFrequentList,Global.SEPARATING_SYMBOL));
                }
                contactMapper.updateByPrimaryKeySelective(contact);
            }

        }



    }*/


    @Override
    public Integer getAreaNo(String poi, Contact contact) {
        return getAreaNo(contact.getCity(), contact.getArea());
    }

    @Override
    @Nullable
    public Integer getAreaNo(String city, String area) {
        //获取市的围栏信息
        AdCodeMsg adCodeMsg = new AdCodeMsg();
        adCodeMsg.setCity(city);
        List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.selectByCity(adCodeMsg);
        if(CollectionUtils.isEmpty(adCodeMsgs)){
            return null;
        }

        //获取高德ID
        //String gdId = GaoDeUtil.queryDirect(poi,gfids.toString());
        //调用失败 自己匹配一次
        Integer areaNo  = getAreaNoNotGd(adCodeMsgs, area, AREA);
        return areaNo;
    }

    @Override
    public Integer getStoreNo(String poi, Contact contact) {

        if(Objects.isNull(contact)){
            throw  new DefaultServiceException("地址信息为空");
        }
        String city = contact.getCity();

        //获取市的围栏信息
        AdCodeMsg adCodeMsg = new AdCodeMsg();
        adCodeMsg.setCity(city);
        List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.selectByCity(adCodeMsg);
        if(CollectionUtils.isEmpty(adCodeMsgs)){
            return null;
        }
        Integer storeNo = getStoreNoNotGd(adCodeMsgs, contact.getArea());
        return storeNo;

    }

    /**
     * 根据市，区 匹配一次围栏
     */
    private Integer getStoreNoNotGd(List<AdCodeMsg> adCodeMsgs,String area){
        AdCodeMsg result;
        if (StringUtils.isNotBlank(area)) {
            List<AdCodeMsg> collect = adCodeMsgs.stream()
                    .filter(msg -> Objects.equals(msg.getArea(), area)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                throw new BizException(WncBizErrorEnum.ADDRESS_FENCE_NOT_SEND);
            }
            result = collect.get(NumberUtils.INTEGER_ZERO);
        } else {
            List<AdCodeMsg> collect = adCodeMsgs.stream()
                    .filter(msg -> StringUtils.isBlank(msg.getArea())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                throw new BizException(WncBizErrorEnum.ADDRESS_FENCE_NOT_SEND);
            }
            result = collect.get(NumberUtils.INTEGER_ZERO);
        }
        FenceVO fenceVO = fenceMapper.selectFenceById(result.getFenceId());
        return  fenceVO.getStoreNo();
    }

    @Override
    public Integer selectStoreNoByAreaNo(Integer areaNo) {

        List<Integer> storeNoList = selectStoreNoListByAreaNo(areaNo);
        return storeNoList.get(NumberUtils.INTEGER_ZERO);
    }

    @Override
    public List<Integer> selectAreaNoByStoreNo(Integer storeNo) {
        Fence selectFence = new Fence();
        selectFence.setStoreNo(storeNo);
        List<Fence> fenceList = fenceMapper.selectFence(selectFence);
        List<Integer> areaNoList = new ArrayList<>();
        //获取城市编号列表
        if(CollectionUtils.isEmpty(fenceList)){
            return  areaNoList;
        }
        Set<Integer> collect = fenceList.stream().map(Fence::getAreaNo).collect(Collectors.toSet());
        areaNoList = new ArrayList<>(collect);
        return areaNoList;
    }

    @Override
    public Integer selectWarehouseNo(Integer areaNo, String sku) {
        Fence selectFence = new Fence();
        selectFence.setAreaNo(areaNo);
        List<Fence> fenceList = fenceMapper.selectFence(selectFence);
        //获取城市编号列表
        if(CollectionUtils.isEmpty(fenceList)){
            //获取城市名称
            Area area = areaMapper.selectByAreaNo(areaNo);
            throw new BizException(area.getAreaName() + "未获取到对应围栏信息");
        }
        Fence fence = fenceList.get(0);
        Integer storeNo = fence.getStoreNo();
        WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
        if(Objects.isNull(mapping)){
            return null;
        }
        return mapping.getWarehouseNo();
    }

    @Override
    public List<Integer> selectStoreNoListByAreaNo(Integer areaNo) {

        Fence selectFence = new Fence();
        selectFence.setAreaNo(areaNo);
        List<Fence> fenceList = fenceMapper.selectFence(selectFence);
        //获取配送仓编号列表
        if(CollectionUtils.isEmpty(fenceList)){
            //获取城市名称
            Area area = areaMapper.selectByAreaNo(areaNo);
            if (area == null) {
                throw new BizException("未查询到运营区域信息");
            }
            throw new BizException(area.getAreaName() + "未获取到城配仓信息");
        }
        Integer packId = fenceList.get(0).getPackId();
        List<Integer> storeNoByPickId = fenceMapper.selectStoreNoByPickId(packId);
        return storeNoByPickId;
    }

    @Override
    public AjaxResult insertGdService() {
        String s = GaoDeUtil.addService();
        return AjaxResult.getOK(s);
    }

    @Override
    public AjaxResult selectCityArea() {
        AdCodeMsg adCodeMsg = new AdCodeMsg();
        adCodeMsg.setStatus(FenceStatusEnum.VALID.ordinal());
        List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.selectAdCodeMsg(adCodeMsg);

        Map<String, List<AdCodeMsg>> collect = adCodeMsgs.stream().collect(Collectors.groupingBy(AdCodeMsg::getCity));
        List<AdcodeMsgQuery> query = new ArrayList<>();
        collect.forEach((city,areaList) ->{
            StringJoiner area = new StringJoiner(",");
            //是否是 市级围栏
            List<AdCodeMsg> cityList = areaList.stream().filter(x -> Objects.isNull(x.getArea())).collect(Collectors.toList());
            //拼装数据信息
            if(CollectionUtils.isEmpty(cityList)){
                areaList.stream().forEach(x -> area.add(x.getArea()));
            }
            AdcodeMsgQuery adcodeMsgQuery = new AdcodeMsgQuery();
            adcodeMsgQuery.setCityName(city);
            adcodeMsgQuery.setAreaName(area.toString());
            query.add(adcodeMsgQuery);
        });
        return AjaxResult.getOK(query);
    }

    @Override
    public AjaxResult deleteAll() {
        GaoDeUtil.deleteAll();
        return AjaxResult.getOK();
    }

    @Override
    public List<Integer> selectFenceByStoreNo(Integer storeNo) {

        Fence fence = new Fence();
        fence.setStoreNo(storeNo);
        List<Fence> fenceList = fenceMapper.selectFence(fence);
        if(CollectionUtils.isEmpty(fenceList)){
            throw new DefaultServiceException("查询不到围栏相关信息");
        }
        Integer packId = fenceList.get(0).getPackId();
        List<Integer> storeNoList = fenceMapper.selectStoreNoByPickId(packId);
        return storeNoList;
    }

    @Override
    public List<LargeAreaVO> selectAreaByStoreNo(Long mId) {
        Merchant merchant = merchantMapper.selectByMId(mId);
        Set<AreaVO> areas = new HashSet<>();
        Contact contact = new Contact();
        contact.setCity(merchant.getCity());
        contact.setArea(merchant.getArea());
        List<Contact> contacts = contactMapper.selectByMid(mId, 1);
        contacts.add(contact);
        //获取配送城市
        for (Contact queryContact : contacts) {
            Integer areaNo = null;
            try {
                areaNo = getAreaNo(null, queryContact);
            } catch (Exception e) {
                //异常跳过
                continue;
            }
            AreaVO areaVO = areaMapper.selectVOByAreaNo(areaNo);
            if (null!=areaVO){
                areas.add(areaVO);
            }
        }
        List<LargeAreaVO> largeAreaVOS = new ArrayList<>();
        //格式转管下
        if(!CollectionUtils.isEmpty(areas)){
            Map<Integer, List<AreaVO>> collect = areas.stream().collect(Collectors.groupingBy(Area::getLargeAreaNo));
            collect.forEach((largeNo,list) ->{
                LargeAreaVO largeAreaVO = new LargeAreaVO();
                List<AreaVO> areaMsgList = new ArrayList<>(list);
                largeAreaVO.setAreaList(areaMsgList);
                largeAreaVO.setLargeAreaNo(largeNo);
                largeAreaVOS.add(largeAreaVO);
            });
        }

        return largeAreaVOS;
    }

    @Override
    public List<Integer> selectPackAllByStoreNo(Integer storeNo) {
        Fence fence = new Fence();
        fence.setStoreNo(storeNo);
        List<Fence> fenceList = fenceMapper.selectFence(fence);
        if(CollectionUtils.isEmpty(fenceList)){
            return null;
        }
        Integer packId = fenceList.get(NumberUtils.INTEGER_ZERO).getPackId();

        List<Integer> storeNoList = fenceMapper.selectStoreNoByPickId(packId);

        return storeNoList;
    }

    @Override
    public List<Fence> selectChangeFence(Integer areaNo) {
        return fenceMapper.selectChangeByAreaNo(areaNo);
    }

    @Override
    public int updateStoreNo(Fence fence) {

        return fenceMapper.updateFence(fence);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void changeFenceByAreaNo(Integer areaNo){
        //获取城市对应的要切换的围栏信息
        List<ChangeFence> queryFence = changeFenceMapper.selectByAreaNo(areaNo);
        if(CollectionUtils.isEmpty(queryFence)){
            return;
        }
        //所有需要切换的区域
        //只需要切换配送仓的却与
        //遍历获取切换的区域信息
        for (ChangeFence changeFence : queryFence) {
            logger.info("id:{}",changeFence.getId());
            List<AdCodeMsg> allAdCodeMsg = new ArrayList<>();
            //切换到的库存使用仓
            List<ChangeFenceData> changeFenceDataList = new ArrayList<>();
            // 配送仓 订单
            List<String> orderList = new ArrayList<>();
            List<Integer> plans = new ArrayList<>();
            Integer fenceId = changeFence.getFenceId();
            Fence fence = selectFenceDetailById(fenceId);
            Integer changeStore = changeFence.getChangeToStoreNo();
            Integer storeNo = fence.getStoreNo();
            Integer newAreaNo = changeFence.getAreaNo();
            if(Objects.equals(changeFence.getType().intValue(),ChangeFence.TYPE_CHANGE_STORE)){
                List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.selectByFenceId(fenceId, 0);
                allAdCodeMsg.addAll(adCodeMsgs);
            } else {
                String changeAcmId = changeFence.getChangeAcmId();
                List<String> adCodeIds = Arrays.asList(changeAcmId.split(","));
                List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.selectByIds(adCodeIds);
                allAdCodeMsg.addAll(adCodeMsgs);
                Integer changeToFenceId = changeFence.getChangeToFenceId();
                Fence newFence = selectFenceDetailById(changeToFenceId);
                changeStore = newFence.getStoreNo();
                newAreaNo = newFence.getAreaNo();
            }
            //获取订单信息
            changeOrderMsg(allAdCodeMsg,orderList,changeFenceDataList,storeNo,changeStore, plans);
            //城配仓不同处理冻结数据
            if(!Objects.equals(changeStore,storeNo)){
                StringJoiner orderJoiner = new StringJoiner(",");
                orderList.forEach(x -> orderJoiner.add(x));
                logger.info("orderMsg={}",orderJoiner.toString());
                //遍历校验库存
                changeFenceDataList.forEach(data -> {
                    Integer changeToWarehouseNo = data.getChangeToWarehouseNo();
                    String sku = data.getSku();
                    Integer oldWarehouseNo = data.getOldWarehouseNo();
                    if(!Objects.equals(changeToWarehouseNo,oldWarehouseNo)){
                        Integer quantity = data.getQuantity();
                        AreaStore areaStore = areaStoreService.selectAreaStore(changeToWarehouseNo, sku);
                        if(areaStore.getOnlineQuantity() < quantity){
                            throw new DefaultServiceException(0,String.format("%s虚拟库存小于冻结库存",sku));
                        }
                    }

                });
                //开始处理冻结库存数据
                for (ChangeFenceData data : changeFenceDataList) {
                    logger.info("sku:{},quantity:{},oldStoreNo:{},newStoreNo:{}",data.getSku(),data.getQuantity(),data.getOldStoreNo(),data.getChangeToStoreNo());
                    Integer changeToWarehouseNo = data.getChangeToWarehouseNo();
                    Integer oldWarehouseNo = data.getOldWarehouseNo();
                    String sku = data.getSku();
                    //不会为空开始添加冻结库存数据
                    if(Objects.nonNull(changeToWarehouseNo) && !Objects.equals(changeToWarehouseNo,oldWarehouseNo)){
                        Integer quantity = data.getQuantity();
                        Map<String,QuantityChangeRecord> recordMap = new HashMap<>();
                        //原库存仓 加虚拟 减冻结
                        areaStoreService.updateOnlineStockByStoreNo(true, quantity, sku, data.getOldStoreNo(), OtherStockChangeTypeEnum.AREA_CHANGE_STORE, null, recordMap, NumberUtils.INTEGER_ZERO);
                        areaStoreService.updateLockStockByWarehouseNo(-quantity, sku,data.getOldStoreNo() ,data.getOldWarehouseNo(), OtherStockChangeTypeEnum.AREA_CHANGE_STORE, null, recordMap);
                        //新库存仓 减虚拟 加冻结
                        areaStoreService.updateOnlineStockByStoreNo(true,-quantity, sku, data.getChangeToStoreNo(), OtherStockChangeTypeEnum.AREA_CHANGE_STORE, null, recordMap, NumberUtils.INTEGER_ZERO);
                        areaStoreService.updateLockStockByWarehouseNo(quantity, sku,data.getChangeToStoreNo() ,data.getChangeToWarehouseNo(), OtherStockChangeTypeEnum.AREA_CHANGE_STORE, null, recordMap);
                        quantityChangeRecordService.insertRecord(recordMap);
                    }
                }
                //处理配送计划对应的城配仓
                if(!CollectionUtils.isEmpty(orderList)){
                    deliveryPlanMapper.updateDeliveryPlanStoreNo(orderList,changeStore);
                }
                if(!CollectionUtils.isEmpty(plans)){
                    tmsDeliveryPlanMapper.updateStoreNo(plans,changeStore);
                }
            }

            //归属城市不同,调整订单城市归属
            if(!Objects.equals(newAreaNo,areaNo) && !CollectionUtils.isEmpty(orderList)){
                ordersMapper.updateOrderAreaNo(orderList,newAreaNo);
            }
            //批量更新地址信息
            for (AdCodeMsg adCodeMsg : allAdCodeMsg) {
                String city = adCodeMsg.getCity();
                String area = adCodeMsg.getArea();
                FenceInput fenceInput = new FenceInput(city,area,changeStore);
                contactMapper.updateContact(fenceInput);
                //更新用户归属城市
                if(Objects.equals(changeFence.getType(),ChangeFence.TYPE_CHANGE_CODE)){
                    Fence newFence = selectFenceDetailById(changeFence.getChangeToFenceId());
                    Integer changeToArea = newFence.getAreaNo();
                    Merchant updateMerchant = new Merchant();
                    updateMerchant.setAreaNo(changeToArea);
                    updateMerchant.setCity(city);
                    updateMerchant.setArea(area);
                    merchantMapper.updateAreaNoByCity(updateMerchant);
                }
                //围栏区域切换
                if(Objects.nonNull(changeFence.getChangeToFenceId())){
                    adCodeMsgMapper.updateFenceIdById(adCodeMsg.getId(),changeFence.getChangeToFenceId());
                }
            }
            //围栏切仓
            if(Objects.nonNull(changeFence.getChangeToStoreNo())){
                Fence updateFence = new Fence();
                updateFence.setId(fenceId);
                updateFence.setStoreNo(changeStore);
                updateStoreNo(updateFence);
            }
            //状态改为成功
            changeFenceMapper.updateChangeFence(changeFence.getId(),ChangeFence.CHANGE_SUCCESS);
        }
        return;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public List<ChangeFenceMsg> handleSampleAndAfterSaleByAreaNo(List<ChangeFence> changeFences) {
        if (CollectionUtils.isEmpty(changeFences)){
            return Collections.emptyList();
        }
        //配送时间 补偿任务由于在切仓成功的次日跑，配送时间取大于当天
        LocalDate deliveryDate = LocalDate.now();

        //围栏切仓 切仓消息提示
        List<ChangeFenceMsg> areaChangeFenceMsgList = new ArrayList<>();
        for (ChangeFence changeFence : changeFences) {
            List<AdCodeMsg> provinceCityAreaList;
            Fence oldFence = this.selectFenceDetailById(changeFence.getFenceId());
            Integer oldStoreNo = oldFence == null ? null : oldFence.getStoreNo();
            Integer newStoreNo;
            if(Objects.equals(changeFence.getType(),ChangeFence.TYPE_CHANGE_STORE)){
                provinceCityAreaList = adCodeMsgMapper.selectByFenceId(changeFence.getFenceId(), 0);
                newStoreNo = changeFence.getChangeToStoreNo();
            } else {
                provinceCityAreaList = adCodeMsgMapper.selectByIds(Arrays.asList(changeFence.getChangeAcmId().split(",")));
                Fence newFence = this.selectFenceDetailById(changeFence.getChangeToFenceId());
                newStoreNo = newFence == null ? null : newFence.getStoreNo();
            }
            if (CollectionUtils.isEmpty(provinceCityAreaList) || newStoreNo == null || oldStoreNo == null){
                //数据异常情况校验(基本上不存在)
                continue;
            }
            if (Objects.equals(oldStoreNo, newStoreNo)){
                //城配仓相同无需处理履约单据
                continue;
            }
            List<OldDistOrder> needCutDistOrders = this.getCutDistOrders(deliveryDate, provinceCityAreaList, oldStoreNo);
            if (CollectionUtils.isEmpty(needCutDistOrders)){
                //无切仓区域对应的样品、售后需履约单据无需处理
                continue;
            }
            logger.info("areaNo:{},needCutDistOrders:{}", changeFence.getAreaNo(), JSON.toJSONString(needCutDistOrders));
            //构造Map<sku,ChangeFenceData>汇总sku相关信息
            Map<String, ChangeFenceData> skuChangeFenceDataMap = this.getSkuChangeFenceDataMap(oldStoreNo, newStoreNo, needCutDistOrders);
            //遍历校验库存
            this.verifyInventory(skuChangeFenceDataMap.values());
            //开始处理冻结库存数据
            this.handleInventory(skuChangeFenceDataMap.values());
            //处理配送计划对应的城配仓
            List<ChangeFenceMsg> changeFenceMsgList = this.handleDeliveryPlanStoreNo(oldStoreNo, newStoreNo, needCutDistOrders);
            if (!CollectionUtils.isEmpty(changeFenceMsgList)){
                areaChangeFenceMsgList.addAll(changeFenceMsgList);
            }
        }
        return areaChangeFenceMsgList;
    }

    private List<ChangeFenceMsg> handleDeliveryPlanStoreNo(Integer oldStoreNo, Integer newStoreNo, List<OldDistOrder> needCutDistOrders) {
        List<ChangeFenceMsg> changeFenceMsgList = new ArrayList<>();
        List<OldDistOrder> needCutDistOrdersWithAfterSale = needCutDistOrders.stream().filter(e -> Objects.equals(201, e.getSource())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(needCutDistOrdersWithAfterSale)){
            List<Integer> afterSaleDeliveryPlanIds = needCutDistOrdersWithAfterSale.stream().map(OldDistOrder::getId).collect(Collectors.toList());
            String outerOrderIds = needCutDistOrdersWithAfterSale.stream().map(OldDistOrder::getOuterOrderId).collect(Collectors.joining(","));
            changeFenceMsgList.add(new ChangeFenceMsg(outerOrderIds, "商城售后", oldStoreNo, newStoreNo));
            afterSaleDeliveryPathMapper.updateAfterSaleDeliveryPlanStoreNo(afterSaleDeliveryPlanIds, oldStoreNo, newStoreNo);
        }
        List<OldDistOrder> needCutDistOrdersWithSample = needCutDistOrders.stream().filter(e -> Objects.equals(202, e.getSource())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(needCutDistOrdersWithSample)){
            List<Integer> sampleDeliveryPlanIds = needCutDistOrdersWithSample.stream().map(OldDistOrder::getId).collect(Collectors.toList());
            String outerOrderIds = sampleDeliveryPlanIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            changeFenceMsgList.add(new ChangeFenceMsg(outerOrderIds, "样品申请", oldStoreNo, newStoreNo));
            sampleApplyMapper.updateSampleDeliveryPlanStoreNo(sampleDeliveryPlanIds, oldStoreNo, newStoreNo);
        }
        return changeFenceMsgList;
    }

    private void handleInventory(Collection<ChangeFenceData> changeFenceDataList) {
        if (CollectionUtils.isEmpty(changeFenceDataList)){
            return;
        }
        for (ChangeFenceData changeFenceData : changeFenceDataList) {
            Integer oldWarehouseNo = changeFenceData.getOldWarehouseNo();
            Integer changeToWarehouseNo = changeFenceData.getChangeToWarehouseNo();
            String sku = changeFenceData.getSku();
            Integer quantity = changeFenceData.getQuantity();
            Integer oldStoreNo = changeFenceData.getOldStoreNo();
            Integer changeToStoreNo = changeFenceData.getChangeToStoreNo();
            if (Objects.equals(oldWarehouseNo, changeToWarehouseNo)){
                continue;
            }
            logger.info("处理切仓样品售后库存冻结数据,[sku:{},quantity:{},oldStoreNo:{},newStoreNo:{},oldWarehouseNo:{},newWarehouseNo:{}]",sku,quantity,oldStoreNo,changeToStoreNo,oldWarehouseNo,changeToWarehouseNo);
            Map<String,QuantityChangeRecord> recordMap = new HashMap<>();
            //原库存仓 加虚拟 减冻结
            areaStoreService.updateOnlineStockByStoreNo(true, quantity, sku, oldStoreNo, OtherStockChangeTypeEnum.AREA_CHANGE_STORE, null, recordMap, NumberUtils.INTEGER_ZERO);
            areaStoreService.updateLockStockByWarehouseNo(-quantity, sku,oldStoreNo ,oldWarehouseNo, OtherStockChangeTypeEnum.AREA_CHANGE_STORE, null, recordMap);
            //新库存仓 减虚拟 加冻结
            areaStoreService.updateOnlineStockByStoreNo(true,-quantity, sku, changeToStoreNo, OtherStockChangeTypeEnum.AREA_CHANGE_STORE, null, recordMap, NumberUtils.INTEGER_ZERO);
            areaStoreService.updateLockStockByWarehouseNo(quantity, sku,changeToStoreNo ,changeToWarehouseNo, OtherStockChangeTypeEnum.AREA_CHANGE_STORE, null, recordMap);
            quantityChangeRecordService.insertRecord(recordMap);
        }
    }

    private List<OldDistOrder> getCutDistOrders(LocalDate deliveryDate, List<AdCodeMsg> provinceCityAreaList, Integer oldStoreNo) {
        List<OldDistOrder> oldDistOrders = new ArrayList<>();
        //查询原城配仓样品申请需履约单据(配送时间取大于当天)
        List<OldDistOrder> oldDistOrdersWithSample = sampleApplyMapper.selectSampleDeliveryPlanByStoreNo(oldStoreNo, deliveryDate);
        if (!CollectionUtils.isEmpty(oldDistOrdersWithSample)){
            oldDistOrders.addAll(oldDistOrdersWithSample);
        }
        //查询原城配仓售后需履约单据(配送时间取大于当天)
        List<OldDistOrder> oldDistOrderWithAfterSale = afterSaleDeliveryPathMapper.selectAfterSaleDeliveryPlanByStoreNo(oldStoreNo, deliveryDate);
        if (!CollectionUtils.isEmpty(oldDistOrderWithAfterSale)){
            oldDistOrders.addAll(oldDistOrderWithAfterSale);
        }
        //原城配仓无样品、售后需履约单据(配送时间取大于当天)那么无需处理
        if (CollectionUtils.isEmpty(oldDistOrders)){
            return Collections.emptyList();
        }
        //有单据则需要过滤出对应切仓区域的样品、售后需履约单据进行后续处理
        Set<String> cityAreas = provinceCityAreaList.stream().map(e -> e.getCity() + "#" + e.getArea()).collect(Collectors.toSet());
        List<OldDistOrder> needCutDistOrders = oldDistOrders.stream().filter(e -> cityAreas.contains(e.getCity() + "#" + e.getArea())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needCutDistOrders)){
            return Collections.emptyList();
        }
        return needCutDistOrders;
    }

    private void verifyInventory(Collection<ChangeFenceData> changeFenceDataList) {
        if (CollectionUtils.isEmpty(changeFenceDataList)){
            return;
        }
        for (ChangeFenceData changeFenceData : changeFenceDataList) {
            Integer oldWarehouseNo = changeFenceData.getOldWarehouseNo();
            Integer changeToWarehouseNo = changeFenceData.getChangeToWarehouseNo();
            if (Objects.equals(oldWarehouseNo, changeToWarehouseNo)){
                continue;
            }
            String sku = changeFenceData.getSku();
            Integer quantity = changeFenceData.getQuantity();
            AreaStore areaStore = areaStoreService.selectAreaStore(changeToWarehouseNo, sku);
            if (areaStore == null){
                throw new BizException(String.format("[sku:%s,warehouseNo:%d]无对应的库存记录", sku, changeToWarehouseNo));
            }
            Integer onlineQuantity = areaStore.getOnlineQuantity();
            if(onlineQuantity < quantity){
                throw new BizException(String.format("[sku:%s,warehouseNo:%d,虚拟库存:%d,冻结库存:%d]虚拟库存小于冻结库存", sku, changeToWarehouseNo, onlineQuantity, quantity));
            }
        }
    }

    private Map<String, ChangeFenceData> getSkuChangeFenceDataMap(Integer oldStoreNo, Integer newStoreNo, List<OldDistOrder> needCutDistOrders) {
        Map<String, ChangeFenceData> skuChangeFenceDataMap = new HashMap<>();
        if (CollectionUtils.isEmpty(needCutDistOrders)){
            return skuChangeFenceDataMap;
        }
        for (OldDistOrder needCutDistOrder : needCutDistOrders) {
            List<OldDistOrderItem> distOrderItemList = needCutDistOrder.getDistOrderItemList();
            for (OldDistOrderItem oldDistOrderItem : distOrderItemList) {
                if (oldDistOrderItem.getDeliveryType() == 1){
                    //对于库存仓发生变化的sku需要进行库存处理 售后回收类型的sku不处理
                    continue;
                }
                String sku = oldDistOrderItem.getOuterItemId();
                if (skuChangeFenceDataMap.containsKey(sku)){
                    //相同sku进行数量累加
                    ChangeFenceData changeFenceData = skuChangeFenceDataMap.get(sku);
                    changeFenceData.setQuantity(changeFenceData.getQuantity() + oldDistOrderItem.getQuantity());
                }else {
                    ChangeFenceData changeFenceData = new ChangeFenceData();
                    changeFenceData.setSku(sku);
                    changeFenceData.setQuantity(oldDistOrderItem.getQuantity());
                    changeFenceData.setOldStoreNo(oldStoreNo);
                    changeFenceData.setChangeToStoreNo(newStoreNo);
                    //查询原库存使用仓
                    WarehouseInventoryMapping oldMapping = warehouseInventoryService.selectByUniqueIndex(oldStoreNo, sku);
                    if (oldMapping == null){
                        throw new BizException(String.format("[sku:%s,storeNo:%d]无对应的库存映射关系", sku, oldStoreNo));
                    }
                    changeFenceData.setOldWarehouseNo(oldMapping.getWarehouseNo());
                    //查询新库存使用仓
                    WarehouseInventoryMapping newMapping = warehouseInventoryService.selectByUniqueIndex(newStoreNo, sku);
                    if (newMapping == null){
                        throw new BizException(String.format("[sku:%s,storeNo:%d]无对应的库存映射关系", sku, newStoreNo));
                    }
                    changeFenceData.setChangeToWarehouseNo(newMapping.getWarehouseNo());
                    skuChangeFenceDataMap.put(sku, changeFenceData);
                }
            }
        }
        return skuChangeFenceDataMap;
    }

    /**
     * @param area 城市
     * @param city 区
     * @return 围栏信息
     */
    @Override
    public FenceVO selectFenceByCityArea(String area, String city) {
        FenceVO fenceVo = fenceMapper.selectFenceVoByAreaCity(area,city);
        return fenceVo;
    }

    /*@Override
    public Integer[] selectDeliveryFrequentByFence(Contact contact) {
        String deliveryFrequent = contact.getDeliveryFrequent();
        if(StringUtils.isNotBlank(deliveryFrequent)){
            String[] split = deliveryFrequent.split(Global.SEPARATING_SYMBOL);
            Integer[] result = Convert.toIntArray(split);
            //排序
            Arrays.sort(result);
            return result;
        }
        //查看所属围栏
        FenceVO fenceVO = this.selectFenceByCityArea(contact.getArea(), contact.getCity());
        if (Objects.isNull(fenceVO)) {
            return null;
        }
        //查看配送周期表
        return getDeliveryTimeArray(fenceVO);
    }*/

    @Override
    public Integer[] getDeliveryTimeArray(FenceVO fenceVO) {
        FenceDelivery fenceDelivery = fenceDeliveryMapper.selectByFenceId(fenceVO.getFenceId());
        String[] split = fenceDelivery.getDeliveryFrequent().split(Global.SEPARATING_SYMBOL);
        Integer[] result = Convert.toIntArray(split);
        //排序
        Arrays.sort(result);
        return result;
    }

    /**
     * 查询当前库存仓下所有的运营区域
     * @param warehouseNo 库存仓编号
     * @return 当前库存仓下所有运营区域
     */
    @Override
    public Set<Integer> selectAreaNosByWareNos(Integer warehouseNo, String sku) {
        WarehouseInventoryMappingVO condition = new WarehouseInventoryMappingVO();
        condition.setWarehouseNo(warehouseNo);
        condition.setSku(sku);
        List<WarehouseInventoryMappingVO> warehouseInventoryMappingVOS = warehouseInventoryService.selectVo(condition);
        if(CollectionUtils.isEmpty(warehouseInventoryMappingVOS)){
            logger.info("当前库存仓无任何城配仓映射信息(未查询到任何数据)，warehouseNo:{}", warehouseNo);
            return null;
        }
        Set<Integer> storeNos = warehouseInventoryMappingVOS.stream().map(WarehouseInventoryMappingVO::getStoreNo).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(storeNos)){
            logger.info("当前库存仓无任何城配仓映射信息(数据非空过滤后为空集)，warehouseNo:{}", warehouseNo);
            return null;
        }
        List<Fence> fences = fenceMapper.selectByStroeNos(storeNos);
        if(CollectionUtils.isEmpty(fences)){
            logger.info("当前库存仓无任何运营城市，warehouseNo:{}", warehouseNo);
            return null;
        }
        Set<Integer> areaNos = fences.stream().map(Fence::getAreaNo)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(areaNos)){
            logger.info("当前库存仓无任何运营城市(数据非空过滤后为空集)，warehouseNo:{}", warehouseNo);
            return null;
        }
        return areaNos;
    }

    /**
     * 根据运营服务区域初始化围栏配送数据接口
     */
    @Override
    @Transactional
    public void initFenceDelivery() {
        List<Fence> fence= fenceMapper.selectFenceAll();
        //配送信息
        fence.forEach(vo -> {
            Integer[] frequent = logisticsService.selectDeliveryFrequent(vo.getAreaNo());
            Area area = areaMapper.selectByAreaNo(vo.getAreaNo());
            String DeliveryFrequent = ArrayUtil.join(frequent, Global.SEPARATING_SYMBOL);
            FenceDelivery fenceDelivery = new FenceDelivery();
            fenceDelivery.setFenceId(vo.getId());
            fenceDelivery.setCreator(1);
            fenceDelivery.setUpdater(1);
            fenceDelivery.setDeliveryFrequent(DeliveryFrequent);
            fenceDelivery.setNextDeliveryDate(Objects.isNull(area)? null:area.getNextDeliveryDate());
            fenceDelivery.setDeleteFlag(DeliveryLogEnum.delete.DELETE_FALSE.ordinal());
            fenceDeliveryMapper.insert(fenceDelivery);
        });
    }


    /**
     * 订单冻结数据
     *
     */
    private void changeOrderMsg(List<AdCodeMsg> codeMsgList,List<String> changeToStoreOrderNoList,
                                List<ChangeFenceData> changeFenceDataList,Integer store,Integer changeToStore,
                                List<Integer> plans){

        Map<String, List<AdCodeMsg>> adCodeMap = codeMsgList.stream().collect(Collectors.groupingBy(o -> o.getCity() + "_" + o.getArea()));


        Set<String> orderNos = new HashSet<>();
        //配送时间
        LocalDate deliveryDate = LocalDate.now().plusDays(1);
        //普通订单待配送信息
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectDeliveryPlanByStoreNo(store,deliveryDate );
        //省心送订单配送信息
        List<DeliveryPlanVO> timingDeliveryPlanVOS = deliveryPlanMapper.selectLockTimingByStoreNo(store,deliveryDate);
        //未冻结库省心送订单配送信息
        List<DeliveryPlanVO> unLockTimingDeliveryPlanVOS = deliveryPlanMapper.selectUnLockTimingByStoreNo(store,deliveryDate);
        deliveryPlanVOS.addAll(timingDeliveryPlanVOS);
        //配送订单
        List<DeliveryPlanVO> deliveryPlanVOList = deliveryPlanVOS.stream().filter(x-> !CollectionUtils.isEmpty(adCodeMap.get(String.format("%s_%s",x.getCity(),x.getArea())))).collect(Collectors.toList());
        //未配送订
        List<DeliveryPlanVO> unLockDeliveryPlanVOList = unLockTimingDeliveryPlanVOS.stream().filter(x-> !CollectionUtils.isEmpty(adCodeMap.get(String.format("%s_%s",x.getCity(),x.getArea())))).collect(Collectors.toList());
        //本次切换未冻结订单号
        if(!CollectionUtils.isEmpty(unLockDeliveryPlanVOList)) {
            orderNos = unLockDeliveryPlanVOList.stream().map(DeliveryPlan::getOrderNo).collect(Collectors.toSet());
        }
        //不存在冻结订单返回不处理
        if(CollectionUtils.isEmpty(deliveryPlanVOList)){
            changeToStoreOrderNoList.addAll(orderNos);
            return;
        }
        //获取所有订单中sku信息汇总
        List<Integer> dpIdList = deliveryPlanVOList.stream().map(DeliveryPlan::getId).collect(Collectors.toList());
        Set<String> orderNoList = deliveryPlanVOList.stream().map(DeliveryPlan::getOrderNo).collect(Collectors.toSet());
        orderNos.addAll(orderNoList);
        //要切换的订单
        changeToStoreOrderNoList.addAll(orderNos);
        List<OrderItem> orderItemList = orderItemMapper.selectSkuByOrderList(dpIdList, LocalDate.now().plusDays(1));
        Map<String, List<OrderItem>> skuInfo = orderItemList.stream().collect(Collectors.groupingBy(OrderItem::getSku));
        Set<String> skuList =new HashSet<>();
        skuList.addAll(skuInfo.keySet());
        //获取tms订单信息
        List<TmsDeliveryPlanVO> tmsDeliveryPlanVOS = tmsDeliveryPlanMapper.selectPlanVO(deliveryDate, store);
        Map<String, List<TmsDeliveryPlanDetail>> tmsDetailSkuMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(tmsDeliveryPlanVOS)){
            List<TmsDeliveryPlanVO> tmsDeliveryPlanVOList = tmsDeliveryPlanVOS.stream().filter(x-> !CollectionUtils.isEmpty(adCodeMap.get(String.format("%s_%s",x.getCity(),x.getArea())))).collect(Collectors.toList());
            List<Integer> tmsDeliveryPlanIdList = tmsDeliveryPlanVOList.stream().map(TmsDeliveryPlanVO::getId).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(tmsDeliveryPlanIdList)){
                List<TmsDeliveryPlanDetail> detailList = tmsDeliveryPlanDetailMapper.selectDetailByPlanId(tmsDeliveryPlanIdList);
                tmsDetailSkuMap = detailList.stream().collect(Collectors.groupingBy(TmsDeliveryPlanDetail::getSku));
                plans.addAll(tmsDeliveryPlanIdList);
                //汇总处理订单冻结sku信息
                //校验一次虚拟库存信息 库存仓一致 不需要调整
                Set<String> strings = tmsDetailSkuMap.keySet();
                skuList.addAll(strings);
            }
        }

        for (String sku : skuList) {
            Integer amount = 0;
            OrderItem orderItem = CollectionUtils.isEmpty(skuInfo.get(sku)) ? null : skuInfo.get(sku).get(0);
            amount = Objects.isNull(orderItem) ? amount : amount + orderItem.getAmount() ;
            TmsDeliveryPlanDetail planDetail =  CollectionUtils.isEmpty(tmsDetailSkuMap.get(sku)) ? null :tmsDetailSkuMap.get(sku).get(0);
            amount = Objects.isNull(planDetail) ? amount : amount + planDetail.getAmount() ;
            ChangeFenceData changeFenceData = new ChangeFenceData();
            //原库存使用仓
            WarehouseInventoryMapping oldMapping = warehouseInventoryService.selectByUniqueIndex(store, sku);
            WarehouseInventoryMapping nowMapping = warehouseInventoryService.selectByUniqueIndex(changeToStore, sku);
            logger.info("sku={},store={}",sku,store);
            Integer oldWarehouseNo = oldMapping.getWarehouseNo();
            Integer nowWarehouseNo = nowMapping.getWarehouseNo();
            changeFenceData.setChangeToWarehouseNo(nowWarehouseNo);
            changeFenceData.setOldWarehouseNo(oldWarehouseNo);
            changeFenceData.setChangeToStoreNo(changeToStore);
            changeFenceData.setSku(sku);
            changeFenceData.setQuantity(amount);
            changeFenceData.setOldStoreNo(store);
            changeFenceDataList.add(changeFenceData);
        }
    }

    /**
     * 拼装 围栏信息
     */
    private Fence createFence(FenceVO fenceVO){
        Fence fence = new Fence();
        Integer adminId = baseService.getAdminId();
        String fenceName = fenceVO.getFenceName();
        fence.setAreaNo(fenceVO.getAreaNo());
        fence.setFenceName(fenceName);
        fence.setStoreNo(fenceVO.getStoreNo());
        fence.setAdminId(adminId);
        fence.setStatus(FenceStatusEnum.VALID.ordinal());
        fence.setType(fenceVO.getType());
        return fence;
    }

    private void handleAdCodeMsg(List<AdCodeMsg> adCodes,Integer fenceId){

        adCodes.forEach(code ->{
            code.setStatus(FenceStatusEnum.VALID.ordinal());
            code.setFenceId(fenceId);
        } );
        return;
    }


    /**
     * 数据校验
     */
    private void checkoutFenceVO(FenceVO fenceVO){


        String fenceName = fenceVO.getFenceName();
        if(StringUtils.isEmpty(fenceName) || StringUtils.isEmpty(fenceName.trim())){
            throw new DefaultServiceException("名称不能为空");
        }
        Fence resultFence = fenceMapper.selectFenceByName(fenceName);
        if(Objects.nonNull(resultFence)){
            throw new DefaultServiceException("名称不能重复");
        }
        checkoutAdCode(fenceVO.getAdCodes(),fenceVO.getId());
        return;
    }

    /**
     *   校验围栏数据
     */
    private void checkoutAdCode(List<AdCodeMsg> insertCodeMsg,Integer fenceId){

        if(CollectionUtils.isEmpty(insertCodeMsg)){
            return;
        }

        AdCodeMsg codeMsg = insertCodeMsg.get(0);
        String city = codeMsg.getCity();
        //查询是市信息
        AdCodeMsg query = new AdCodeMsg();
        query.setCity(city);
        query.setFenceId(fenceId);
        List<AdCodeMsg> result = adCodeMsgMapper.selectAndStopByCity(query);
        //市围栏
        if(!CollectionUtils.isEmpty(result) && Objects.equals(codeMsg.getLevel(),CITY)){
            throw new DefaultServiceException(city + "存在市围栏区域");
        }
        //区围栏
        if(!Objects.equals(codeMsg.getLevel(),CITY)){
            List<AdCodeMsg> cityMsg = result.stream().filter(x -> Objects.equals(x.getLevel(), CITY)).collect(Collectors.toList());
            Map<String, List<AdCodeMsg>> areaMap = result.stream().filter(x -> !Objects.equals(x.getLevel(), CITY)).collect(Collectors.groupingBy(AdCodeMsg::getArea));
            if(!CollectionUtils.isEmpty(cityMsg)){
                throw new DefaultServiceException(city + "存在市围栏区域");
            }
            //校验区是否重复
            for (AdCodeMsg adCodeMsg : insertCodeMsg) {
                //优先根据市处理围栏
                List<AdCodeMsg> adCodeMsgs = areaMap.get(adCodeMsg.getArea());
                if(!CollectionUtils.isEmpty(adCodeMsgs)){
                    StringBuffer stringBuffer = new StringBuffer();
                    stringBuffer.append(adCodeMsg.getProvince()).append(adCodeMsg.getCity())
                            .append(adCodeMsg.getArea()).append("存在围栏区域");
                    throw new DefaultServiceException(stringBuffer.toString());
                }
            }
        }


    }

    /**
     * 删除高德id
     */
    private void deleteAdCodeMsg(List<AdCodeMsg> deleteCodeMsg){
       /* StringJoiner deleteIds = new StringJoiner(",");
        deleteCodeMsg.stream().forEach(msg -> {
            if(!StringUtils.isEmpty(msg.getGdId())){
                deleteIds.add(msg.getGdId());
            }
        });
        GaoDeUtil.deleteDirect(deleteIds.toString());*/
        //批量删除
        adCodeMsgMapper.deleteBatch(deleteCodeMsg);
        return;
    }


    private Integer selectPackId(FenceVO fenceVO){
        Integer areaNo = fenceVO.getAreaNo();
        Fence fence = new Fence();
        fence.setAreaNo(areaNo);
        List<Fence> areaFenceList = fenceMapper.selectFenceArea(fence);

        if(!CollectionUtils.isEmpty(areaFenceList)){
            Fence resultFence = areaFenceList.get(NumberUtils.INTEGER_ZERO);
            //校验库存使用仓
            List<WarehouseStorageCenter> storageCenters = logisticsService.selectStorageByMapping(fenceVO.getStoreNo());
            List<WarehouseStorageCenter> oldStorageCenters = logisticsService.selectStorageByMapping(resultFence.getStoreNo());
            if(Objects.equals(storageCenters.size(),oldStorageCenters.size()) && oldStorageCenters.containsAll(storageCenters) ){
                return areaFenceList.get(NumberUtils.INTEGER_ZERO).getPackId();
            } else {
                throw new DefaultServiceException("请调整配送仓的库存使用仓");
            }

        }
        Integer packId = fenceMapper.selectMaxPackId() + 1;
        //获取最大打包id
        return packId;
    }

    /**
     * 根据市，区 匹配一次围栏
     */
    private Integer getAreaNoNotGd(List<AdCodeMsg> adCodeMsgs,String area,String type){

        AdCodeMsg result = adCodeMsgs.get(NumberUtils.INTEGER_ZERO);
        String level = result.getLevel();
        //区级别 需求获取具体围栏信息
        if(Objects.equals(level,DISTRICT)){
            List<AdCodeMsg> collect = adCodeMsgs.stream()
                    .filter(msg -> Objects.equals(msg.getArea(), area)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(collect)){
                throw new BizException(WncBizErrorEnum.ADDRESS_FENCE_NOT_SEND);
            }
            result = collect.get(NumberUtils.INTEGER_ZERO);
        }
        FenceVO fenceVO = fenceMapper.selectFenceById(result.getFenceId());
        if(Objects.equals(type,AREA)){
            return  fenceVO.getAreaNo();
        }
        return  fenceVO.getStoreNo();
    }

    /**
     * 围栏暂停 影响 客户下单以及注册，但是围栏中的城市信息仍然有效
     */
    @Override
    public AjaxResult stopFence(FenceVO fenceVO){

        //校验是否为空
        if(Objects.isNull(fenceVO) || Objects.isNull(fenceVO.getId())){
            return AjaxResult.getErrorWithMsg("参数错误");
        }
        Fence fence = fenceMapper.selectFenceByFenceId(fenceVO.getId());
        if(Objects.equals(fence.getStatus(),FenceStatusEnum.IN_VALID.ordinal())){
            return AjaxResult.getErrorWithMsg("围栏已经被删除");
        }
        if(Objects.equals(fenceVO.getStatus(),FenceStatusEnum.VALID.ordinal())
                && !Objects.equals(fence.getStatus(),FenceStatusEnum.STOP.ordinal())){
            return AjaxResult.getErrorWithMsg("围栏状态为有效");
        }
        if(Objects.equals(fenceVO.getStatus(),FenceStatusEnum.STOP.ordinal())
                && !Objects.equals(fence.getStatus(),FenceStatusEnum.VALID.ordinal())){
            return AjaxResult.getErrorWithMsg("围栏状态为暂停");
        }
        Fence updateFence = new Fence();
        updateFence.setId(fenceVO.getId());
        updateFence.setStatus(fenceVO.getStatus());
        updateFence.setAdminId(baseService.getAdminId());
        fenceMapper.updateFence(updateFence);
        Integer originStatus = Objects.equals(FenceStatusEnum.STOP.ordinal(), fenceVO.getStatus()) ?
                FenceStatusEnum.VALID.ordinal() :  FenceStatusEnum.STOP.ordinal();
        adCodeMsgMapper.updateStatusByFenceId(fenceVO.getId(),fenceVO.getStatus(),originStatus);

        //参数围栏状态是有效，原围栏状态是无效的状态
        if(Objects.equals(fenceVO.getStatus(),FenceStatusEnum.VALID.ordinal()) && Objects.equals(fence.getStatus(),FenceStatusEnum.STOP.ordinal())){
            JSONObject msgJson = new JSONObject();
            msgJson.put("fenceId", fence.getId());
            msgJson.put("storeNo", fence.getStoreNo());
            msgJson.put("areaNo",fence.getAreaNo());
            msgJson.put("packId",fence.getPackId());

            // 发送消息
            logger.info("围栏状态开启变更消息通知:{}",JSON.toJSONString(msgJson));
            mqProducer.send(WncMqConstants.Topic.TOPIC_WNC_FENCE,"tag_fence_status_open" , msgJson);
        }

        return AjaxResult.getOK();
    }

    @Override
    public Set<Integer> selectStoreByAreaNo(Integer areaNo) {

        Fence fence = new Fence();
        fence.setAreaNo(areaNo);
        List<Fence> fenceList = fenceMapper.selectFence(fence);
        if(CollectionUtils.isEmpty(fenceList)){
            throw new DefaultServiceException("围栏异常");
        }

        Set<Integer> storeList = fenceList.stream().map(Fence::getStoreNo).collect(Collectors.toSet());
        return storeList;
    }

    @Override
    public Fence selectFenceDetailById(Integer id) {
        Fence fence = fenceMapper.selectFenceByFenceId(id);
        return fence;
    }

    @Override
    public Boolean selectChangeStore(Integer storeNo) {
        List<ChangeFence> changeFences = changeFenceMapper.selectByChangeToStoreNo(storeNo);
        return CollectionUtils.isEmpty(changeFences) ? Boolean.FALSE : Boolean.TRUE;
    }

    private String changeFenceMsg(FenceVO fenceVO,ChangeFence changeFences){
        LocalDateTime exeTime = changeFences.getExeTime();
        String time = exeTime.format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_THREE));
        String msg = String.format("切仓预约成功,任务将于%s开始执行",time);
        String storeMsg = "";
        if(Objects.equals(changeFences.getType(),ChangeFence.TYPE_CHANGE_STORE)){
            String fenceName = fenceVO.getFenceName();
            WarehouseLogisticsCenter oldCenter = logisticsService.selectByStoreNo(fenceVO.getStoreNo());
            WarehouseLogisticsCenter newCenter = logisticsService.selectByStoreNo(changeFences.getChangeToStoreNo());
            storeMsg = String.format("%s围栏对应城配仓的将由%s变为%s",fenceName ,oldCenter.getStoreName(),newCenter.getStoreName());

        } else {
            String changeAcmId = changeFences.getChangeAcmId();
            Integer changeToFenceId = changeFences.getChangeToFenceId();
            Fence fence = fenceMapper.selectFenceByFenceId(changeToFenceId);
            List<String> adCodeIdList = Arrays.asList(changeAcmId.split(","));
            List<AdCodeMsg> msgList = adCodeMsgMapper.selectByIds(adCodeIdList);
            StringJoiner stringJoiner = new StringJoiner(",");
            msgList.stream().forEach(x -> stringJoiner.add(x.getArea()));
            String codeMsg = stringJoiner.toString();
            storeMsg = String.format("%s的归属围栏将由%s变为%s",codeMsg ,fenceVO.getFenceName(),fence.getFenceName());
        }
        msg = msg + storeMsg;
        return msg;
    }

    /**
     * 新城配仓，切对应的是之前的区域，需要同步库存使用仓
     */
    @XmLock(waitTime = 1000 * 60, key = "(FenceService.insertMapping)")
    public void insertMapping(FenceVO fenceVO){
        Integer storeNo = fenceVO.getStoreNo();
        Integer areaNo = fenceVO.getAreaNo();
        List<WarehouseInventoryMapping> mappings = warehouseInventoryService.selectAll(storeNo, null);
        //新配送仓 无映射信息
        if(CollectionUtils.isEmpty(mappings) && Objects.nonNull(areaNo)){
            Fence selectFence = new Fence();
            selectFence.setAreaNo(areaNo);
            //是否存在映射,存在开始同步
            List<Fence> fenceList = fenceMapper.selectFence(selectFence);
            //获取配送仓，同步库存使用
            if(!CollectionUtils.isEmpty(fenceList)){
                Integer oldStoreNo = selectStoreNoByAreaNo(areaNo);
                //获取映射
                List<WarehouseInventoryMapping> mappingList = warehouseInventoryService.selectAll(oldStoreNo, null);
                mappingList.forEach(mapping -> {
                    WarehouseInventoryMapping insertMapping = new WarehouseInventoryMapping();
                    insertMapping.setSku(mapping.getSku());
                    insertMapping.setStoreNo(storeNo);
                    insertMapping.setWarehouseNo(mapping.getWarehouseNo());
                    warehouseInventoryService.add(insertMapping);
                });
            }
        }
        return;
    }

    @Override
    public List<AllEfficientFenceVo> getAllEfficientFence() {
        //获取所有的围栏信息
        List<AllEfficientFenceVo> efficientFenceVoList = fenceMapper.getAllEfficientFenceInfo();
        if(CollectionUtils.isEmpty(efficientFenceVoList)){
            return Collections.emptyList();
        }
        List<Integer> fenceIds = efficientFenceVoList.stream().map(AllEfficientFenceVo::getFenceId).collect(Collectors.toList());
        List<AdCodeMsg> adCodeMsgList = adCodeMsgMapper.selectByFenceIds(fenceIds, 0);
        Map<Integer, List<AdCodeMsg>> fenceAdCodeMsgMap = adCodeMsgList.stream().collect(Collectors.groupingBy(AdCodeMsg::getFenceId));
        efficientFenceVoList.stream().forEach(fence->{
            //根据围栏查询数据
            List<AdCodeMsg> adCodeMsgs = Optional.ofNullable(fenceAdCodeMsgMap.get(fence.getFenceId())).orElse(Collections.emptyList());
//            List<AdCodeMsg> adCodeMsgs = adCodeMsgMapper.selectByFenceId(fence.getFenceId(), 0);
            Map<String, List<AdCodeMsg>> provinceMap = adCodeMsgs.stream().collect(Collectors.groupingBy(AdCodeMsg::getProvince));

            List<ProvinceVo> provinceVoList = new ArrayList<>();
            provinceMap.keySet().stream().forEach(provinceName ->{
                ProvinceVo provinceVo = new ProvinceVo();
                provinceVo.setProvinceName(provinceName);
                provinceVo.setCityVoList(getCityVoList(provinceMap,provinceName));
                provinceVoList.add(provinceVo);
            });
            fence.setProvinceVoList(provinceVoList);
        });
        efficientFenceVoList = efficientFenceVoList.stream().filter(efficientFenceVo -> efficientFenceVo.getProvinceVoList().size() > 0).collect(Collectors.toList());
        return efficientFenceVoList;
    }

    @Override
    public LocalDate checkCycleDelivery(Integer storeNo, LocalDateTime startTime, Integer[] deliveryFrequent) {
        //计算配送日期
        LocalDate defaultDeliveryDate = startTime.plusDays(2).toLocalDate();

        if (!Objects.equals(0, deliveryFrequent[0])) {
            int dayOfWeek = defaultDeliveryDate.getDayOfWeek().getValue();
            boolean flag = true;
            for (Integer deliveryDay : deliveryFrequent) {
                if (dayOfWeek <= deliveryDay) {
                    defaultDeliveryDate = defaultDeliveryDate.plusDays(deliveryDay - dayOfWeek);
                    flag = false;
                    break;
                }
            }
            if (flag) {
                defaultDeliveryDate = defaultDeliveryDate.plusDays(7 - dayOfWeek + deliveryFrequent[0]);
            }
        }
        //根据地址配送仓判断是否有停运信息
        TmsStopDelivery tmsStopDelivery = tmsStopDeliveryMapper.selectByStoreNo(storeNo);
        //无停运信息直接返回
        if (Objects.isNull(tmsStopDelivery)) {
            return defaultDeliveryDate;
        }
        //如果配送时间在停运时间内,调整配送时间
        if (!defaultDeliveryDate.isBefore(tmsStopDelivery.getShutdownStartTime()) && !defaultDeliveryDate.isAfter(tmsStopDelivery.getShutdownEndTime())) {
            //如果是每天配送，直接在提运结束时间后一天配送
            if(Objects.equals(NumberUtils.INTEGER_ZERO, deliveryFrequent[NumberUtils.INTEGER_ZERO])){
                return tmsStopDelivery.getShutdownEndTime().plusDays(NumberUtils.LONG_ONE);
            }
            //如果非每天配送，默认取提运结束时间后一天配送，判断是否在配送周期内,不在周期内,按照周期添加
            if(!Objects.equals(NumberUtils.INTEGER_ZERO,deliveryFrequent[NumberUtils.INTEGER_ZERO])){
                LocalDate deliveryDate = tmsStopDelivery.getShutdownEndTime().plusDays(NumberUtils.LONG_ONE);
                int dayOfWeek = deliveryDate.getDayOfWeek().getValue();
                boolean flag = true;
                //循环配送周期
                for (Integer deliveryDay : deliveryFrequent) {
                    //如果配送日期小于配送周期
                    if (dayOfWeek <= deliveryDay) {
                        //配送日期加上配送周期-配送日期的天数
                        defaultDeliveryDate = deliveryDate.plusDays(deliveryDay - dayOfWeek);
                        flag = false;
                        break;
                    }
                }
                //配送日期大于配送周期,取下个周期的日期
                if (flag) {
                    defaultDeliveryDate = deliveryDate.plusDays( SEVEN - dayOfWeek + deliveryFrequent[NumberUtils.INTEGER_ZERO]);
                }
                return defaultDeliveryDate;
            }
        }
        return defaultDeliveryDate;
    }

    /**
     * 组装city
     * @param provinceMap
     * @param provinceName
     * @return
     */
    private List<CityVo> getCityVoList(Map<String, List<AdCodeMsg>> provinceMap, String provinceName) {
        List<AdCodeMsg> cityList = provinceMap.get(provinceName);
        //分组去重
        Map<String, List<AdCodeMsg>> cityMap = cityList.stream().filter(bean -> bean.getCity() != null).collect(Collectors.groupingBy(AdCodeMsg::getCity));
        List<CityVo> cityVoList = new ArrayList<>();
        cityMap.keySet().stream().forEach(cityName->{
            if(StringUtils.isNotBlank(cityName)){
                CityVo cityVo = new CityVo();
                cityVo.setCityName(cityName);
                cityVo.setAreaVoList(getAreaList(cityMap,cityName));
                cityVoList.add(cityVo);
            }
        });

        return cityVoList;
    }

    /**
     * 组装area
     * @param cityMap
     * @param cityName
     * @return
     */
    private  ArrayList<Map<String,String>> getAreaList(Map<String, List<AdCodeMsg>> cityMap, String cityName) {
        List<AdCodeMsg> areaList = cityMap.get(cityName);
        //分组去重
        Map<String, List<AdCodeMsg>> areaMap = areaList.stream().collect(Collectors.groupingBy(AdCodeMsg::getArea));
        ArrayList<Map<String,String>> areaNameList = new ArrayList<Map<String,String>>();
        areaMap.keySet().stream().forEach(areaName ->{
            HashMap<String, String> returnMap = new HashMap<>();
            returnMap.put("label",areaName);
            returnMap.put("value",areaName);
            if(!StringUtil.isEmpty(areaName)){
                areaNameList.add(returnMap);
            }
        });
        return areaNameList;
    }

    @Override
    public List<FenceVO> selectByCityName(String cityName) {
        return fenceMapper.selectByCityName(cityName);
    }

    @Override
    public List<FenceVO> selectByCityNames(List<String> cityNames) {
        return fenceMapper.selectByCityNames(cityNames);
    }

    @Override
    public List<String> selectCityNameByAreaNo(Integer areaNo) {
        return fenceMapper.selectCityNameByAreaNo(areaNo);
    }

    @Override
    public List<AdCodeMsg> selectAdCodeMsgByStoreNo(Integer storeNo) {
        Fence fence = new Fence();
        fence.setStoreNo(storeNo);
        List<Fence> fenceList = fenceMapper.selectFence(fence);
        if(CollectionUtils.isEmpty(fenceList)){
            return null;
        }
        List<Integer> fenceIdList = fenceList.stream().map(Fence::getId).collect(Collectors.toList());
        List<AdCodeMsg> codeMsgList = adCodeMsgMapper.selectByFenceIds(fenceIdList, 0);
        return codeMsgList;
    }

    /*@Override
    public boolean checkDeliveryTime(Integer storeNo, LocalDate deliveryTime, Integer[] deliveryFrequent, LocalTime closeTime) {
        if(storeNo == null || deliveryTime == null || deliveryFrequent == null){
            return false;
        }
        if(closeTime == null){
            //校验截单时间
            WarehouseLogisticsCenter wlc = logisticsService.selectByStoreNo(storeNo);
            closeTime = LocalTime.parse(wlc.getCloseTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        }
        LocalDateTime startTime = Global.getStartTime(closeTime);
        //计算最近的配送日期
        LocalDate recentDeliveryDate = startTime.plusDays(2).toLocalDate();
        if (deliveryTime.isBefore(recentDeliveryDate)){
            return false;
        }
        if (deliveryFrequent[0] != 0) {
            int weekDay = deliveryTime.getDayOfWeek().getValue();
            List<Integer> deliveryWeekDays = Arrays.stream(deliveryFrequent).collect(Collectors.toList());
            //存在周期配送客户，判断是否在指定周期
            if (!deliveryWeekDays.contains(weekDay)) {
                return false;
            }
        }
        //根据地址配送仓判断是否有停运信息
        TmsStopDelivery tmsStopDelivery = tmsStopDeliveryMapper.selectByStoreNo(storeNo);
        //无停运信息直接返回
        if (Objects.isNull(tmsStopDelivery)) {
            return true;
        }
        //如果配送时间在停运时间内,配送时间无效
        if (!deliveryTime.isBefore(tmsStopDelivery.getShutdownStartTime()) && !deliveryTime.isAfter(tmsStopDelivery.getShutdownEndTime())) {
            return false;
        }
        return true;
    }*/

    @Override
    public void packHandleFix() {
        List<Fence> fences = fenceMapper.selectFenceAll();
        Map<Integer, List<Fence>> areaFenceListMap = fences.stream().collect(Collectors.groupingBy(Fence::getAreaNo));
        // 创建一个TreeMap，并传入Comparator进行自定义排序
        Map<Integer, List<Fence>> sortedAreaFenceListMap = new TreeMap<>(Comparator.naturalOrder());
        // 将原始的areaFenceListMap中的数据放入sortedMap中
        sortedAreaFenceListMap.putAll(areaFenceListMap);

        Integer packId = 1;
        for (Integer areaNo : sortedAreaFenceListMap.keySet()) {
            if(areaNo == null){
                continue;
            }
            List<Fence> fenceList = sortedAreaFenceListMap.get(areaNo);
            //更新围栏组
            selfService.fencePackUpdate(fenceList.stream().map(Fence::getId).collect(Collectors.toList()), packId);
            packId++;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void fencePackUpdate(List<Integer> fenceIds, Integer packId) {
        if(CollectionUtils.isEmpty(fenceIds) || packId == null){
            return;
        }
        fenceMapper.updateFencePack(fenceIds,packId);
    }
}
