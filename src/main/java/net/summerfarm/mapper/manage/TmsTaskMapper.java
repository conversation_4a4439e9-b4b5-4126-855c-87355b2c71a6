package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.TmsTask;
import net.summerfarm.model.vo.OutTimeMonitorDownloadVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/7/14  10:42
 */
@Repository
public interface TmsTaskMapper {

    int insertTmsTask(TmsTask tmsTask);

    int selectCountTmsTask(@Param("storeNo") Integer storeNo, @Param("deliverTime") LocalDate deliverTime);

    TmsTask selectTmsTask(@Param("storeNo") Integer storeNo, @Param("deliverTime") LocalDate deliverTime, @Param("path") String path);

    int selectTmsTaskByPathId(@Param("id") Integer pathId);

    /**
     * 获取当天的拣货任务
     * @return
     */
    List<TmsTask> selectTodayTmsTask();

    /**
     * 查询当天配送下载信息
     * @return
     * @param day
     */
    List<OutTimeMonitorDownloadVO> selectTodayDownTmsTask(@Param("day") Date day);

    /**
     * 根据配送日期和状态查询任务
     * @param deliveryTime
     * @param status
     * @return
     */
    List<TmsTask> selectTmsTaskByDateStatus(@Param("deliveryTime")LocalDate deliveryTime,@Param("status") Integer status);

    /**
     * 更新
     * @param tmsTask
     */
    void update(TmsTask tmsTask);
}
