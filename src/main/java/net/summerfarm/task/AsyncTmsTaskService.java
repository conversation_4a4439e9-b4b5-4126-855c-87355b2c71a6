package net.summerfarm.task;

import com.google.common.base.Joiner;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mapper.manage.DeliveryCarPathMapper;
import net.summerfarm.mapper.manage.DeliveryPathMapper;
import net.summerfarm.model.domain.DeliveryCarPath;
import net.summerfarm.model.vo.DeliveryPathVO;
import net.summerfarm.warehouse.mapper.WarehouseLogisticsCenterMapper;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/8/15 16:35<br/>
 *
 * <AUTHOR> />
 */
@Component
@Async
public class AsyncTmsTaskService {

    @Resource
    private DeliveryCarPathMapper deliveryCarPathMapper;

    @Resource
    private WarehouseLogisticsCenterMapper warehouseLogisticsCenterMapper;

    /**
     * 根据点位信息调用高德接口获取总距离信息
     * @param storeNo
     * @param deliveryPathVOS
     * @param deliveryTime
     * @return
     */
    public void getTotalDistance(Integer storeNo, List<DeliveryPathVO> deliveryPathVOS,LocalDate deliveryTime) {
        //获取城配仓点位
        WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsCenterMapper.selectByStoreNo(storeNo);
        String warehousePoi = warehouseLogisticsCenter.getPoiNote();

        //过滤没有路线的数据，根据路线分组
        Map<String, List<DeliveryPathVO>> pathDeliveryMapList = deliveryPathVOS.stream()
                .filter(deliveryPath -> StringUtils.isNotBlank(deliveryPath.getPath()))
                .collect(Collectors.groupingBy(DeliveryPathVO::getPath));

        pathDeliveryMapList.forEach((path,deliveryPathVOs)->{

            //按照完成配送时间进行排序
            List<String> pathPoiNoteList = deliveryPathVOs.stream()
                    .sorted(Comparator.comparing(DeliveryPathVO::getSort))
                    .map(DeliveryPathVO::getPoiNote)
                    .collect(Collectors.toList());

            //将城配仓点位放入集合中
            ArrayList<String> poiNoteList = new ArrayList<>();
            poiNoteList.add(warehousePoi);
            poiNoteList.addAll(pathPoiNoteList);

            BigDecimal km = new BigDecimal(0);
            //说明超过16个点位，需要分批调用，还要获取上一次的最后一个作为出发点
            if((poiNoteList.size() - 1) / 16 > 1){
                Iterator<String> poiIterator = poiNoteList.iterator();
                //上一次的未，新一次的开始
                String lastEnd = null;
                //实际就要list里面要有17个点位
                while((poiNoteList.size() - 1) / 16 > 1){
                    ArrayList<String> waypointsList = new ArrayList<>();
                    while(poiIterator.hasNext()){
                        waypointsList.add(poiIterator.next());
                        poiIterator.remove();
                        if(waypointsList.size() == 17 || poiNoteList.size() == 0){
                            //出发点位
                            String firstPoi = lastEnd == null ? waypointsList.get(0) : lastEnd;
                            //终点
                            String endPoi = waypointsList.get(waypointsList.size() - 1);
                            //途经点
                            String waypoints = Joiner.on(";").join(waypointsList.subList(0, waypointsList.size() - 1));
                            lastEnd = endPoi;
                            BigDecimal distance = GaoDeUtil.calculWaypointsDistance(firstPoi, endPoi, waypoints, 2);
                            km = km.add(distance);
                            break;
                        }
                    }
                }

                if(poiNoteList.size() > 0){
                    String endPoi = poiNoteList.get(poiNoteList.size() - 1);
                    String waypoints = Joiner.on(";").join(poiNoteList.subList(0, poiNoteList.size() - 1));
                    BigDecimal distance = GaoDeUtil.calculWaypointsDistance(lastEnd, endPoi, waypoints, 2);
                    km = km.add(distance);
                }
            }else if(pathPoiNoteList.size() > 0){
                String firstPoi = poiNoteList.get(0);
                String endPoi = pathPoiNoteList.get(pathPoiNoteList.size() - 1);
                pathPoiNoteList = pathPoiNoteList.subList(0, pathPoiNoteList.size() - 1);
                String waypoints = Joiner.on(";").join(pathPoiNoteList);
                km = GaoDeUtil.calculWaypointsDistance(firstPoi, endPoi, waypoints, 2);
            }

            //保存到数据库中
            DeliveryCarPath deliveryCarPath = deliveryCarPathMapper.selectByDelivery(storeNo,deliveryTime,path);
            if(deliveryCarPath != null){
                //实际公里数（转为公里数）
                km = km.divide(new BigDecimal(1000)).setScale(2,BigDecimal.ROUND_HALF_UP);
                //预计公里数
                deliveryCarPath.setTotalDistance(km);
                deliveryCarPathMapper.updateByPrimaryKeySelective(deliveryCarPath);
            }
        });
    }
}
