package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.ConversionSkuQuantity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/10/22  14:47
 */
@Repository
public interface ConversionSkuQuantityMapper {

    /**
     *  查询信息
     * @Author: ct
     * @param skuQuantity 信息
     * @return
     **/
    ConversionSkuQuantity selectDetail(ConversionSkuQuantity skuQuantity);

    /**
     *  新增
     * @Author: ct
     * @param skuQuantity
     * @return
     **/
    int saveSkuQuantity(ConversionSkuQuantity skuQuantity);

    /**
     * 批量插入
     * @Author: ct
     * @param list 数据list
     * @return
     **/
    int saveBatchSkuQuantity(List<ConversionSkuQuantity> list);

}
