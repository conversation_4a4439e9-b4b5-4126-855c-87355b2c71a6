package net.summerfarm.dao.stockTransfer;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferDO;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public interface StockTransferDAO {
    /**
     * 插入转换任务
     */
    int insertStockTransfer(StockTransferDO stockTransferDO);

    /**
     * 批量插入
     */
    void batchInsert(@Param("items") List<StockTransferDO> stockTransferDos);

    /**
     * 更新转换任务
     */
    int updateStateById(StockTransferDO stockTransferDO);

    /**
     * 唯一建查询
     */
    StockTransferDO selectById(Long id);

    /**
     * 通过时间和备注查询
     * @param createdAt
     * @param remark
     * @return
     */
    List<StockTransferDO> selectByCreatedAt(@Param("createdAt") LocalDate createdAt, @Param("remark") String remark);

    /**
     * 唯一建和remark查询未完成的任务
     */
    List<StockTransferDO> selectByIdsAndRemark(@Param("ids") List<Long> ids, @Param("remark") String remark,
                                               @Param("warehouseNo") Long warehouseNo);

    /**
     * 唯一建和库存号查询
     *
     */
    List<StockTransferDO> selectByIdAndWarehouseNo(@Param("ids") List<Long> ids, @Param("warehouseNo") Long warehouseNo, @Param("states") List<Integer> states);

    /**
     * 根据备注查询未完成的转换任务
     */
    List<StockTransferDO> selectByRemarkAndWarehouseNo(@Param("warehouseNo") Long warehouseNo, @Param("remark") String remark);

    /**
     * 根据库存仓号和时间查询所有的任务数量
     */
    Long countByWarehouseNo(@Param("warehouseNo") Long warehouseNo, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 复杂分页查询
     * 迭代后去除
     */
    @RequiresDataPermission(originalField = "warehouse_no")
    List<StockTransferDO> pageByIdsAndCondition(@Param("ids") List<Long> ids, @Param("item") StockTransferDO item, @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 查询总数
     */
    int countStockTransfer(StockTransferDO stockTransferDO);

    /**
     * 复杂分页查询总数
     * 迭代后去除
     */
    @RequiresDataPermission(originalField = "warehouse_no")
    int countByIdsAndCondition(@Param("ids") List<Long> ids, @Param("item") StockTransferDO item);

    /**
     * 修复数据接口
     * 请勿使用
     */
    @Deprecated
    List<StockTransferDO> repairData(@Param("maxId") Long maxId, @Param("startId") Long startId, @Param("id") Long id);
}
