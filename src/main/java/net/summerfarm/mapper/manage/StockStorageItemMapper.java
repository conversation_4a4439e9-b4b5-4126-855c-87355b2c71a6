package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.BatchStorageSkuDetailDTO;
import net.summerfarm.model.domain.StockShipmentItemDetail;
import net.summerfarm.model.domain.StockStorageItem;
import net.summerfarm.model.input.purchase.StockDashboardQueryInput;
import net.summerfarm.model.vo.*;
import net.summerfarm.module.pms.model.vo.AllocationOrderItemEntityVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> xiang
 * create at:  2022-02-24
 */
@Repository
public interface StockStorageItemMapper {
    /**
     * 批量新增入库条目
     * @param id
     * @param items
     */
    void insertBatch(@Param("stockTaskId") Integer id, @Param("list") List<AllocationOrderItemEntityVO> items);

    void insert(@Param("stockTaskId")Integer stockTaskId,@Param("entity") AllocationOrderItemEntityVO item);

    List<AllocationOrderItemEntityVO> select(String taskNo);

    /**
     * 查询入库明细
     * @param stockTaskId
     * @return
     */
    List<StockTaskProcessVO> selectByTaskId(Integer stockTaskId);

    /**
     * 查询出库任务条目
     * @param sku
     * @param listNo
     * @return
     */
    StockShipmentItemDetail selectShipmentItem(String sku, String listNo, String purchaseNo, LocalDate qualityDate);

    /**
     * 查询出库任务条目总数量
     * @param sku
     * @param listNo
     * @return
     */
    StockShipmentItemDetail selectShipmentItemSum(String sku, String listNo, String purchaseNo, LocalDate qualityDate);

    /**
     * 查询入库条目
     * @param stockAllocationItemId
     * @return
     */
    StockStorageItem selectById(Integer stockAllocationItemId);

    /**
     * 更新入库条目
     * @param item
     */
    void update(StockStorageItem item);

    /**
     * 查询入库条目list
     * @param taskNo
     * @return
     */
    List<StockStorageItem> selectByTaskNo(@Param("taskNo")String taskNo,@Param("type")Integer type);

    /**
     * 查询入库明细list
     * @param taskNo
     * @return
     */
    List<StockStorageItemDetailVO> selectByTaskNoAndType(@Param("taskNo")String taskNo, @Param("type")Integer type);
    List<StockStorageItem> selectByTaskNo(String taskNo);

    List<StockStorageItem> selectByStockStorageItem(StockStorageItem select);

    List<StockTaskStorageItemVO> selectByStockTaskId(@Param("stockTaskId") Integer id);

    void insertBean(StockStorageItem item);

    List<StockTaskItemDetailVO> selectByItemId(@Param("stockTaskItemId") Integer stockTaskItemId);

    void updateQuantity(@Param("stockStorageItemId")Integer stockStorageItemId, @Param("amount")Integer amount);

    List<StockTaskStorageItemVO> getBatchStorageSkuDetail(BatchStorageSkuDetailDTO batchStorageSkuDetailDTO);

    List<StockTaskStorageItemVO> selectSkuQuantity(@Param("stockTaskIdSet") Set<String> stockTaskIdSet,@Param("sku") String sku);

    StockStorageItem selectItemByTaskIdAndSku(@Param("stockTaskId")Integer stockTaskId,@Param("sku") String sku);

    StockStorageItem selectOneByTaskIdAndSku(@Param("stockTaskId")Integer stockTaskId,@Param("sku") String sku);

    List<StockStorageItem> selectListByTaskId(@Param("stockTaskId")Integer stockTaskId);

    List<StockTaskStorageItemVO> getBatchStorageSkuDetailByIds(@Param("stockTaskIdList")List<Integer> stockTaskIdList);

    List<StockStorageItem> queryTransInItems(StockDashboardQueryInput queryInput);

    BigDecimal countCapacity(@Param("stockTaskIds")List<Integer> stockTaskIds);
}
