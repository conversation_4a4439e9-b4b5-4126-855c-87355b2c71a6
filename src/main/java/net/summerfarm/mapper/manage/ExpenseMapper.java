package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Expense;
import net.summerfarm.model.vo.ExpenseDetailVO;
import net.summerfarm.model.vo.ExpenseVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-11
 */
@Repository
public interface ExpenseMapper {

    //查询导出明细
    List<ExpenseDetailVO> selectExport(Expense selectKeys);

    //查询报销单
    List<ExpenseVO> select(ExpenseVO expense);

    /**
     * 查询报销详情
     * @param atTime
     * @param deliveryCarId
     * @param storeNo
     * @param path
     * @return
     */
    List<ExpenseVO> selectByCondition(@Param("time") LocalDateTime atTime, @Param("deliveryCarId") Integer deliveryCarId, @Param("storeNo") Integer storeNo, @Param("path") String path);

    /**
     * 更新费用报销字段
     * @param expense 费用报销
     */
    void updateExpense(Expense expense);

    /**
     * 根据id查询报销费用
     * @param id
     * @return
     */
    Expense selectById(Integer id);
}
