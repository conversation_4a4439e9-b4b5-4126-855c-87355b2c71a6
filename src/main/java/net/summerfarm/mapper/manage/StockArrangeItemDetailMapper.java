package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockArrangeItemDetail;
import net.summerfarm.model.vo.StockArrangeItemDetailVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> xiang
 * create at:  2021-12-29
 */
@Repository
public interface StockArrangeItemDetailMapper {

    /**
     * 根据任务id查询预约单明细
     * @param id
     * @return
     */
    List<StockArrangeItemDetailVO> selectByTaskId(Integer id);

    /**
     * 根据预约单id查询预约单明细
     * @param id
     * @return
     */
    List<StockArrangeItemDetailVO> selectByStockArrangeId(Integer id);

    List<StockArrangeItemDetail> selectByStockTaskId(Long stockTaskId);

    /**
     * 新增预约单明细
     * @param detail
     * @return
     */
    void insert(StockArrangeItemDetail detail);

    /**
     * 根据id修改预约单明细
     * @param detail
     * @return
     */
    void updateByPrimaryKey(StockArrangeItemDetail detail);

    /**
     * 添加预约单明细
     * @param insertList
     */
    void insertByPurchases(@Param("list") List<StockArrangeItemDetailVO> insertList);

    List<StockArrangeItemDetailVO> selectByStockArrangeItemId(Integer stockArrangeItemId);

    void updateCheckReport(@Param("id") Integer id, @Param("checkReport") String checkReport);

    Integer getQuantityById(Integer id);

    List<StockArrangeItemDetailVO> selectByPurchaseNoSupplierSku(String purchaseNo, Integer supplierId, String sku);

    StockArrangeItemDetailVO selectById(Integer stockArrangeItemDetailId);
}
