package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.TimingOrderRefundWhiteListDTO;
import net.summerfarm.model.domain.TimingOrderRefundWhiteList;
import net.summerfarm.model.input.TimingOrderRefundWhiteListPageQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TimingOrderRefundWhiteListMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TimingOrderRefundWhiteList record);

    int insertSelective(TimingOrderRefundWhiteList record);

    TimingOrderRefundWhiteList selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TimingOrderRefundWhiteList record);

    int updateByPrimaryKey(TimingOrderRefundWhiteList record);

    Boolean selectByOrderNo(@Param("orderNo") String orderNo);

    int batchDeleteWhiteList(@Param("ids") List<Long> ids);

    List<TimingOrderRefundWhiteListDTO> page(TimingOrderRefundWhiteListPageQuery input);

    int batchInsert(@Param("records") List<TimingOrderRefundWhiteList> timingOrderRefundWhiteLists);

    List<TimingOrderRefundWhiteList> selectByOrderNos(@Param("orderNos") List<String> orderNos);

    List<TimingOrderRefundWhiteList> selectAll();

    int batchUpdate(@Param("list") List<TimingOrderRefundWhiteList> refundWhiteLists);

    List<TimingOrderRefundWhiteList> selectByIds(@Param("ids") List<Long> ids);
}