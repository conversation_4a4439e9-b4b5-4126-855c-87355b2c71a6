package net.summerfarm.data.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.StockTaskProcessDetail;
import net.summerfarm.model.domain.Supplier;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockTaskProcessDetailDTO extends StockTaskProcessDetail {
    private Integer pdId;

    private String pdName;

    private Date createdAt;

    private String pack;

    private String weight;

    private String nameRemakes;

    private String transferWeight;

    private String transferPdName;

    private Integer inQuantity;

    private Integer type;

    private Integer supplierId;

    /**
     * 出入库任务单详情表id
     */
    private Integer stockTaskProcessDetailId;

    /**
     * 储存区域
     */
    private String storageArea;

    /**
     * 包装方式
     */
    private String packing;

    private Integer extType;

    /**
     * spu的供应商
     */
    private List<Supplier> suppliers;

    /**
     * skus
     */
    private List<Inventory> skus;

    private Integer printNumber;

    /**
     * 类目类型
     */
    private Integer canPrint;

    /**
     * 预付金额
     */
    private BigDecimal advanceAmount;

    /**
     * 付款金额
     */
    private BigDecimal paymentAmount;

    /**
     * 核销金额
     */
    private BigDecimal writeOffAmount;

    // 任务编号
    private String taskNo;

    private BigDecimal price;
}
