package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.ProductsProperty;
import net.summerfarm.model.domain.ProductsPropertyMapping;
import net.summerfarm.model.vo.ProductsPropertyValueVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductsPropertyMappingMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ProductsPropertyMapping record);

    int insertSelective(ProductsPropertyMapping record);

    ProductsPropertyMapping selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ProductsPropertyMapping record);

    int updateByPrimaryKey(ProductsPropertyMapping record);

    List<ProductsProperty> selectAnchoredProperty(@Param("type") Integer type, @Param("mappingId") Integer mappingId);

    int deleteCategoryKeyPropertyMapping(@Param("categoryId") Integer categoryId, @Param("propertyId") Integer propertyId);

    List<ProductsPropertyMapping> selectCategoryKeyPropertyMapping(@Param("categoryId") Integer categoryId);

    int deleteBySelective(@Param("type") Integer type, @Param("productsPropertyId") Integer productsPropertyId, @Param("mappingIds") List<Integer> mappingIds);

    List<ProductsPropertyValueVO> selectSpuCategoryValue(@Param("pdId") Long pdId, @Param("category") Integer category);


    /**
     * 查询商品规格属性列表
     * @param type
     * @param mappingIds
     * @return
     */
    List<ProductsProperty> selectAnchoredPropertyByMappingIds(@Param("type") Integer type, @Param("mappingIds") List<Long> mappingIds);
}