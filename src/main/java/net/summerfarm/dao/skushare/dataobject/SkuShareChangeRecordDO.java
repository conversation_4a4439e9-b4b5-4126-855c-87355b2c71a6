package net.summerfarm.dao.skushare.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkuShareChangeRecordDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 单号
     */
    private String outOrderNo;

    /**
     * 新待转出数量
     */
    private Integer newRemainingTransferOutQuantity;

    /**
     * 原待转出数量
     */
    private Integer oldRemainingTransferOutQuantity;

    /**
     * 新待转入数量
     */
    private Integer newRemainingTransferInQuantity;

    /**
     * 原待转入数量
     */
    private Integer oldRemainingTransferInQuantity;

    /**
     * 库存变动类型
     */
    private Integer recordType;

    /**
     * 库存变动类型描述
     */
    private String recordTypeDesc;

    /**
     * 备注
     */
    private String remark;

    /**
     * 添加时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

}