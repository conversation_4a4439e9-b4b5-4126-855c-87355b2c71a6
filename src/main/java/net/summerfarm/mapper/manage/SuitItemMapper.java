package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SuitItem;
import net.summerfarm.model.vo.SuitItemVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SuitItemMapper {

    List<SuitItemVO> selectDetailBySuitId(int suitId);

    int insertBatch(@Param("list") List<SuitItemVO> suitItems);

    int update(SuitItem suitItem);

    int deleteById(Integer id);

    List<SuitItemVO> select(Integer areaNo, String sku);

    /**
     * 查询已上架的组合包信息
     * @param areaNo  城市编号
     * @param sku     商品编号
     * @return        当前sku组合包信息
     */
    List<SuitItem> selectOnSaleByAreaNoAndSku(@Param("areaNo") Integer areaNo, @Param("sku") String sku);
}