package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.DTO.inventory.WhiteListQueryDTO;
import net.summerfarm.model.domain.DynamicPriceWhiteList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DynamicPriceWhiteListMapper {
    
    int deleteByPrimaryKey(Long id);

    int insert(DynamicPriceWhiteList record);

    int insertSelective(DynamicPriceWhiteList record);

    DynamicPriceWhiteList selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DynamicPriceWhiteList record);

    int updateByPrimaryKey(DynamicPriceWhiteList record);

    int insertBatch(@Param("list") List<DynamicPriceWhiteList> list);

    List<DynamicPriceWhiteList> selectBySku(@Param("sku") String sku);

    int deleteBySku(@Param("sku") String sku);

    List<String> listByQuery(WhiteListQueryDTO queryDTO);

    List<DynamicPriceWhiteList> listBySkus(@Param("skus") List<String> skus);

    List<DynamicPriceWhiteList> listByCategoryType(@Param("categoryType") Integer categoryType);
}