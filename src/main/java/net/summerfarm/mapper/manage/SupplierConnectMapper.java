package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SupplierConnect;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wjd on 2017/9/22.
 */
@Repository
public interface SupplierConnectMapper {

    void insertBatch(List<SupplierConnect> list);

    List<SupplierConnect> selectBySupplierId(int supplierId);

    void delete(Integer supplierId);

    void deleteByPrimaryKey(Integer id);

    void insert(SupplierConnect supplierConnect);

    void update(SupplierConnect supplierConnect);

    void removeBySupplierId(Integer supplierId);
}
