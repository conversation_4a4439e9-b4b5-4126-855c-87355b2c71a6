package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.PurchasesBack;
import net.summerfarm.model.vo.PurchasesBackVO;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface PurchasesBackMapper {

    PurchasesBack selectByNo(String purchasesBackNo);

    int insert(PurchasesBackVO purchasesBackVO);

    @RequiresDataPermission(originalField = "pb.store_no")
    List<PurchasesBackVO> selectVOS(PurchasesBackVO select);

    PurchasesBackVO selectVO(String purchasesBackNo);

    int update(PurchasesBack purchasesBack);
    /**
     * @Description: 查询出供应商付款为到期支付时未结算的采购单内退单的所有的价格总额
     * @Param:
     * @Return: java.math.BigDecimal
     * @Date: 2020/9/23 20:31
     * @Author: <EMAIL>
     */
    BigDecimal purchasesBackAmountInPlan();
    /**
     * @Description: 查询出供应商付款为公对公现结和私对私现结时未结算的采购单内退单的所有的价格总额
     * @Param:
     * @Return: java.math.BigDecimal
     * @Date: 2020/9/23 20:34
     * @Author: <EMAIL>
     */
    BigDecimal purchasesBackAmountInCash();




}
