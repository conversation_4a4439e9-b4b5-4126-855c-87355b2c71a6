package net.summerfarm.mapper.manage;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import net.summerfarm.model.domain.FeedbackWo;
import net.summerfarm.model.vo.FeedbackWoVO;
import org.apache.ibatis.annotations.Param;
import org.hibernate.validator.constraints.NotEmpty;

import java.time.LocalDateTime;
import java.util.List;

public interface FeedbackWoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FeedbackWo record);

    int insertSelective(FeedbackWo record);

    FeedbackWo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FeedbackWo record);

    int updateByPrimaryKey(FeedbackWo record);

    /**
     * 根据入参内容来获取工单列表
     * @param query
     * @return
     */

    List<FeedbackWoVO> selectBySelectKeys(FeedbackWoVO query);

    /**
     * 查询出某个时间点前的工单类型为大客户，最新的为已反馈但是没回复的工单
     * @param deadLine
     * @return
     */
    List<FeedbackWoVO> selectNotReply(@Param("deadLine") LocalDateTime deadLine);
}