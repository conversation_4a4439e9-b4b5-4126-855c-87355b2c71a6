package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PurchasesBackDetail;
import net.summerfarm.model.param.pms.PurchaseSkuQuery;
import net.summerfarm.model.vo.PurchasesBackDetailVO;
import net.summerfarm.model.vo.pms.PurchaseSkuCountVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface PurchasesBackDetailMapper {

    int insertBatch(List<PurchasesBackDetailVO> list);

    PurchasesBackDetail selectByPrimaryKey(Integer id);

    int update(PurchasesBackDetail detail);

    int delete(Integer id);

    PurchasesBackDetail selectOne(PurchasesBackDetail select);

    int insert(PurchasesBackDetail insert);

    List<PurchasesBackDetailVO> selectByNo(String purchasesBackNo);

    /**
     * 根据采购退货任务编号list查询
     * @param purchasesBackNoList 采购退货任务编号list
     * @return 详情列表
     */
    List<PurchasesBackDetailVO> selectByNoList(@Param("list") List<String> purchasesBackNoList);

    List<PurchasesBackDetailVO> selectVOs(@Param("purchasesBackNo") String purchasesBackNo, @Param("areaNo") Integer areaNo, @Param("purchasesNo") String purchasesNo);

    /**
    * 查询 退货单数量
     * @param sku
     * @param bath
     * @return
    */
    List<PurchasesBackDetail> selectByNoAndSku(@Param("bath") String bath, @Param("sku") String sku);
    /**
    * 查询在待审核状态的退款单
     * @param bath
     * @param sku
     * @return
    */
    List<String> selectAudite(@Param("bath") String bath, @Param("sku") String sku);

    BigDecimal selectBackTotalCost(LocalDate startDate);


    List<PurchasesBackDetail> selectStatusByNoAndSku(@Param("bath") String bath, @Param("sku") String sku);

    /**
     * @Description: 根据批号和sku查询审核状态为已通过的退款单
     * @Param: bath
     * @Param sku
     * @Return: java.util.List<net.summerfarm.model.domain.PurchasesBackDetail>
     * @Date: 2020/9/21
     * @Author: <EMAIL>
     */
    List<PurchasesBackDetail> selectByPurchaseNoAndSku(@Param("bath") String bath, @Param("sku") String sku);

    /**
     * @Description: 查询出来的是未发起结算但是已退货退款的那部分购买退款详情
     * @Param:
     * @Return: java.util.List<net.summerfarm.model.domain.PurchasesBackDetail>
     * @Date: 2020/10/14 7:11
     * @Author: <EMAIL>
     */
    List<PurchasesBackDetail> queryUnsettlePurchaseBackDetail();

    /**
    * 获取退货单详情信息
    */
    List<PurchasesBackDetailVO> selectDetailByNo(String purchasesBackNo);

    /**
     * 查询退货单详情
     * @param detail
     * @return
     */
    List<PurchasesBackDetailVO> select(PurchasesBackDetailVO detail);

    /**
     * 查询退货单详情
     * @param sku
     * @return
     */
    List<PurchasesBackDetailVO> selectByPurchasesNoAndSku(@Param("purchasesNo") String purchasesNo, @Param("sku") String sku);

    /**
     * 查询所有采购退货详情
     * @return
     */
    List<PurchasesBackDetailVO> selectPurchasesBackDetail();

    /**
     * 根据采购批次查询退货退款总金额
     * @param purchaseNo
     * @param supplierId
     * @return
     */
    BigDecimal selectTotalCost(@Param("purchaseNo") String purchaseNo, @Param("supplierId") Integer supplierId);

    /**
     * 根据采购单号和sku查询退货金额
     * @param purchaseNo
     * @param sku
     * @return
     */
    BigDecimal addRefundSettlementAmount(@Param("purchaseNo") String purchaseNo, @Param("sku") String sku);

    /**
     * 根据条件查询退货数量
     * @param query
     * @return
     */
    Integer selectTotalQuantity(PurchasesBackDetailVO query);
    /**
     * 根据采购单号获取审核通过的退货数量和退订数量
     * @param purchasesNo 采购单号
     *
    */
    List<PurchasesBackDetailVO> selectByPurchasesNo(@Param("purchasesNo") String purchasesNo, @Param("sku") String sku);


    /**
     * 根据采购编号查询退订总数量
     * @param batch
     * @return
     */
    Integer selectByBatch(@Param("purchasesNo") String batch);

    /**
     * 根据采购单号和sku查询退订金额
     * @param purchaseNo
     * @param sku
     * @param type 0 退订 1 退货
     * @return
     */
    BigDecimal queryBackAmount(@Param("purchaseNo") String purchaseNo, @Param("sku") String sku, @Param("type") Integer type);

    /**
     * 根据采购单号和sku查询数量
     * @param purchaseNo
     * @param sku
     * @param type 0 退订 1 退货
     * @return
     */
    Integer queryBackQuantity(String purchaseNo, String sku, Integer type);

    // 查询采购退货冻结批次
    Integer selectLockBatch(PurchasesBackDetailVO backDetailVO);

    /**
     * 根据采购单号查询退订金额
     */
    BigDecimal queryBackAmountByPurchaseNo(@Param("purchaseNo") String purchaseNo, @Param("type") Integer type,  @Param("supplierId") Integer supplierId);

    /** 获取退订和退货的数量 **/
    List<PurchaseSkuCountVO> selectBackCountBySkuList(@Param("skuList") List<PurchaseSkuQuery> skuList);

    List<Long> selectNotSupplierIdIds();

    void updateSupplierIdById(@Param("supplierId") Long supplierId, @Param("id") Long id);
}
