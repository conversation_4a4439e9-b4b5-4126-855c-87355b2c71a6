package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.AccountChange;
import org.springframework.stereotype.Repository;

/**
 * Created by wjd on 2017/12/20.
 */

@Repository
public interface AccountChangeMapper {

    @RequiresDataPermission(originalField = "m.area_no")
    AccountChange select(AccountChange accountChange);

    void update(AccountChange accountChange);

}
