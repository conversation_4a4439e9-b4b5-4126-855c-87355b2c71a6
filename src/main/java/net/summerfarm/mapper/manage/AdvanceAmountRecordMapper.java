package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AdvanceAmountRecord;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AdvanceAmountRecordMapper {

    Integer insertAmountRecord(AdvanceAmountRecord record);

    List<AdvanceAmountRecord> selectAmount(String purchaseNo);


    Integer insertRecordBath(List<AdvanceAmountRecord> records);

}
