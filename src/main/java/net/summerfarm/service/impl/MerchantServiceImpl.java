package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.constant.ManagerConstant;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.common.util.es.EsIndexContext;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.*;
import net.summerfarm.enums.bms.ContactLogEnum;
import net.summerfarm.enums.market.GroupHeadFlagEnum;
import net.summerfarm.es.EsClientPoolUtil;
import net.summerfarm.facade.MerchantCancelFacade;
import net.summerfarm.facade.mall.MerchantSubFacade;
import net.summerfarm.facade.marketing.DistributionRulesFacade;
import net.summerfarm.facade.marketing.dto.DistributionRulesDTO;
import net.summerfarm.facade.msg.FeiShuPersonalMsgFacade;
import net.summerfarm.facade.usercenter.MerchantStoreExtCommandFacade;
import net.summerfarm.facade.wnc.ContactDeliveryRuleQueryFacade;
import net.summerfarm.facade.wnc.DeliveryFenceQueryFacade;
import net.summerfarm.facade.wnc.dto.*;
import net.summerfarm.mall.client.req.MerchantCancelInputReq;
import net.summerfarm.mall.client.req.merchant.TagManageReq;
import net.summerfarm.mapper.ContactOperateLogMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.mapper.offline.CrmMerchantDayGmvMapper;
import net.summerfarm.mapper.offline.CrmMerchantDayLabelMapper;
import net.summerfarm.mapper.offline.CrmMerchantMonthGmvMapper;
import net.summerfarm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.model.DTO.merchant.ContactAddressRemark;
import net.summerfarm.model.converter.merchant.ContactUpdateConverter;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.crm.ContactOperateLog;
import net.summerfarm.model.input.DistributionRulesInsertInput;
import net.summerfarm.model.input.FeedbackWoOrderInput;
import net.summerfarm.model.input.MerchantCancelReq;
import net.summerfarm.model.input.MerchantReq;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.vo.merchant.ContactAddressRemarkUpdateVO;
import net.summerfarm.model.vo.merchant.ContactQueryVO;
import net.summerfarm.model.vo.merchant.ContactUpdateVO;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.service.*;
import net.summerfarm.service.contact.ContactService;
import net.summerfarm.task.AsyncTaskService;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.marketing.center.client.freight.req.DistributionRulesDeleteReq;
import net.xianmu.marketing.center.client.freight.req.DistributionRulesInfoReq;
import net.xianmu.redis.support.lock.annotation.XmLock;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.GeoDistanceSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static net.summerfarm.contexts.ResultConstant.AREA_NOT_EXIST;
import static net.summerfarm.contexts.ResultConstant.DEFAULT_FAILED;

/**
 * 商户业务接口实现
 *
 * @author: <EMAIL>
 * @Date: 2016/7/19
 */
@Service
public class MerchantServiceImpl extends BaseService implements MerchantService {

    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private MerchantLifecycleMapper merchantLifecycleMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private MerchantCancelFacade merchantCancelFacade;
    @Lazy
    @Resource
    private CouponService couponService;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;
    @Lazy
    @Resource
    private AsyncTaskService asyncTaskService;
    @Resource
    private AccountChangeMapper accountChangeMapper;
    @Lazy
    @Resource
    private MemberService memberService;
    @Resource
    private OrdersMapper ordersMapper;
    @Lazy
    @Resource
    private FollowUpRelationService followUpRelationService;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private MerchantLeadsMapper merchantLeadsMapper;
    @Resource
    private Executor asyncServiceExecutor;
    @Resource
    private DiscountCardToMerchantMapper discountCardToMerchantMapper;
    @Resource
    private VisitPlanMapper visitPlanMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private MerchantCluePoolMapper merchantCluePoolMapper;
    @Resource
    private MerchantUpdateRecordMapper merchantUpdateRecordMapper;
    @Lazy
    @Resource
    private AreaService areaService;
    @Resource
    private MerchantReviewRecordMapper merchantReviewRecordMapper;
    @Resource
    private MerchantOuterMapper merchantOuterMapper;
    @Resource
    private InvoiceMerchantRelationMapper invoiceMerchantRelationMapper;
    @Resource
    private WarehouseLogisticsService logisticsService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private FenceService fenceService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private MerchantMonthPurmoneyMapper merchantMonthPurmoneyMapper;
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private PanicBuyMapper panicBuyMapper;
    @Resource
    CirclePeopleRuleMapper circlePeopleRuleMapper;
    @Resource
    private CrmManageAreaMapper crmManageAreaMapper;
    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private OrderOuterInfoService orderOuterInfoService;
    @Resource
    private MerchantExtMapper merchantExtMapper;
    @Resource
    private DistributionRuleMapper distributionRuleMapper;
    @Resource
    DataSynchronizationInformationMapper dataSynchronizationInformationMapper;
    @Resource
    CrmMerchantMonthGmvMapper crmMerchantMonthGmvMapper;
    @Resource
    CrmMerchantDayGmvMapper crmMerchantDayGmvMapper;
    @Resource
    private CrmBdConfigMapper crmBdConfigMapper;
    @Resource
    private FollowWhiteListMapper followWhiteListMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private CrmManageBdMapper crmManageBdMapper;
    @Resource
    private CrmCommissionMerchantLevelMapper crmCommissionMerchantLevelMapper;
    @Resource
    private MchEnterpriseAddressRelationMapper mchEnterpriseAddressRelationMapper;
    @Resource
    private CrmMerchantDayLabelMapper crmMerchantDayLabelMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private InvoiceConfigMapper invoiceConfigMapper;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private MerchantLabelService merchantLabelService;
    @Resource
    private MerchantCancelService merchantCancelService;
    @Resource
    private ContactOperateLogMapper  contactOperateLogMapper;
    @Resource
    private MerchantSubFacade merchantSubFacade;
    @Resource
    private DistributionRulesFacade distributionRulesFacade;
    @Resource
    private ContactDeliveryRuleQueryFacade deliveryRuleQueryFacade;
    @Resource
    MerchantStoreExtCommandFacade merchantStoreExtCommandFacade;
    @Resource
    private DeliveryFenceQueryFacade deliveryFenceQueryFacade;
    @Resource
    private DynamicConfig dynamicConfig;
    @Resource
    private ContactDeliveryRuleQueryFacade contactDeliveryRuleFacade;

    @Autowired
    MqProducer mqProducer;

    @Resource
    private ConfigService configService;
    @Resource
    private CrmBdOrgMapper crmBdOrgMapper;
    @Resource
    private ContactService contactService;

    /**
     * bd释放类型
     */
    private final Integer RELEASE_TYPE = 1;
    /**
     * 所有的merchatType
     */
    private final static Integer ALL_MERCHANT_TYPE = -1;


    private static final Logger logger = LoggerFactory.getLogger(MerchantService.class);
    private static final String BATCH_FAIL_MERCHANT = "batch_fail_merchant";
    private static final String EXAMINE_STORE_ROBOT_CONFIG = "examine_store_robot_config";
    /**
     * 免配日字段
     */
    private static final String FREE_DELIVERY_WEEK = "freeDeliveryWeek";

    /**
     * status字段
     */
    private static final String STATUS = "operate_status";

    /**
     * id字段
     */
    private static final String ID = "m_id";
    /**
     *
     */
    private static final Long LAST_FEISHU_ADMIN_ID = 1891L;
    private FeiShuPersonalMsgFacade feiShuPersonalMsgFacade;


    @Override
    public AjaxResult selectMerchantInfo(int pageIndex, int pageSize, MerchantVO selectKeys) {
        selectKeys = Optional.ofNullable(selectKeys).orElse(new MerchantVO());

        // 若有标签筛选
        List<MerchantVO> merchantInfoList = new ArrayList<>(pageSize);
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_LABEL.getTableName());
        Integer dateFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
        PageHelper.startPage(pageIndex, pageSize);
        if(Objects.nonNull(selectKeys.getMerchantLabel())){
            // 构建标签查询条件
            String[] merchantLabel = selectKeys.getMerchantLabel().trim().split(Global.SEPARATING_SYMBOL);
            List<String> merchantLabelList = Arrays.asList(merchantLabel);
            selectKeys.setMerchantLabelList(merchantLabelList);
            // 查询具有该标签的客户id具体信息,循环pageSize次
            merchantInfoList = crmMerchantDayLabelMapper.selectMidListByInput(selectKeys, dateFlag);
            // 删除生产库与离线库数据不一致的数据
            List<MerchantVO> deleteMerchantInfoList = new ArrayList<>(pageSize);
            for (MerchantVO merchant : merchantInfoList) {
                selectKeys.setmId(merchant.getmId());
                List<MerchantVO> merchantVOS = this.queryMerchantInfoDetail(selectKeys);
                if(CollectionUtil.isNotEmpty(merchantVOS)){
                    BeanCopyUtil.copyProperties(merchantVOS.get(0),merchant);
                }else {
                    deleteMerchantInfoList.add(merchant);
                }
            }
            merchantInfoList.removeAll(deleteMerchantInfoList);
        }else {
            merchantInfoList = this.queryMerchantInfoDetail(selectKeys);
        }

        return AjaxResult.getOK(PageInfoHelper.createPageInfo(merchantInfoList));
    }

    /**
     * 查询商户信息
     * @param selectKeys 查询条件
     * @return 商户信息
     */
    private List<MerchantVO> queryMerchantInfoDetail(MerchantVO selectKeys){
        selectKeys = Optional.ofNullable(selectKeys).orElse(new MerchantVO());
        // 手机号搜索,需同时搜索注册手机号及子账号手机号
        if(Objects.nonNull(selectKeys.getPhone())){
            int islock = selectKeys.getIslock() == null ? ALL_MERCHANT_TYPE : selectKeys.getIslock().intValue();
            List<Long> mIdList = merchantMapper.selectByPhone(selectKeys.getPhone(), islock);
            //未检测到数据直接返回
            if (CollectionUtil.isEmpty(mIdList)){
                return new ArrayList<>();
            }

            selectKeys.setmIdList(mIdList);
        }

        // isLock 0 审核通过 1 审核中 2 审核未通过 3 账号被拉黑
        // chat 1 多地址 2 更换账号
        List<MerchantVO> merchantVOs = merchantMapper.select(selectKeys);
        if (CollectionUtil.isNotEmpty(merchantVOs)) {
            this.fillMerchantDetailVO(selectKeys,merchantVOs);
        }
        return merchantVOs;
    }

    private void fillMerchantDetailVO(MerchantVO selectKeys, List<MerchantVO> merchantVOs) {
        InvoiceConfigVO queryInvoiceConfigVO = new InvoiceConfigVO();
        queryInvoiceConfigVO.setType(NumberUtils.INTEGER_ZERO);
        Map<String, Object> inviterSelectKeys = new HashMap<>();
        Contact select = new Contact();
        AccountChange accountChange = new AccountChange();
        List<Integer> adminIds = merchantVOs.stream().map(MerchantVO::getAuditUser).distinct().collect(Collectors.toList());
        Map<Integer, String> adminMap = getAdminMap(adminIds);
        for (MerchantVO merchantVO : merchantVOs) {
            if (merchantVO.getAuditUser()!=null){
                merchantVO.setAuditUserName(adminMap.get(merchantVO.getAuditUser()));
            }
            // 获取商户区域
            Optional.ofNullable(merchantVO.getAreaNo())
                    .map(areaNo -> areaMapper.selectByAreaNo(merchantVO.getAreaNo()))
                    .ifPresent(a -> {
                        merchantVO.setAreaName(a.getAreaName());
                        merchantVO.setMapSection(a.getMapSection());
                    });

            // 获取工商信息
            queryInvoiceConfigVO.setMerchantId(merchantVO.getmId());
            List<InvoiceConfigVO> invoiceConfigVOs = invoiceConfigMapper.selectByKeys(queryInvoiceConfigVO).stream().filter(
                    it -> it.getType() <= 1
            ).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(invoiceConfigVOs)){
                merchantVO.setInvoiceTitle(invoiceConfigVOs.get(NumberUtils.INTEGER_ZERO).getInvoiceTitle());
            }

            // 审核通过获取账号地推人员
            boolean isApproved = Objects.nonNull(selectKeys.getIslock()) &&
                    (Objects.equals(0, selectKeys.getIslock().intValue())
                            || Objects.equals(3, selectKeys.getIslock().intValue()));
            if(isApproved){
                FollowUpRelation followUpRelation = followUpRelationMapper.selectByMid(merchantVO.getmId().intValue());
                if (!ObjectUtils.isEmpty(followUpRelation)){
                    String adminName =  followUpRelation.getReassign() ? "默认邀请码" : followUpRelation.getAdminName();//NOSONAR
                    merchantVO.setAdminRealname(adminName);
                }
            }

            // 大客户合作方式
            Optional.ofNullable(merchantVO.getAdminId())
                    .map(adminId -> adminMapper.selectByPrimaryKey(merchantVO.getAdminId()))
                    .ifPresent(a -> merchantVO.setContractMethod(a.getContractMethod()));

            // 用户分享状态
            if (StringUtils.isNotBlank(merchantVO.getInviterChannelCode())) {
                inviterSelectKeys.put("channelCode", merchantVO.getInviterChannelCode());
                Merchant inviter = merchantMapper.selectOne(inviterSelectKeys);
                if (Objects.nonNull(inviter)) {
                    merchantVO.setInviterPhone(inviter.getMname() + "-" + inviter.getPhone());
                } else {
                    merchantVO.setInviterPhone(merchantVO.getInviterChannelCode());
                }
            }

            select.setStatus(1);
            if (Objects.nonNull(selectKeys.getChat()) && selectKeys.getChat() == 1) {
                select.setStatus(3);
            }
            select.setmId(merchantVO.getmId());
            List<Contact> contacts = contactMapper.select(select);
            for(Contact contact: contacts) {
                try{
                    Integer areaNo = fenceService.getAreaNo(contact.getCity(), contact.getArea());
                    if(areaNo != null){
                        Area area = areaMapper.queryByAreaNo(areaNo);
                        if(area != null){
                            contact.setAreaName(area.getAreaName());
                            contact.setAreaNo(area.getAreaNo());
                        }
                    }
                } catch (Exception e){
                    logger.warn("meet error", e);
                }
                contact.initAddrRemark();
            }
            merchantVO.setContacts(contacts);

            if (Objects.nonNull(selectKeys.getChat()) && selectKeys.getChat() == 2) {
                accountChange.setStatus(1);
                accountChange.setmId(merchantVO.getmId());
                merchantVO.setAccountChange(accountChangeMapper.select(accountChange));
            }
        }

    }

    @Override
    public AjaxResult selectOne(Long mId) {
        MerchantVO merchant = (MerchantVO) merchantMapper.selectByPrimaryKey(mId);
        if (merchant == null) {
            return AjaxResult.getErrorWithMsg("无此用户!");
        }
        //Integer areaNo = fenceService.getAreaNo(merchant.getPoiNote());
        //merchant.setInMapSection(Objects.equals(areaNo, merchant.getAreaNo()));
        merchant.setInMapSection(true);
        Contact query = new Contact();
        query.setmId(merchant.getmId());
        query.setStatus(1);
        List<Contact> contacts = contactMapper.select(query);
        merchant.setContacts(contacts);
        //先判断用户是否是大客户，再根据配置判断用户是否充送
        if (ObjectUtils.isEmpty(merchant.getAdminId())){
            //单店用户
            Config config = configMapper.selectOne("singleSwitch");
            if (Objects.equals(Integer.valueOf(config.getValue()), SendCouponEnum.OPEN.getId())){
                //充送
                merchant.setSendCoupon(SendCouponEnum.OPEN.getId());
            }else {
                //不充送
                merchant.setSendCoupon(SendCouponEnum.CLOSE.getId());
            }
        }else{
            //大客户先判断品牌总开关情况
            Config config = configMapper.selectOne("brandSwitch");
            if (Objects.equals(Integer.valueOf(config.getValue()), SendCouponEnum.OPEN.getId())){
                //如果品牌总开关开启，遵照品牌的开关情况
                Admin admin = adminMapper.selectAdminSwitch(merchant.getAdminId());
                merchant.setSendCoupon(admin.getAdminSwitch());
            } else {
                merchant.setSendCoupon(SendCouponEnum.CLOSE.getId());
            }
        }
        return AjaxResult.getOK(merchant);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult reviewAccount(Integer id, String remark, Integer status) {
        AccountChange change = new AccountChange();
        change.setId(id);
        AccountChange accountChange = accountChangeMapper.select(change);
        if (accountChange == null) {
            return AjaxResult.getError();
        }
        change.setRemark(remark);
        change.setStatus(status);
        accountChangeMapper.update(change);
        if (status == 2) {
            Merchant merchant = new Merchant();
            merchant.setOpenid(accountChange.getOpenid());
            merchant.setUnionid(accountChange.getUnionid());
            merchant.setMpOpenid("");
            merchant.setPhone(accountChange.getNewPhone());
            merchant.setMcontact(accountChange.getNewContact());
            merchant.setmId(accountChange.getmId());
            merchant.setChangePop(0);
            merchantMapper.updateByPrimaryKeySelective(merchant);

            //店长子账号处理
            MerchantSubAccount account = merchantSubAccountMapper.selectManageByMid(accountChange.getmId());
            MerchantSubAccount record = new MerchantSubAccount();
            record.setAccountId(account.getAccountId());
            record.setOpenid(accountChange.getOpenid());
            record.setUnionid(accountChange.getUnionid());
            record.setMpOpenid("");
            record.setPhone(accountChange.getNewPhone());
            record.setContact(accountChange.getNewContact());
            merchantSubAccountMapper.updateSelective(record);

            //更新之后推送门店信息
            //pushMerchantInfo(merchant.getmId());
        }
        return AjaxResult.getOK();
    }

    /**
     * 检查是否是茶百道门店
     * @param mId
     * @return
     */
    private boolean checkCBD(Long mId) {
        Merchant merchant = merchantMapper.selectByMId(mId);
        if (Objects.isNull(merchant.getAdminId())){
            return false;
        }
        String adminIdStr = configMapper.selectOne(Global.CBD_ADMIN_ID).getValue();
        return Arrays.stream(adminIdStr.split(net.summerfarm.common.util.StringUtils.SEPARATING_SYMBOL)).anyMatch(el -> el.equals(merchant.getAdminId().toString()));
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult reviewContact(Long id, String remark, Integer status, String address, String poiNote) {
        logger.info("接收到消息 contactId{} ",id);
        Contact update = new Contact();
        update.setContactId(id);
        update.setRemark(remark);

        Contact record = contactMapper.selectByPrimaryKey(id);
        if (record == null){
            logger.info("接收到消息 contactId 没查询到{} ",id);
            return AjaxResult.getOK();
        }
        Merchant merchant = merchantMapper.selectByPrimaryKey(record.getmId());
        if (merchant == null){
            return AjaxResult.getErrorWithMsg("店铺不存在");
        }
        if (status == 4) {
            update.setStatus(4);
        } else {
            if (StringUtils.isBlank(poiNote)) {
                return AjaxResult.getErrorWithMsg("请先选择poi地址");
            }
            //获取省市区 围栏
            record.setPoiNote(poiNote);
            Integer storeNo = this.getStoreNo(isPopMerchantV2(merchant.getBusinessLine()),record);
            if(Objects.isNull(storeNo)){
                return AjaxResult.getErrorWithMsg("暂无归属围栏");
            }
            // 获取地址到仓库地址的距离
            BigDecimal distance = BigDecimal.ZERO;
            distance = getDistance(poiNote, storeNo, distance);

            update.setStatus(1);
            update.setPoiNote(poiNote);
            update.setDistance(distance);
            update.setAddress(address);
            update.setmId(record.getmId());

            update.setStoreNo(storeNo);
            logger.info("接收到消息 contactId 更新状态{} ",id);

        }
        contactMapper.updateByPrimaryKeySelective(update);
        ContactOperateLog contactOperateLog = new ContactOperateLog();
        contactOperateLog.setMId(record.getmId());
        contactOperateLog.setContactId(id);
        contactOperateLog.setCreateTime(LocalDateTime.now());
        contactOperateLog.setUpdateTime(LocalDateTime.now());
        contactOperateLog.setContext("审核地址通过");
        contactOperateLog.setOperateType(ContactLogEnum.OperateType.APPROVE.ordinal());
        contactOperateLog.setOperateName(getAdminName());
        contactOperateLog.setOperateSource(ContactLogEnum.OperateSource.MANAGE.ordinal());
        contactOperateLogMapper.insertSelective(contactOperateLog);
        logger.info("地址日志加入{} ",id);
        // 后置处理调用wnc保存城配仓和配送周期
        if(this.isPopMerchantV2(merchant.getBusinessLine())) {
            //先根据仓网接口返回的POP城配仓属性判断是T+1还是T+2，如果是T+2则不需要调用
            Integer popFulfillmentway = deliveryFenceQueryFacade.getPopFulfillmentway(record.getCity(), record.getArea(), update.getPoiNote(), record.getmId());
            if (Objects.equals(popFulfillmentway, CommonStatus.NO.getCode())) {
                contactDeliveryRuleFacade.popDeliveryRuleConfigSaveOrUpdate(update.getContactId(), update.getStoreNo());
            }
        }
        return AjaxResult.getOK();
    }
   
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult reviewContact(Long id, String remark, Integer status, String address, String poiNote, boolean sleep) {
        if (sleep) {
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                logger.warn(e.getMessage());
            }
        }
        return reviewContact(id, remark, status, address, poiNote);
    }

    /**
     * 获取联系人地址到仓库的距离
     * @param contactPoiStr 联系人地址poi
     * @param storeNo 仓库no
     * @param distance 距离
     * @return
     */
    private BigDecimal getDistance(String contactPoiStr, Integer storeNo, BigDecimal distance) {
        WarehouseLogisticsCenter logisticsCenter = logisticsService.selectByStoreNo(storeNo);
        logisticsCenter = Optional.ofNullable(logisticsCenter).orElse(new WarehouseLogisticsCenter());
        PoiVO logisticsCenterPoi = SplitUtils.string2poi(logisticsCenter.getPoiNote());
        PoiVO contactPoi = SplitUtils.string2poi(contactPoiStr);

        if(Objects.nonNull(logisticsCenterPoi) && Objects.nonNull(contactPoi)){
            double distanceDou = DistanceUtil.getDistance(logisticsCenterPoi.getLon(),logisticsCenterPoi.getLat()
                    ,contactPoi.getLon(),contactPoi.getLat());
            distance = new BigDecimal(distanceDou);
        }
        return distance;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult reviewMerchant(int id, MerchantVO merchantVO, boolean auto) throws IOException {
        logger.info("开始用户审核：id:{}, merchantVO:{}, auto:{}", id, JSON.toJSONString(merchantVO), auto);
        //状态值合理判断，0通过，1未审核，2审核不通过
        if (id < 0 && (merchantVO.getState() != 0 || merchantVO.getState() != 1 || merchantVO.getState() != 2)) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        //判断id对应用户是否存在
        Merchant merchant = merchantMapper.selectByPrimaryKey((long) id);
        if (merchant == null) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        boolean isPopMerchant = this.isPopMerchantV2(merchant.getBusinessLine());
        //自动审核
        if(Objects.isNull(getAdminId()) && !isPopMerchant){
            Contact contact = new Contact();
            contact.setCity(merchantVO.getCity());
            contact.setArea(merchantVO.getArea());
            Integer areaNo = fenceService.getAreaNo(merchantVO.getPoiNote(),contact);
            // 自动审核，默认非团购团长
            merchantVO.setGroupHeadFlag(GroupHeadFlagEnum.NORMAL.getFlag());
            if(Objects.isNull(areaNo)){
                merchantVO.setState(2);
                merchantVO.setExamineType(0);
                merchantVO.setRemark("该店铺地址不在运营范围内");
            }

        }

        //只有审核状态为默认值的用户才能被审核
        if (merchant.getIslock() != 1) {
            return AjaxResult.getError(ResultConstant.REQUEST_DONE);
        }

        if ("大客户".equals(merchantVO.getSize()) && (merchantVO.getAdminId() == null || merchantVO.getDirect() == null)) {
            return AjaxResult.getErrorWithMsg("所属总部或者客户属性不能为空");
        }
        //原先大客户变成普通客户 adminId 和 direct 变成空
        if ("大客户".equals(merchant.getSize()) && !"大客户".equals(merchantVO.getSize())) {
            Merchant record = new Merchant();
            record.setmId(merchant.getmId());
            record.setDirect(null);
            record.setAdminId(null);
            record.setSkuShow(null);
            record.setRemark(merchantVO.getRemark());
            merchantMapper.updateInfo(record);
        }
        //大客户默认为ka
        if(Objects.equals(Global.BIG_MERCHANT,merchantVO.getSize()) && StringUtils.isEmpty(merchantVO.getMerchantType())){
            merchantVO.setMerchantType("普通");
        }
        if(!StringUtils.isEmpty(merchantVO.getEsId())){
            int amount = merchantCluePoolMapper.queryEsIdNumber(merchantVO.getEsId());
            if(amount > 0){
                return AjaxResult.getErrorWithMsg("当前线索已经被其他用户绑定");
            }
        }
        if (merchantVO.getMname() == null || "".equals(merchantVO.getMname())) {
            return AjaxResult.getErrorWithMsg("商铺名不能为空");
        } else if (!merchantVO.getMname().equals(merchant.getMname())) {
            HashMap selectKey = new HashMap();
            selectKey.put("mname", merchantVO.getMname());
            Merchant result = merchantMapper.selectOne(selectKey);
            if (result != null) {
                return AjaxResult.getErrorWithMsg("该商铺名已存在");
            }
        }
        if (merchantVO.getMcontact() == null || "".equals(merchantVO.getMcontact())) {
            return AjaxResult.getErrorWithMsg("联系人不能为空");
        }
        if (merchantVO.getPhone() == null || "".equals(merchantVO.getPhone())) {
            return AjaxResult.getErrorWithMsg("手机号不能为空");
        } else if (!merchantVO.getPhone().equals(merchant.getPhone())) {
            HashMap selectKey = new HashMap();
            selectKey.put("phone", merchantVO.getPhone());
            Merchant result = merchantMapper.selectOne(selectKey);
            if (result != null) {
                return AjaxResult.getErrorWithMsg("该手机号已被注册");
            }
        }
        Pattern phonePattern = ManagerConstant.PHONE_PATTERN;
        if (!phonePattern.matcher(merchantVO.getPhone()).find()) {
            return AjaxResult.getErrorWithMsg("手机号格式不正确");
        }

        if (merchantVO.getPrintOutTMSConfig()!=null){
            updateTmsConfig(merchant.getmId(), merchantVO.getPrintOutTMSConfig());
        }


        Area area = areaMapper.selectByAreaNo(merchantVO.getAreaNo());
        if (merchantVO.getState() == 0 && (merchantVO.getAreaNo() == null || area == null)) {
            return AjaxResult.getError(AREA_NOT_EXIST);
        }

        if (merchantVO.getAreaNo() != null) {
            //初始化用户等级
            if (area != null) {
                List<MemberVO> memberVOS = JSON.parseArray(area.getMemberRule(), MemberVO.class);
                if (!CollectionUtils.isEmpty(memberVOS) && !"大客户".equals(merchantVO.getSize())) {
                    merchant.setGrade(0);
                }
            }
        }

        logger.info("商户:{},{}审核，审核人:{}", merchant.getMname(), merchantVO.getState() == 0 ? "通过" : "未通过", this.getAdminName());
        ReflectUtils.copyData(merchant, merchantVO, "phone", "mname", "mcontact", "remark", "areaNo", "size", "adminId", "direct", "skuShow", "poiNote", "showPrice");
        merchant.setAuditUser(auto ? 0 : this.getAdminId());
        merchant.setIslock(Byte.valueOf(merchantVO.getState() + ""));

        Date now = new Date();
        merchant.setAuditTime(now);
        merchant.setCluePool(merchantVO.getCluePool());
        if (StringUtils.isNotBlank(merchantVO.getAddress())) { //修改注册地址
            merchant.setAddress(merchantVO.getAddress());
        }
        merchant.setHouseNumber(merchantVO.getHouseNumber());
        merchant.setEnterpriseScale(merchantVO.getEnterpriseScale());
        merchant.setType(merchantVO.getType());
        merchant.setCompanyBrand(merchantVO.getCompanyBrand());
        merchant.setMerchantType(merchantVO.getMerchantType());
        merchant.setExamineType(merchantVO.getExamineType());
        merchant.setRemark(merchantVO.getRemark());
        merchant.setOperateStatus(merchantVO.getOperateStatus());
        merchantMapper.updateByPrimaryKeySelective(merchant);


        if (merchantVO.getState() == 0) {
            if (StringUtils.isBlank(merchantVO.getPoiNote())) {
                throw new DefaultServiceException("请先选择poi地址");
            }
            // 审核通过,调整contact 联系人信息
            Contact contact;
            List<Contact> contacts = contactMapper.selectAllByMId(merchant.getmId());
            if(CollUtil.isNotEmpty(contacts)) {
                contact = contacts.get(0);
                buildContact(contact, merchant, merchantVO);
                contactMapper.updateByPrimaryKeySelective(contact);
            } else {
                contact = new Contact();
                buildContact(contact, merchant, merchantVO);
                contactMapper.insertSelective(contact);
            }
            // 保存pop城配仓信息
            if(isPopMerchant) {
                //先根据仓网接口返回的POP城配仓属性判断是T+1还是T+2，如果是T+2则不需要调用
                Integer popFulfillmentway = deliveryFenceQueryFacade.getPopFulfillmentway(contact.getCity(), contact.getArea(), contact.getPoiNote(), merchant.getmId());
                if (Objects.equals(popFulfillmentway, CommonStatus.NO.getCode())) {
                    contactDeliveryRuleFacade.popDeliveryRuleConfigSaveOrUpdate(contact.getContactId(), contact.getStoreNo());
                }
            }
            afterReviewMerchant(merchant,merchantVO);
        }
        //外部对接 外部门店编码不为空 进行门店关联
        merchantOuterMapping(merchant,merchantVO,merchantVO.getOuterMappings());

        //店长子账号处理
        MerchantSubAccount account = merchantSubAccountMapper.selectManageByMid(merchant.getmId());
        account.setAuditTime(now);
        account.setAuditUser(auto ? 0 : this.getAdminId());
        if (merchantVO.getState() == 0) {
            account.setStatus(SubAccountStatus.AUDIT_SUC.ordinal());
        } else {
            // 鲜沐暂时没有审核不通过的状态，所以这里不动
            //account.setDeleteFlag(0);
        }
        merchantSubAccountMapper.updateSelective(account);
        // esID暂无 不处理数据
        if(!StringUtils.isEmpty(merchantVO.getEsId())){
            MerchantCluePool merchantCluePool = new MerchantCluePool(merchantVO);
            merchantCluePool.setMId(merchant.getmId());
            //处理数据 更新关联关系
            MerchantCluePool query = new MerchantCluePool();
            query.setMId(merchant.getmId());
            MerchantCluePool result = merchantCluePoolMapper.queryMerchantClue(query);
            if(result == null){
                updateCurlPool(merchantCluePool,MerchantCluePool.BANDING,null);
            }
        }

        //审核通过的门店为大客户的门店则需要记录到记录中
        if(Objects.deepEquals(merchant.getSize(),"大客户") && Objects.nonNull(merchant.getAdminId())){
            Integer operator = getAdminId();
            Long creator = operator == null ? null : operator.longValue();
            MerchantUpdateRecord recordInsert = new MerchantUpdateRecord();
            recordInsert.setMId(merchant.getmId());
            recordInsert.setChangeType(1);
            recordInsert.setNewAdmin(merchant.getAdminId().longValue());
            recordInsert.setCreatorId(creator);
            merchantUpdateRecordMapper.insertSelective(recordInsert);
        }
        //插入到月度金额表
        merchantMonthPurmoneyMapper.deleteByMid(merchant.getmId());
        MerchantMonthPurmoney merchantMonthPurmoney = new MerchantMonthPurmoney();
        merchantMonthPurmoney.setMonthPurmoney(merchantVO.getMonthPurmoney());
        merchantMonthPurmoney.setCreator(getAdminId());
        merchantMonthPurmoney.setCreateTime(LocalDateTime.now());
        merchantMonthPurmoney.setMId(merchant.getmId());
        merchantMonthPurmoneyMapper.insertSelective(merchantMonthPurmoney);
        //插入到待审核表
        //随机一个数组
        MerchantExt merchantExtIsExist = merchantExtMapper.selectByMid(merchant.getmId());
        if(Objects.isNull(merchantExtIsExist) && merchantVO.getState() == 0){
            String freeDay = null;
            Integer clickFlag = FreeDayLogEnmu.clickFlag.FALSE.ordinal();
            //取配置的免邮城市弹窗提示编号
            Config config = configMapper.selectOne(ConfigValueEnum.FREE_DAY_AREANO.getKey());
            String[] split = config.getValue().split(Global.SEPARATING_SYMBOL);
            List<String> freeDayList = new ArrayList<>(Arrays.asList(split));
            //如果在那个城市内, 随机数组，并且需要弹窗
            if(Objects.equals(freeDayList.get(0), net.summerfarm.common.util.StringUtils.ZONE) || freeDayList.contains(merchant.getCity())){
                freeDay = net.summerfarm.common.util.StringUtils.randomFreeDay();
            }else{ //如果不在取原先运营服务区域免邮日
                String deliveryRule = area.getDeliveryRule();
                DeliveryRuleVO deliveryRuleVO = JSONObject.parseObject(deliveryRule, DeliveryRuleVO.class);
                if(net.summerfarm.common.util.StringUtils.isNotBlank(deliveryRule)&& deliveryRule.contains(FREE_DELIVERY_WEEK) && !net.summerfarm.common.util.StringUtils.isBlank(deliveryRuleVO.getFreeDeliveryWeek())){
                    freeDay = deliveryRuleVO.getFreeDeliveryWeek();
                }
                clickFlag = FreeDayLogEnmu.clickFlag.TRUE.ordinal();
            }
            MerchantExt merchantExt = new MerchantExt();
            merchantExt.setUpdater(getAdminName());
            merchantExt.setCreator(getAdminName());
            merchantExt.setFreeDay(freeDay);
            merchantExt.setMId(merchant.getmId());
            merchantExt.setClickFlag(clickFlag);
            if(Objects.isNull(merchantVO.getGroupHeadFlag())){
                merchantExt.setGroupHeadFlag(GroupHeadFlagEnum.NORMAL.getFlag());
            }else{
                merchantExt.setGroupHeadFlag(merchantVO.getGroupHeadFlag());
                merchantExt.setGroupBuyAreaNo(merchantVO.getGroupBuyAreaNo());
            }

            merchantExtMapper.insertSelective(merchantExt);
        }


        // 插入审核记录afterReviewMerchant
        insertMerchantReviewRecord(merchant.getmId(), Byte.valueOf(String.valueOf(merchantVO.getState())), getAdminId(), getAdminName(), merchantVO.getRemark());
        logger.info("门店审核消息 manage发送 门店id {}",merchant.getmId());
        //审核通过后的消息
        asyncTaskService.sendMessage(merchant.getmId());

        //新增门店  进行数据推送
        if (Objects.equals(merchantVO.getState(),0)){
            //pushMerchantInfo(merchant.getmId());
        }
        return AjaxResult.getOK(merchant);
    }


    private void buildContact(Contact contact, Merchant merchant, MerchantVO merchantVO){
        contact.setContact(merchant.getMcontact());
        contact.setmId(merchant.getmId());
        contact.setPhone(merchant.getPhone());
        contact.setProvince(merchant.getProvince());
        contact.setCity(merchant.getCity());
        contact.setArea(merchant.getArea());
        contact.setHouseNumber(merchantVO.getHouseNumber());
        if (StringUtils.isNotBlank(merchantVO.getAddress())) { //修改注册地址
            contact.setAddress(merchantVO.getAddress());
        } else {
            contact.setAddress(merchant.getAddress());
        }
        contact.setStatus(1);
        contact.setIsDefault(1);
        contact.setPoiNote(merchantVO.getPoiNote());
        //获取poi对应的配送仓编号
        String poiNote = merchantVO.getPoiNote();
        Integer storeNo = this.getStoreNo(isPopMerchantV2(merchant.getBusinessLine()),contact);
        if(Objects.isNull(storeNo)){
            throw new DefaultServiceException("该地址不在运营服务范围内");
        }
        // 获取联系地址与仓库的距离
        BigDecimal distance = BigDecimal.ZERO;
        distance = this.getDistance(merchantVO.getPoiNote(),storeNo,distance);

        contact.setDistance(distance);
        contact.setStoreNo(storeNo);
    }

    /**
     * 外部门店映射
     * @param merchant
     * @param record
     * @param outerMappings
     */
    public void merchantOuterMapping(Merchant merchant,MerchantVO record,List<MerchantOuterVO> outerMappings){
        // 根据mId查询变更前外部映射平台
        List<Long> mIds = new ArrayList<>();
        mIds.add(merchant.getmId());
        List<MerchantOuterDO>  merchantOuterList= merchantOuterMapper.selectByMIdList(mIds);
        if (CollectionUtils.isEmpty(merchantOuterList) && CollectionUtils.isEmpty(outerMappings)) {
            return;
        }

        List<MerchantOuterDO> delMerchantOuterList=null;
        if (!CollectionUtils.isEmpty(merchantOuterList) && CollectionUtils.isEmpty(outerMappings)) {
            delMerchantOuterList=merchantOuterList;
        }else{
            // 变更前外部映射平台与现有外部平台比较，没有的进行删除操作
            delMerchantOuterList = merchantOuterList.stream()
                    .filter(all -> outerMappings.stream()
                            .noneMatch(add -> Objects.equals(add.getOuterPlatformId(), all.getOuterPlatformId()))).
                            collect(Collectors.toList());
        }

        if(!CollectionUtils.isEmpty(delMerchantOuterList)){
            merchantOuterMapper.deleteByIdBatch(delMerchantOuterList);
            delMerchantOuterList.forEach(del ->{
                // 外部对接，向外部平台推送删除门店
                orderOuterInfoService.mqPushStore("storedel",del.getOuterNo(),del.getOuterPlatformId(),merchant.getAreaNo());
            });
        }

        for(MerchantOuterVO merchantOuterVO:outerMappings){
            MerchantOuterDO merchantOuterDO = merchantOuterMapper.selectByOuterNo(merchantOuterVO.getOuterNo(), merchantOuterVO.getOuterPlatformId());
            if (merchantOuterDO != null && !merchantOuterDO.getmId().equals(merchant.getmId())) {
                Merchant merchantVo = merchantMapper.selectByPrimaryKey(merchantOuterDO.getmId());
                throw new DefaultServiceException("该"+merchantOuterVO.getOuterPlatformName()+"门店编号已与其他门店“" + merchantVo.getMname() + "”关联");
            }
            if (merchantOuterDO == null) {
                MerchantOuterDO queryMerchantOuterDO = merchantOuterMapper.selectByMid(merchant.getmId(),merchantOuterVO.getOuterPlatformId());
                if (queryMerchantOuterDO == null) {
                    MerchantOuterDO insertContent = new MerchantOuterDO();
                    insertContent.setmId(merchant.getmId());
                    insertContent.setOuterPlatformId(merchantOuterVO.getOuterPlatformId());
                    insertContent.setOuterNo(merchantOuterVO.getOuterNo());
                    insertContent.setRemark("外部映射添加门店编号");
                    merchantOuterMapper.insert(insertContent);
                    // 外部对接，向外部平台推送门店关联城市
                    orderOuterInfoService.mqPushStore("storeadd",merchantOuterVO.getOuterNo(),merchantOuterVO.getOuterPlatformId(),record.getAreaNo());
                } else {
                    MerchantOuterDO updateContent = new MerchantOuterDO();
                    updateContent.setId(queryMerchantOuterDO.getId());
                    updateContent.setOuterPlatformId(merchantOuterVO.getOuterPlatformId());
                    updateContent.setOuterNo(merchantOuterVO.getOuterNo());
                    merchantOuterMapper.update(updateContent);
                    if (!queryMerchantOuterDO.getOuterNo().equals(merchantOuterVO.getOuterNo()) ||
                            !queryMerchantOuterDO.getOuterPlatformId().equals(merchantOuterVO.getOuterPlatformId()) ||
                            !merchant.getAreaNo().equals(record.getAreaNo())) {
                        // 外部对接，向外部平台推送删除门店
                        orderOuterInfoService.mqPushStore("storedel",queryMerchantOuterDO.getOuterNo(),queryMerchantOuterDO.getOuterPlatformId(),merchant.getAreaNo());
                        // 外部对接，向外部平台推送门店关联城市
                        orderOuterInfoService.mqPushStore("storeadd", merchantOuterVO.getOuterNo(), merchantOuterVO.getOuterPlatformId(), record.getAreaNo());
                    }
                }
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult rebackReview(int id) {

        Merchant merchant = merchantMapper.selectByPrimaryKey(Long.valueOf(id));
        if (merchant == null || merchant.getIslock() != 2) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        merchant.setAuditUser(getAdminId());
        merchant.setAdminId(dynamicConfig.getNoAuditMerchantDefaultAdminId());
        merchantMapper.rebackReview(merchant);

        // 店长账号处理
        MerchantSubAccount account = merchantSubAccountMapper.selectManageByMid(merchant.getmId());
        if(account == null) {
            // 兼容老的数据
            account = merchantSubAccountMapper.selectManageByMidDeleted(merchant.getmId());
        }
        account.setAuditTime(new Date());
        account.setAuditUser(getAdminId());
        account.setDeleteFlag(1);
        merchantSubAccountMapper.updateSelective(account);

        insertMerchantReviewRecord(merchant.getmId(), new Byte("1"), getAdminId(), getAdminName(), null);

        return AjaxResult.getOK();
    }

    /**
     * 写入商家审核记录
     * @param mId
     * @param isLock
     * @param auditUser
     * @param remark
     * @return
     */
    public int insertMerchantReviewRecord(Long mId, Byte isLock, Integer auditUser, String auditName, String remark){
        MerchantReviewRecord record = new MerchantReviewRecord(mId, isLock, auditUser, auditName, remark);
        return merchantReviewRecordMapper.insert(record);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult save(Merchant record, BindingResult bindingResult) {
        if (null != bindingResult && bindingResult.hasErrors()) {
            return AjaxResult.getError(String.valueOf(bindingResult.getFieldError()));
        }
        ProvinceConveter.convert(record, "save");
        int rs = merchantMapper.insertSelective(record);
        if (rs == 1) {
            return AjaxResult.getOK();
        }

        return AjaxResult.getError(DEFAULT_FAILED);
    }


    /**
     * 验证新增字段的有效性
     *
     * @param record 商户VO对象
     * @return 验证结果
     */
    private AjaxResult validateNewFields(MerchantVO record) {
        // 验证门店主业类型
        if (StringUtils.isNotBlank(record.getMainBusinessType())) {
            if (record.getMainBusinessType().length() > 100) {
                return AjaxResult.getErrorWithMsg("门店主业类型长度不能超过100个字符");
            }
        }

        // 验证门店副业类型
        if (StringUtils.isNotBlank(record.getSideBusinessType())) {
            if (record.getSideBusinessType().length() > 512) {
                return AjaxResult.getErrorWithMsg("门店副业类型长度不能超过512个字符");
            }
            // 验证副业类型格式（多个用英文逗号分隔）
            String[] sideBusinessTypes = record.getSideBusinessType().split(",");
            for (String type : sideBusinessTypes) {
                if (StringUtils.isBlank(type.trim())) {
                    return AjaxResult.getErrorWithMsg("门店副业类型不能包含空值，请检查逗号分隔格式");
                }
            }
        }

        // 验证连锁范围
        if (record.getMerchantChainType() != null) {
            if (!MerchantChainTypeEnum.isValidCode(record.getMerchantChainType())) {
                return AjaxResult.getErrorWithMsg("连锁范围值无效，有效值为：0(NKA-全国连锁)、1(LKA-区域连锁)、2(其他连锁)、3(跨区域连锁)、4(工厂)、99(单店)");
            }
        }

        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult update(Long id, MerchantVO record) {
        //判断id对应用户是否存在
        Merchant merchant = merchantMapper.selectByPrimaryKey(id);
        if (merchant == null) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        if ("大客户".equals(record.getSize()) && (record.getAdminId() == null || record.getDirect() == null)) {
            return AjaxResult.getErrorWithMsg("所属总部或者客户属性不能为空");
        }
        if (record.getMname() == null || "".equals(record.getMname())) {
            return AjaxResult.getErrorWithMsg("商铺名不能为空");
        } else if (!record.getMname().equals(merchant.getMname())) {
            Merchant queryMerchant = merchantMapper.selectIsUserName(record.getMname(), null);
            if (queryMerchant != null) {
                return AjaxResult.getErrorWithMsg("该商铺名已存在");
            }
        }
        if (record.getMcontact() == null || "".equals(record.getMcontact())) {
            return AjaxResult.getErrorWithMsg("联系人不能为空");
        }

        // 验证新增字段
        AjaxResult validationResult = validateNewFields(record);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        Area area = areaMapper.selectByAreaNo(record.getAreaNo());
        if(!Objects.equals(merchant.getBusinessLine(), area.getBusinessLine())) {
            return AjaxResult.getErrorWithMsg("门店和与运营区域的业务线不一致！");
        }

        if(!isPopMerchantV2(merchant.getBusinessLine()) && Objects.equals(dynamicConfig.getPopDefaultAdminId(), record.getAdminId())) {
            return AjaxResult.getErrorWithMsg("鲜沐门店不允许切换为pop大客户");
        }

        
        //大客户默认为普通
        if(Objects.equals(Global.BIG_MERCHANT,record.getSize()) && StringUtils.isEmpty(record.getMerchantType())){
            record.setMerchantType("普通");
        }
        // 查询关联线索池信息
        MerchantCluePool queryCluePool = new MerchantCluePool();
        queryCluePool.setMId(id);
        MerchantCluePool resultPool = merchantCluePoolMapper.queryMerchantClue(queryCluePool);
        //修改线索池校验
        if(StringUtils.isEmpty(record.getEsId())){
            if(resultPool == null || !Objects.equals(resultPool.getEsId(),record.getEsId())){
                int amount = merchantCluePoolMapper.queryEsIdNumber(record.getEsId());
                if(amount > 0){
                    return AjaxResult.getErrorWithMsg("该线索已被其他客户绑定");
                }
            }
        }
        Pattern phonePattern = ManagerConstant.PHONE_PATTERN;
        if (!phonePattern.matcher(record.getPhone()).find()) {
            return AjaxResult.getErrorWithMsg("手机号格式不正确");
        }
        record.setGrade(merchant.getGrade());
        //标识单店切大客户
        boolean ddToDkh = false;

        //原先大客户变成普通客户 adminId 和 direct 变成空 初始化grade字段
        if ("大客户".equals(merchant.getSize()) && !"大客户".equals(record.getSize())) {
            record.setAdminId(null);
            record.setDirect(null);
            record.setSkuShow(null);
            record.setGrade(memberService.getMemberGrade(id, record.getAreaNo()));
            // 账期大客户转单店立即出账
            if (ObjectUtil.equal(merchant.getDirect(), NumberUtils.INTEGER_ONE)) {
                MQData mqData = new MQData();
                mqData.setType(MType.REISSUE_BRAND_BILL.name());
                JSONObject msgJson = new JSONObject();
                msgJson.put("adminId", merchant.getAdminId());
                msgJson.put("mIds", Arrays.asList(merchant.getmId()));
                String producerMsg = msgJson.toJSONString();
                mqData.setData(producerMsg);
                mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
            }
        }
        //普通客户变成大客户 grade字段置空
        if (!"大客户".equals(merchant.getSize()) && "大客户".equals(record.getSize())) {
            //切仓拦截
            if (areaService.inChange(record.getAreaNo(), record.getAdminId(), null)) {
                return AjaxResult.getError("切仓中，该功能暂时无法使用");
            }

            record.setGrade(null);
            ddToDkh = true;
            // 将门店默认抬头带入大客户抬头
            List<InvoiceConfig> invoiceConfigs =
                    invoiceConfigMapper.selectByMerchantId(id).stream().filter(i -> ObjectUtil.equal(i.getDefaultFlag(), DefaultInvoiceConfigEnum.DEFAULT_CONFIG.getCode())).collect(Collectors.toList());
            if (!invoiceConfigs.isEmpty()) {
                InvoiceConfig invoiceConfig = invoiceConfigs.get(0);
                // 查询大客户抬头是否存在
                List<InvoiceConfig> adminInvoiceList = invoiceConfigMapper.selectByType(record.getAdminId(), null, invoiceConfig.getInvoiceTitle(), invoiceConfig.getTaxNumber());
                if (adminInvoiceList.isEmpty()) {
                    invoiceConfig.setId(null);
                    invoiceConfig.setType(1);
                    invoiceConfig.setDefaultFlag(DefaultInvoiceConfigEnum.NOT_DEFAULT.getCode());
                    invoiceConfig.setAdminId(record.getAdminId());
                    invoiceConfigMapper.insert(invoiceConfig);
                } else {
                    invoiceConfig = adminInvoiceList.get(0);
                }
                invoiceMerchantRelationMapper.deleteMerchantId(id);
                invoiceMerchantRelationMapper.insertNew(invoiceConfig.getId(), id);
            }
        }

        String msg = "";
        if (!record.getAreaNo().equals(merchant.getAreaNo())) {
            Orders orders = ordersMapper.selectLast(id);

            if (orders != null) {
                return AjaxResult.getErrorWithMsg("订单状态和售后完结才可切换城市");
            }
            int num = afterSaleOrderMapper.countNotClose(id);
            if (num > 0) {
                return AjaxResult.getErrorWithMsg("订单状态和售后完结才可切换城市");
            }

            // 切换区域取消购物车商品清空，将商品移到失效栏中
            // 清空购物车
            // trolleyMapper.cancelCheckByMid(id);
            msg = "切换区域后注册地址依旧有效,下单会造成商品送不到的情况,请勿在切换区域后下单";
        }

        if ("大客户".equals(record.getSize()) && !Objects.equals(record.getDirect(), merchant.getDirect())) {
            Orders orders = ordersMapper.selectLast(id);
            if (orders != null) {
                return AjaxResult.getErrorWithMsg("订单状态和售后完结才可切换城市");
            }
            int num = afterSaleOrderMapper.countNotClose(id);
            if (num > 0) {
                throw new DefaultServiceException("订单状态和售后完结才可切换");
            }
        }
        //外部对接 外部门店编码不为空 进行门店关联
        merchantOuterMapping(merchant,record,record.getOuterMappings());

        record.setmId(id);
        //只能修改地址和备注\所属配送车辆\服务城市\配置客户
        merchantMapper.updateInfo(record);


        // 更新门店联系人
        this.updateMerchantManager(id, record.getMcontact());

        //失效优惠卡
        if (ddToDkh) {
            int r = discountCardToMerchantMapper.invalidDiscountCard(merchant.getmId());
            if (r > 0) {
                logger.info("单店切换到大客户，商品优惠卡失效");
            }
        }

        //有关联改为暂无
        if(resultPool != null && StringUtils.isEmpty(record.getEsId())){
            //移除线索池关联关系
            updateCurlPool(null,null,resultPool.getEsId());
            //新增关联关系
        } else if(resultPool == null && !StringUtils.isEmpty(record.getEsId())){
            MerchantCluePool merchantCluePool = new MerchantCluePool(record);
            merchantCluePool.setMId(id);
            updateCurlPool(merchantCluePool,MerchantCluePool.BANDING,null);
            //更新线索池关联关系
        } else if (resultPool != null && !Objects.equals(resultPool.getEsId(),record.getEsId())){
            MerchantCluePool merchantCluePool = new MerchantCluePool(record);
            merchantCluePool.setMlId(resultPool.getMlId());
            updateCurlPool(merchantCluePool,MerchantCluePool.BANDING,resultPool.getEsId());

        }

        // 非单店转大客户，存在转化关系的( 大客户 -> 其它大客户； 大客户 -> 单店），则将对应的抬头联接关系解除
        if (merchant.getAdminId() != null && !Objects.deepEquals(record.getAdminId(), merchant.getAdminId())) {
            InvoiceMerchantRelationVO query = new InvoiceMerchantRelationVO();
            query.setMerchantId(merchant.getmId());
            List<InvoiceMerchantRelationVO> list = invoiceMerchantRelationMapper.selectBySelectKeys(query);
            if (!CollectionUtils.isEmpty(list)) {
                for (InvoiceMerchantRelationVO relationItem : list) {
                    invoiceMerchantRelationMapper.deleteByPrimaryKey(relationItem.getId());
                }
            }
        }

        /**
         * 记录的前提是该门店为正常使用的
         * 存在门店从普通门店切换到大客户门店，大客户门店切换到普通门店,
         * 门店从一个大客户切换到另一个大客户时记录下切换前后必要信息
         */
        //门店 --> 大客户 ， 大客户 --> 门店， 大客户 --> 另一个大客户 的变动记录
        if( merchant.getIslock() == 0 &&
                ( !Objects.deepEquals(record.getSize(), merchant.getSize()) || !Objects.deepEquals(record.getAdminId(),merchant.getAdminId()))){
            MerchantUpdateRecord insertRecord = new MerchantUpdateRecord();
            insertRecord.setCreatorId(getAdminId().longValue());
            insertRecord.setChangeType(0);
            insertRecord.setMId(merchant.getmId());
            if(Objects.deepEquals(record.getSize(),"大客户") && Objects.nonNull(record.getAdminId())){
                insertRecord.setNewAdmin(record.getAdminId().longValue());
            }
            if(Objects.nonNull(merchant.getAdminId())){
                insertRecord.setOldAdmin(merchant.getAdminId().longValue());
            }
            merchantUpdateRecordMapper.insertSelective(insertRecord);
            logger.info("门店{}所属类型有变动：原来的类型:{},对应id:{} -> 修改后的类型:{},对应的id:{}",
                    merchant.getMname(),merchant.getSize(),merchant.getAdminId(), record.getSize(),record.getAdminId());
        }
        //插入到月度金额表
        merchantMonthPurmoneyMapper.deleteByMid(merchant.getmId());
        MerchantMonthPurmoney merchantMonthPurmoney = new MerchantMonthPurmoney();
        merchantMonthPurmoney.setMonthPurmoney(record.getMonthPurmoney());
        merchantMonthPurmoney.setCreator(getAdminId());
        merchantMonthPurmoney.setCreateTime(LocalDateTime.now());
        merchantMonthPurmoney.setMId(merchant.getmId());
        merchantMonthPurmoneyMapper.insertSelective(merchantMonthPurmoney);
        logger.info("管理员：{}修改了商户:{}({})信息", this.getAdminName(), id, merchant.getMname());
       //tms配送单配置
        updateTmsConfig(merchant.getmId(), record.getPrintOutTMSConfig());

        // 更新是否是团长
        MerchantExt merchantExt = merchantExtMapper.selectByMid(merchant.getmId());
        String adminName = getAdminName();
        LocalDateTime now = LocalDateTime.now();
        if(merchantExt == null){
            merchantExt = new MerchantExt();
            merchantExt.setMId(merchant.getmId());
            merchantExt.setCreator(adminName);
            merchantExt.setCreateTime(now);
            merchantExt.setUpdater(adminName);
            merchantExt.setUpdateTime(now);
            merchantExt.setGroupHeadFlag(record.getGroupHeadFlag());
            merchantExt.setGroupBuyAreaNo(record.getGroupBuyAreaNo());
            merchantExtMapper.insertSelective(merchantExt);
        }else{
            merchantExt.setId(merchantExt.getId());
            merchantExt.setGroupHeadFlag(record.getGroupHeadFlag());
            merchantExt.setGroupBuyAreaNo(record.getGroupBuyAreaNo());
            merchantExt.setMId(record.getmId());
            merchantExt.setUpdater(adminName);
            merchantExt.setUpdateTime(now);
            merchantExtMapper.updateByPrimaryKeySelective(merchantExt);
        }

        if (StringUtils.isNotBlank(msg)) {
            return AjaxResult.getOK(msg);
        }
        return AjaxResult.getOK("修改成功");
    }

    public void  updateTmsConfig(Long mid, Boolean printOutTMSConfig){
        if (printOutTMSConfig == null){
            return;
        }
        merchantStoreExtCommandFacade.addMerchantStoreExt(mid,MerchantPropertiesExtEnum.TMS_PRINT_OUT_CONFIG.name(),printOutTMSConfig?String.valueOf(MerchantPropertiesExtEnum.TMS_PRINT_OUT_CONFIG.getId()):RELEASE_TYPE.toString());
    }
    /**
     * 更新店长信息
     */
    private void updateMerchantManager(Long mid, String mcontact){
        MerchantSubAccount subAccount = merchantSubAccountMapper.selectManageByMid(mid);
        if(null == subAccount) {
            logger.warn("当前门店mid：{}没有店长信息", mid);
            return;
        }
        MerchantSubAccount updateAccount = new MerchantSubAccount();
        updateAccount.setAccountId(subAccount.getAccountId());
        updateAccount.setContact(mcontact);
        merchantSubAccountMapper.updateSelective(updateAccount);
    }

    /**
     * 发送钉钉通知
     * @param merchant 商户
     * @param dingTalkMessage 消息信息
     */
    private void contactFrequentDingTalkSend(Merchant merchant, StringJoiner dingTalkMessage) {
        try {
            StringBuilder textSb = new StringBuilder();
            textSb.append("配送周期差异提醒").append("\n\n");
            textSb.append("门店名称:").append(merchant.getMname()).append("\n\n");
            textSb.append("门店编号:").append(merchant.getmId()).append("\n\n");
            textSb.append(dingTalkMessage);

            HashMap<String, String> msgMap = new HashMap<>();
            msgMap.put("text",textSb.toString());
            msgMap.put("title","配送周期差异提醒");

            //机器人url
            Config config = configMapper.selectOne("change_fence_frequent_contact_notice");
            String url = config.getValue();
            DingTalkRobotUtil.sendMsgAndAtAll("markdown", url, () -> msgMap);
        } catch (Exception e) {
            logger.error("发送钉钉消息失败",e);
        }
    }

    protected StringJoiner changeContact(List<Contact> contacts, Merchant merchant) {
        //发送消息通知
        StringJoiner sendMessageInfo = new StringJoiner("\n\n");
        if (!CollectionUtils.isEmpty(contacts)) {
            boolean isPopMerchant = isPopMerchantV2(merchant.getBusinessLine());
            for (Contact contact : contacts) {
                if(!Objects.equals(contact.getModify(), true)){
                    continue;
                }
                if (contact.getContactId() == null) {
                    throw new DefaultServiceException("修改地址不存在");
                }
                if (StringUtils.isEmpty(contact.getPoiNote())) {
                    throw new DefaultServiceException("请先选择poi地址");
                }
                Contact contactRecord = contactMapper.selectByPrimaryKey(contact.getContactId());
                //
                if(Objects.isNull(contact.getmId())){
                    contact.setmId(contactRecord.getmId());
                }
                if(Objects.isNull(contact.getPosition())){
                    contact.setPosition(contactRecord.getPosition());
                }
                if(Objects.isNull(contact.getDistance())){
                    contact.setDistance(contactRecord.getDistance());
                }
                if(Objects.isNull(contact.getIsDefault())){
                    contact.setIsDefault(contactRecord.getIsDefault());
                }
                //获取地址对应 使用页面提供的城配仓编号
                //contact.setStoreNo(contactRecord.getStoreNo());
                if (!contact.getPoiNote().equals(contactRecord.getPoiNote())) { //修改了收货地址
                    // 围栏
                    Integer storeNo = this.getStoreNo(isPopMerchant, contact);
                    contact.setStoreNo(storeNo);
                    // 获取联系地址与仓库的距离
                    BigDecimal distance = BigDecimal.ZERO;
                    distance = this.getDistance(contact.getPoiNote(), contactRecord.getStoreNo(), distance);
                    contact.setDistance(distance);
                }
                //地址部分修改都是删除然后新增
                Long typeId = contact.getContactId();
                Contact old = new Contact();
                old.setContactId(contact.getContactId());
                old.setStatus(2);
                contactMapper.updateByPrimaryKeySelective(old);

                //如果和用户企业信息有关联，则修改新的关联
                MchEnterpriseAddressRelation mchEnterpriseAddressRelation = mchEnterpriseAddressRelationMapper.selectById(contact.getContactId());

                if (Objects.equals(contact.getStatus(), 2)) {
                    //删除地址之后，判断是否商城中有和企业信息关联，如有则删除关联
                    MchEnterpriseAddressRelation mch = mchEnterpriseAddressRelationMapper.selectById(contact.getContactId());
                    if (!ObjectUtils.isEmpty(mch)) {
                        //如果存在关联关系则使其失效
                        mchEnterpriseAddressRelationMapper.updateByKey(mch.getEnterpriseInformationId());
                    }
                }

                contact.setContactId(null);
                contact.setmId(merchant.getmId());
                contact.setAddressRemark(contactRecord.getAddressRemark());
                contactMapper.insertSelective(contact);

                if (!ObjectUtils.isEmpty(mchEnterpriseAddressRelation) && !Objects.equals(contact.getStatus(), 2)) {
                    //如果存在关联关系则更新
                    MchEnterpriseAddressRelation m = new MchEnterpriseAddressRelation();
                    m.setId(mchEnterpriseAddressRelation.getId());
                    m.setContactId(contact.getContactId());
                    mchEnterpriseAddressRelationMapper.updateByPrimaryKeySelective(m);
                }
                ContactOperateLog contactOperateLog = new ContactOperateLog();
                contactOperateLog.setMId(contact.getmId());
                contactOperateLog.setContactId(contact.getContactId());
                contactOperateLog.setCreateTime(LocalDateTime.now());
                contactOperateLog.setUpdateTime(LocalDateTime.now());
                contactOperateLog.setContext("修改联系人信息");
                contactOperateLog.setOperateType(ContactLogEnum.OperateType.APPROVE.ordinal());
                contactOperateLog.setOperateName(getAdminName());
                contactOperateLog.setOperateSource(ContactLogEnum.OperateSource.MANAGE.ordinal());
                contactOperateLogMapper.insertSelective(contactOperateLog);

                // 运费规则支持阶梯价后，运费规则不再从入参的insertInput里取，而是调用marketing-center接口直接
                // 从老的contactId转移到新的contactId   modify@2024.09.19
                distributionRulesFacade.transferDeliveryFeeRule(DistributionRulesTypeEnum.MERCHANT.getCode(), String.valueOf(typeId), String.valueOf(contact.getContactId()));
                // 新版运费规则发布上线过程中，仍然需要修改老版运费规则（用于发布失败回滚），发布上线后就无需再修改老版运费规则，这里通过Nacos配置来控制
                if (Boolean.TRUE.equals(dynamicConfig.getChangeContactModifyDistributionRules())) {
                    logger.info("修改老版运费规则，原contactId:{}，新contactId:{}", typeId, contact.getContactId());
                    //保存运费规则
                    DistributionRulesInsertInput insertInput = contact.getInsertInput();
                    if (Objects.nonNull(insertInput)) {
                        insertInput.setTypeId(contact.getContactId());
                        distributionRulesFacade.insert(insertInput);
                    }

                    //删除运费规则
                    DistributionRulesDeleteReq deleteReq = new DistributionRulesDeleteReq();
                    deleteReq.setTypeId(typeId);
                    deleteReq.setType(DistributionRulesTypeEnum.MERCHANT.getCode());
                    distributionRulesFacade.delete(deleteReq);
                }


                //保存配送周期 假如不修改配送周期则 先查出配送周期再新增（不发飞书消息提醒）
                // pop商城暂时不允许修改配送周期
                if(isPopMerchant) {
                    // 后置处理调用wnc保存城配仓和配送周期
                    //先根据仓网接口返回的POP城配仓属性判断是T+1还是T+2，如果是T+2则不需要调用
                    Integer popFulfillmentway = deliveryFenceQueryFacade.getPopFulfillmentway(contact.getCity(), contact.getArea(), contact.getPoiNote(), merchant.getmId());
                    if (Objects.equals(popFulfillmentway, CommonStatus.NO.getCode())) {
                        contactDeliveryRuleFacade.popDeliveryRuleConfigSaveOrUpdate(contact.getContactId(), contact.getStoreNo());
                    }
                } else {
                    ContactDeliveryDTO contactDeliveryDTO = deliveryRuleQueryFacade.getInfo(typeId);
                    if (Objects.nonNull(contact.getFrequentMethod()) && ClearDeliveryEnum.NO.getCode()
                            .equals(contact.getClearDelivery())) {
                        ContactDeliverySaveOrUpdateInput input = new ContactDeliverySaveOrUpdateInput();
                        input.setContactId(contact.getContactId());
                        input.setBeginCalculateDate(contact.getBeginCalculateDate());
                        input.setDeliveryFrequentInterval(contact.getDeliveryFrequentInterval());
                        input.setFrequentMethod(contact.getFrequentMethod());
                        input.setWeekDeliveryFrequent(contact.getWeekDeliveryFrequent());
                        deliveryRuleQueryFacade.saveOrUpdate(input);

                        //封装飞书信息
                        sendMessageInfo = new StringJoiner("\n\n");
                        createFeiShuMessage(sendMessageInfo, contactDeliveryDTO, contact);
                    }
                    //删除老地址的配送周期
                    deliveryRuleQueryFacade.delete(typeId);
                }

                // 指定城配仓处理逻辑
                contactService.appointStoreNoHandle(isPopMerchant, contact,old.getContactId(), contact.getStoreNo());
            }
        }
        return sendMessageInfo;
    }

    @Override
    public boolean isPopMerchantV2(Integer businessLine) {
        return MerchantEnum.BusinessLineEnum.POP.getCode().equals(businessLine);
    }

    private Integer getStoreNo(boolean isPopMerchant, Contact contact){
        Integer storeNo = null;
        if(isPopMerchant) {
            logger.info("pop商城获取城配仓信息, contact:{}", JSON.toJSONString(contact));
            storeNo = deliveryFenceQueryFacade.getPopStoreNo(contact.getCity(), contact.getArea(), contact.getPoiNote(), contact.getmId());
        } else {
            logger.info("鲜沐商城获取城配仓信息, contact:{}", JSON.toJSONString(contact));
            storeNo = fenceService.getStoreNo(contact.getPoiNote(), contact);
        }
        if(Objects.isNull(storeNo)){
            throw new DefaultServiceException("该地址不在运营服务范围内");
        }
        return storeNo;
    }

    private void createFeiShuMessage(StringJoiner sendMessageInfo, ContactDeliveryDTO contactDeliveryDTO, Contact contact) {
        try {
            if (contactDeliveryDTO != null) {
                //如果和原来的一样，则不发送消息
                if (Objects.equals(contactDeliveryDTO.getBeginCalculateDate(), contact.getBeginCalculateDate())
                        && Objects.equals(contactDeliveryDTO.getDeliveryFrequentInterval(), contact.getDeliveryFrequentInterval())
                        && Objects.equals(contactDeliveryDTO.getFrequentMethod(), contact.getFrequentMethod())
                        && Objects.equals(contactDeliveryDTO.getWeekDeliveryFrequent(), contact.getWeekDeliveryFrequent())) {
                    return;
                }
            }

            //根据城市和区域取围栏
            FenceVO fenceVO = fenceService.selectFenceByCityArea(contact.getArea(),contact.getCity());
            if (Objects.isNull(fenceVO)) {
                throw new DefaultServiceException("地址归属不在有效围栏内");
            }
            sendMessageInfo.add("联系方式:"+ contact.getPhone());
            sendMessageInfo.add("送货地址:"+ contact.getProvince()
                    + contact.getCity()
                    +(StringUtils.isNotBlank(contact.getArea())? contact.getArea():"")
                    + contact.getAddress()
                    + (StringUtils.isNotBlank(contact.getHouseNumber())? contact.getHouseNumber():""));
            if(fenceVO.getStoreNo() != null){
                WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsService.selectByStoreNo(fenceVO.getStoreNo());
                sendMessageInfo.add("城配仓:"+ (warehouseLogisticsCenter != null ? warehouseLogisticsCenter.getStoreName() : ""));
            }
            if(fenceVO.getFrequentMethod() == null || fenceVO.getFrequentMethod() == 1){
                sendMessageInfo.add("围栏配送周期:"+ (("0".equals(fenceVO.getDeliveryFrequent()) ? "每天" : "周"+fenceVO.getDeliveryFrequent())));
            }else{
                sendMessageInfo.add("围栏配送周期:每"+ fenceVO.getDeliveryFrequentInterval()+"天(开始计算日期"+ fenceVO.getBeginCalculateDate()+")");
            }
            if(Objects.equals(contact.getFrequentMethod(), 1)){
                if (Objects.equals(contact.getWeekDeliveryFrequent(),"0")) {
                    sendMessageInfo.add("当前地址配送周期:每天");
                } else {
                    List<String> days = Arrays.asList(contact.getWeekDeliveryFrequent().split(Global.SEPARATING_SYMBOL));
                    StringJoiner stringJoiner = new StringJoiner(Global.SEPARATING_SYMBOL);

                    days.forEach(day -> stringJoiner.add(WeekTimeEnum.weekTimeMap.get(day)));
                    sendMessageInfo.add("当前地址配送周期:"+ stringJoiner);
                }
            }else {
                sendMessageInfo.add("当前地址配送周期:每"+ contact.getDeliveryFrequentInterval()+"天(开始计算日期"+ contact.getBeginCalculateDate()+")");
            }
        } catch (Exception e) {
            logger.error("生成飞书消息通知失败",e);
        }
    }

    private void createDingTalkFrequentMssage(StringJoiner sendMessageInfo, Contact contact, FenceVO fenceVO) {
        try {
            sendMessageInfo.add("联系方式:"+ contact.getPhone());
            sendMessageInfo.add("送货地址:"+ contact.getProvince()
                    + contact.getCity()
                    +(StringUtils.isNotBlank(contact.getArea())? contact.getArea():"")
                    + contact.getAddress()
                    + (StringUtils.isNotBlank(contact.getHouseNumber())? contact.getHouseNumber():""));
            if(fenceVO.getStoreNo() != null){
                WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsService.selectByStoreNo(fenceVO.getStoreNo());
                sendMessageInfo.add("城配仓:"+ (warehouseLogisticsCenter != null ? warehouseLogisticsCenter.getStoreName() : ""));
            }
            if(fenceVO.getFrequentMethod() == null || fenceVO.getFrequentMethod() == 1){
                sendMessageInfo.add("围栏配送周期:"+ (("0".equals(fenceVO.getDeliveryFrequent()) ? "每天" : "周"+fenceVO.getDeliveryFrequent())));
            }else{
                sendMessageInfo.add("围栏配送周期:每"+ fenceVO.getDeliveryFrequentInterval()+"天(开始计算日期"+ fenceVO.getBeginCalculateDate()+")");
            }

            if(Objects.equals(contact.getDeliveryFrequent(),"0")){
                sendMessageInfo.add("当前地址配送周期:每天");
            }else{
                List<String> days = Arrays.asList(contact.getDeliveryFrequent().split(Global.SEPARATING_SYMBOL));
                StringJoiner stringJoiner = new StringJoiner(Global.SEPARATING_SYMBOL);

                days.forEach(day ->{
                    stringJoiner.add(WeekTimeEnum.weekTimeMap.get(day));
                });
                sendMessageInfo.add("当前地址配送周期:"+ stringJoiner);
            }
        } catch (Exception e) {
            logger.error("生成钉钉消息通知失败",e);
        }
    }

    public void pushMerchantInfo(Long mId){
        if (checkCBD(mId)){
            logger.info("门店{}符合条件，正在发送到消息队列中",mId);
            MQData mqData=new MQData();
            mqData.setType(MType.MERCHANT_MSG.name());
            mqData.setData(mId);
            String params = JSONObject.toJSONString(mqData);
            logger.info("准备发送新mq消息,content:{}",params);
            mqProducer.send("teahundredways-list",null,params);
        }
    }
    /**
     * 排序配送周期字段
     * @param contact 联系人
     */
    private void sortDeliveryFrequent(Contact contact) {
        String deliveryFrequent = contact.getDeliveryFrequent();
        String[] split = deliveryFrequent.split(Global.SEPARATING_SYMBOL);
        ArrayList<String> deliveryFrequentList = new ArrayList<>(Arrays.asList(split));
        List<String> sortDeliveryFrequentList = deliveryFrequentList.stream().sorted().collect(Collectors.toList());
        String join = org.apache.commons.lang3.StringUtils.join(sortDeliveryFrequentList, Global.SEPARATING_SYMBOL);
        contact.setDeliveryFrequent(join);
    }

    private Boolean inAfterSaleTime(String orderNo, int afterSaleTime) {
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orderNo);
        DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
        LocalDateTime start = deliveryPlanVO.getDeliveryTime().atTime(0, 0);
        LocalDateTime now = LocalDateTime.now();
        return now.isAfter(start.plusHours(afterSaleTime));
    }


    @Override
    public AjaxResult selectMerchantList(int pageIndex, int pageSize, MerchantVO record) {
        Integer adminId = getAdminId();
        record.setAdminId(adminId);
        PageHelper.startPage(pageIndex, pageSize);
        List<MerchantVO> result = merchantMapper.selectMerchantListBD(record);
        LocalDateTime startTime = LocalDateTime.of(LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), 1), LocalTime.MIN);
        //在流转白名单中的用户
        if (!CollectionUtils.isEmpty(result)) {
            for (MerchantVO merchantVO : result) {
                Contact select = new Contact();
                select.setmId(merchantVO.getmId());
                select.setStatus(1);
                List<Contact> contacts = contactMapper.select(select);
                merchantVO.setContacts(contacts);
                // 计算拜访天数和未下单天数
                merchantVO.setReassign(record.getReassign());
                followUpRelationService.notOrderOrFollow(merchantVO);
                // 本月是否下单
                int monthOrderCount = ordersMapper.count(startTime, LocalDateTime.now(), merchantVO.getmId());
                merchantVO.setOrderCurrentMonth(monthOrderCount > 0);
            }
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(result));
    }


    @Override
    public AjaxResult selectMerchantList(MerchantVO record) {
        List<MerchantVO> result = merchantMapper.selectMerchantList(record);
        if (!CollectionUtils.isEmpty(result) && result.size() > 5) {
            result = result.subList(0, 5);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(result));
    }

    @Override
    public AjaxResult selectDetail(MerchantVO selectKeys) {
        MerchantVO merchantVO = this.getBaseDetail(selectKeys.getmId());

        // 商户运营区域名称,等级
        Area area = areaMapper.queryByAreaNo(merchantVO.getAreaNo());
        if (null == area) {
            logger.error("未找到area:{}", merchantVO.getAreaNo(), new Exception());
            merchantVO.setAreaName("暂无");
            merchantVO.setAreaGrade("暂无");
        } else {
            merchantVO.setAreaName(area.getAreaName());
            merchantVO.setAreaGrade(area.getGrade());
        }

        //计算拜访天数和未下单天数
        merchantVO.setReassign(selectKeys.getReassign());
        followUpRelationService.notOrderOrFollow(merchantVO);

        //今天是否有拜访计划
        VisitPlanVO query = new VisitPlanVO();
        query.setAdminId(getAdminId());
        query.setDate(LocalDate.now());
        query.setMId(selectKeys.getmId());
        query.setAreaNo(merchantVO.getAreaNo());
        List<VisitPlanVO> visitPlanVOList = visitPlanMapper.selectList(query);
        merchantVO.setHasVisitPlanToday(!CollectionUtils.isEmpty(visitPlanVOList));

        // 本月gmv 本月配送客单价
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
        merchantVO.setUpdateTime(thisMonth.getDateFlag());
        PrivateSeaVO mGmv = crmMerchantDayGmvMapper.selectByMid(selectKeys.getmId().intValue(), thisMonth.getDateFlag());
        if(Objects.nonNull(mGmv)){
            merchantVO.setThisMonthGmv(mGmv.getThisMonthGmv());
            merchantVO.setThisMonthDeliveryUnitPrice(mGmv.getThisMonthDeliveryUnitPrice());
            merchantVO.setThisDistributionGmv(mGmv.getDistributionGmv());
            merchantVO.setCoreMerchantTag(mGmv.getCoreMerchantTag());
            merchantVO.setDistributionAmount(mGmv.getDistributionAmount());
        }
        // 跟进城市取核心客户的阈值
        CrmCommissionMerchantLevel crmCommissionMerchantLevel = crmCommissionMerchantLevelMapper.selectCoreMerchantByAreaNo(merchantVO.getAreaNo());
        if (Objects.nonNull(crmCommissionMerchantLevel)) {
            merchantVO.setGmvThreshold(crmCommissionMerchantLevel.getGmvMinimum());
            merchantVO.setPriceThreshold(crmCommissionMerchantLevel.getPriceMinimum());
        }
        // 上月gmv,配送客单价,配送gmv
        DataSynchronizationInformation lastMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_MONTH_GMV.getTableName());
        PrivateSeaVO lastGmv = crmMerchantMonthGmvMapper.selectByMid(selectKeys.getmId().intValue(),lastMonth.getDateFlag());
        if(Objects.nonNull(lastGmv)){
            merchantVO.setLastMonthGmv(lastGmv.getThisMonthGmv());
            merchantVO.setLastMonthDeliveryUnitPrice(lastGmv.getThisMonthDeliveryUnitPrice());
            merchantVO.setLastDistributionGmv(lastGmv.getDistributionGmv());
        }
        // 近三个月平均下单周期,若只有一笔订单，平均周期等于未下单天数
        int orderCycle = followUpRelationService.averageOrderCycle(selectKeys.getmId());
        merchantVO.setOrderCycle(orderCycle);
        if(orderCycle == 0){
            merchantVO.setOrderCycle(merchantVO.getNotOrder());
        }
        // 下单预警标识
        final boolean orderCycleWarn = Objects.nonNull(merchantVO.getNotOrder()) && (merchantVO.getNotOrder() > merchantVO.getOrderCycle()
                || merchantVO.getNotOrder() > 30);
        merchantVO.setOrderCycleWarn(orderCycleWarn);

        // 当前已超过平均下单周期天数
        if(orderCycleWarn){
            int moreThanOrderCycle = merchantVO.getNotOrder() - merchantVO.getOrderCycle();
            merchantVO.setMoreThanOrderCycle(moreThanOrderCycle);
        }

        return AjaxResult.getOK(merchantVO);
    }

    @Override
    public AjaxResult baseDetail(Long mId){
        MerchantVO merchantVO = this.getBaseDetail(mId);
        return AjaxResult.getOK(merchantVO);
    }

    private MerchantVO getBaseDetail(Long mId){
        // 客户信息
        MerchantVO merchantVO = merchantMapper.selectMerchantByMid(mId);
        if(Objects.isNull(merchantVO)){
            return new MerchantVO();
        }
        // 客户联系地址
        Contact select = new Contact();
        select.setStatus(ContactStatusEnum.COMMON.ordinal());
        select.setmId(mId);
        List<Contact> contacts = contactMapper.select(select);
        merchantVO.setContacts(contacts);

        //历史下单
        int orderCount = ordersMapper.countOrderByMId(mId);
        merchantVO.setHistoryOrderCount(orderCount);

        //历史售后
        int afterCount = afterSaleOrderMapper.countByMId(mId);
        merchantVO.setHistoryAfterSaleCount(afterCount);

        //历史商品
        int skuAmount = orderItemMapper.queryTotalOrderAmount(mId);
        merchantVO.setHistorySkuAmount(skuAmount);

        //历史流转
        int relationCount = followUpRelationMapper.countByMId(mId);
        merchantVO.setHistoryRelation(relationCount);

        // 获取全部客户标签
        List<String> merchantAllLabel = this.selectMerchantAllLabelByMid(mId);
        if(CollectionUtil.isNotEmpty(merchantAllLabel)){
            // 需要展示的周年庆标签
            List<String> allAnniversaryEnumValue = MerchantLabelEnum.getAllAnniversaryEnumValue();
            // 其他标签
            List<String> middleList = new ArrayList<>(merchantAllLabel);
            middleList.removeAll(allAnniversaryEnumValue);
            // 需要的标签
            merchantAllLabel.removeAll(middleList);
            merchantVO.setMerchantLabelList(merchantAllLabel);
        }
        return merchantVO;
    }

    @Override
    public AjaxResult selectMerchantLabel(Long mId){
        // 商户标签
        return AjaxResult.getOK(this.selectMerchantAllLabelByMid(mId));
    }

    @Override
    public AjaxResult selectMerchantSpuLabel(int pageIndex, int pageSize, Long mId){
        // 商户全部标签
        List<String> pdNameList = this.selectMerchantAllLabelByMid(mId);
        if(CollectionUtils.isEmpty(pdNameList)){
            return AjaxResult.getOK();
        }
        PageHelper.startPage(pageIndex,pageSize);
        List<Products> products = productsMapper.selectByCIdAndPdName(null, null, pdNameList);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(products));
    }

    @Override
    public AjaxResult selectContact(Long contactId) {
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        if(Objects.isNull(contact)){
            return AjaxResult.getErrorWithMsg("联系人信息已被删除,请添加后选择地址");
        }
        PoiVO poiVO = SplitUtils.string2poi(contact.getPoiNote());
        contact.setPoi(poiVO);
        return AjaxResult.getOK(contact);
    }

    private List<String> selectMerchantAllLabelByMid(Long mId){
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_LABEL.getTableName());
        Integer dataTag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
        return crmMerchantDayLabelMapper.selectByPrimaryKey(mId, dataTag);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult updateContact(Contact contact) {
        contactMapper.updateByPrimaryKeySelective(contact);
        return AjaxResult.getOK();
    }



    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult updateStatus(Long id, Byte islock, String blackRemark) {

        //是否是大客户
        Merchant queryMerchant = merchantMapper.selectByMId(id);
        //是大客户切大客户归属是拉黑状态,需要更改用户信息
        if (Objects.equals(queryMerchant.getSize(), "大客户") && queryMerchant.getIslock() == 3) {
            Admin admin = adminMapper.selectByPrimaryKey(queryMerchant.getAdminId());
            if (admin.getIsDisabled()) {//NOSONAR
                //将大客户更改为单店用户
                Orders orders = ordersMapper.selectLast(id);
                if (orders != null) {
                    return AjaxResult.getErrorWithMsg("订单状态和售后完结才可切换城市");
                }
                int num = afterSaleOrderMapper.countNotClose(id);
                if (num > 0) {
                    return AjaxResult.getErrorWithMsg("订单状态和售后完结才可切换城市");
                }
                merchantMapper.updateMerchantSize(id);
                logger.info("管理员：{}修改了商户:{}({})审核状态信息,大客户改为单店", this.getAdminName(), id, queryMerchant.getMname());

                return AjaxResult.getOK("修改成功");
            }
        }
        Merchant merchant = new Merchant();
        merchant.setmId(id);
        merchant.setIslock(islock);
        merchant.setPullBlackRemark(blackRemark);
        merchant.setPullBlackOperator(getAdminName());
        int result = merchantMapper.updateStatus(merchant);
        if (islock == 3) {
            List ids = new ArrayList();
            ids.add(id);
            followUpRelationService.reassign(ids, null, "拉黑释放", null);
        }

        //取消拉黑，恢复店长账号
        if (queryMerchant.getIslock() == 3 && islock == 0) {
            MerchantSubAccount subAccount = merchantSubAccountMapper.selectManageByMid(id);
            MerchantSubAccount update = new MerchantSubAccount();
            update.setAccountId(subAccount.getAccountId());
            update.setStatus(1);
            merchantSubAccountMapper.updateSelective(update);
        }

        /**
         * 对为大客户的门店处理，拉黑和释放拉黑均记录
         */
        if(Objects.equals(queryMerchant.getSize(), "大客户")){
            MerchantUpdateRecord insert = new MerchantUpdateRecord();
            insert.setMId(queryMerchant.getmId());
            insert.setCreatorId(getAdminId().longValue());
            //门店为大客户类型且被拉黑的则记录到记录中
            if(queryMerchant.getIslock() == 0 && islock == 3){
                insert.setChangeType(2);
                insert.setOldAdmin(queryMerchant.getAdminId().longValue());
            }
            //门店为大客户类型且释放
            if(queryMerchant.getIslock() == 3 && islock == 0 ){
                insert.setChangeType(3);
                insert.setNewAdmin(queryMerchant.getAdminId().longValue());
            }
            merchantUpdateRecordMapper.insertSelective(insert);
        }

        if (result == 0) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        logger.info("管理员：{}修改了商户:{}({})审核状态信息", this.getAdminName(), id, queryMerchant.getMname());
        return AjaxResult.getOK("修改成功");
    }

    @Override
    public AjaxResult countNewMerchants(Date startTime, Date endTime) {
        startTime = DateUtils.localDateTime2Date(LocalDateTime.of(DateUtils.date2LocalDate(startTime), Global.CLOSING_ORDER_TIME));
        endTime = DateUtils.localDateTime2Date(LocalDateTime.of(DateUtils.date2LocalDate(endTime), Global.CLOSING_ORDER_TIME));

        int newMerchants = merchantMapper.countNewMerchants(startTime, endTime);
        return AjaxResult.getOK(newMerchants);
    }

    /**
     * 审核后的操作
     *
     * @param merchant
     * @param areaNo
     * @throws IOException
     */
    private void afterReviewMerchant(Merchant merchant, MerchantVO merchantVO) throws IOException {
        if(Objects.equals(merchantVO.getOperateStatus(),0)) {
            // 正常经营的发送优惠券
            this.sendCouponForAuditSuccess(merchant, merchantVO.getAreaNo());
        }

        saveFollowUpRelation(merchant);
    }

    private void saveFollowUpRelation(Merchant merchant) {
        FollowUpRelation followUpRelation = new FollowUpRelation(merchant.getmId(), 1, "杨晗");
        followUpRelation.setReassign(true);
        if (!("seeegj".equalsIgnoreCase(merchant.getInvitecode()) || "5axszx".equalsIgnoreCase(merchant.getInvitecode()))) {
            Admin admin = adminMapper.selectByInvitecode(merchant.getInvitecode());
            if (Objects.nonNull(admin)) {
                followUpRelation = new FollowUpRelation(merchant.getmId(), admin.getAdminId(), admin.getRealname());
                followUpRelation.setReassign(false);
            }
        }
        followUpRelation.setReason("新注册用户");
        followUpRelationService.updateAndInsertFollow(followUpRelation);
    }

    private void sendCouponForAuditSuccess(Merchant merchant, Integer areaNo) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    List<String> adminIds = configService.getValues(Global.POP_ADMIN_ID);
                    Set<Integer> adminIdSet = adminIds.stream().map(Integer::valueOf).collect(Collectors.toSet());

                    //单店或者POP大客户发放新人优惠券
                    logger.info("发放新人优惠券, merchant:{}", JSON.toJSONString(merchant));
                    if (MerchantSizeEnum.SINGGLE_STORE.getValue().equals(merchant.getSize())
                            || (MerchantSizeEnum.MAJOR_ACCOUNT.getValue().equals(merchant.getSize()) && !CollectionUtils.isEmpty(adminIdSet)
                            && adminIdSet.contains(merchant.getAdminId()))) {
                        if (MerchantSizeEnum.MAJOR_ACCOUNT.getValue().equals(merchant.getSize())) {
                            //发送优惠券
                            logger.info("POP大客户注册发放新人券, merchant:{}", JSON.toJSONString(merchant));
                            couponService.sendCoupon(areaNo, merchant, "afterReview");
                        } else {
                            //门店已注销的用户不发放新人劵--第一期POP客户暂不做注销校验（无法标识注销来源，目前允许商城注销到POP商城申请注册发放新人券）
                            MerchantCancelReq merchantCancelReq = new MerchantCancelReq();
                            merchantCancelReq.setPhone(merchant.getPhone());
                            merchantCancelReq.setStatus(MerchantCancelEnum.CANCELLED.getCode());
                            MerchantCancelVO merchantCancelVO = merchantCancelService.getInfo(merchantCancelReq);
                            if (Objects.isNull(merchantCancelVO)) {
                                //发送优惠券
                                logger.info("普通注册发放新人券, merchant:{}", JSON.toJSONString(merchant));
                                couponService.sendCoupon(areaNo, merchant, "afterReview");
                            } else {
                                logger.info("普通注册发放新人券, merchant:{}, 已注销", JSON.toJSONString(merchant));
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("优惠券发送失败", e);
                }
            }
        });
    }

    @Override
    public AjaxResult merchantData() {
        Map map = new HashMap();
        LocalDate date = LocalDate.now();
        LocalDateTime now = date.atTime(LocalTime.now());
        map.put("lifecycle", merchantLifecycleMapper.lifecycleData(date.minusDays(1).atTime(0, 0, 0), now, null, null));
        List<DataVO> dataVOS = merchantMapper.merchatSizeData(now);
        List<DataVO> dataVOEnterpriseScale =  merchantMapper.merchatEnterpriseScaleData(now);
        dataVOS.addAll(dataVOEnterpriseScale);
        map.put("size", dataVOS);
        map.put("type", merchantMapper.merchatTypeData(now));
        return AjaxResult.getOK(map);
    }

    @Override
    public AjaxResult merchantRingData(String type, String value) {
        if (StringUtils.isEmpty(type) || (!"day".equals(type) && !"week".equals(type))) {
            return AjaxResult.getErrorWithMsg("参数错误");
        }
        if (StringUtils.isEmpty(value) || (!"lifecycle".equals(value) && !"size".equals(value) && !"type".equals(value))) {
            return AjaxResult.getErrorWithMsg("参数错误!");
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oldTime = null;
        if ("day".equals(type)) {
            oldTime = now.minusDays(1);
        } else {
            oldTime = now.minusDays(7);
        }
        Map map = new HashMap();
        if ("lifecycle".equals(value)) {
            map.put("now", merchantLifecycleMapper.lifecycleData(null, now, null, null));
            map.put("old", merchantLifecycleMapper.lifecycleData(null, oldTime, null, null));
        } else if ("size".equals(value)) {
            List<DataVO> nowDataVOS = merchantMapper.merchatSizeData(now);
            List<DataVO> dataVOS1 = merchantMapper.merchatEnterpriseScaleData(now);
            nowDataVOS.addAll(dataVOS1);
            map.put("now", nowDataVOS);
            List<DataVO> oldDataVOS = merchantMapper.merchatSizeData(oldTime);
            List<DataVO> dataVOS2 = merchantMapper.merchatEnterpriseScaleData(oldTime);
            oldDataVOS.addAll(dataVOS2);
            map.put("old", oldDataVOS);
        } else {
            map.put("now", merchantMapper.merchatTypeData(now));
            map.put("old", merchantMapper.merchatTypeData(oldTime));
        }
        return AjaxResult.getOK(map);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult cancelInviter(long mId) {
        int rs = merchantMapper.updateInviter2Null(mId);
        if (rs > 1) {
            throw new DefaultServiceException();
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult assignArea(Merchant merchant) {
        int rs = merchantMapper.updateByPrimaryKeySelective(merchant);
        if (rs > 1) {
            throw new DefaultServiceException();
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult signTags(MerchantReq select) {
        select = Optional.ofNullable(select).orElse(new MerchantReq());
        if (StringUtils.isEmpty(select.getTagName())) {
            return AjaxResult.getErrorWithMsg("请先填写标签名称");
        }

        // 处理会员等级为空的情况
        if(CollectionUtil.isNotEmpty(select.getGrades())){
            boolean containGradeIsNull = select.getGrades().contains(net.summerfarm.common.util.NumberUtils.INTEGER_ONE_HUNDREDS);
            select.setGradeIsNull(containGradeIsNull);
        }

        List<Merchant> merchantList = merchantMapper.selectSomeMerchants(select);
        if (CollectionUtils.isEmpty(merchantList)) {
            return AjaxResult.getErrorWithMsg("没有符合条件的用户");
        }

        List<Long> mIdList = merchantList.stream().map(Merchant::getmId).collect(Collectors.toList());
        StringBuilder mIds = new StringBuilder();
        for (Long mId : mIdList) {
            mIds.append(mId).append(Global.SEPARATING_SYMBOL);
        }

        if (StringUtils.isEmpty(mIds.toString())) {
            return AjaxResult.getErrorWithMsg("没有符合条件的用户");
        }

        mIds.deleteCharAt(mIds.toString().length() - 1);
        TagManageReq tagManageReq = new TagManageReq();
        tagManageReq.setTagName(select.getTagName());
        tagManageReq.setMIds(mIds.toString());
        Integer tagId = merchantSubFacade.merchantSignTags(tagManageReq);
        if (null != tagId) {
            JSONObject param = new JSONObject();
            param.put("tagName", select.getTagName());
            param.put("mIds", mIds.toString());
            param.put("tagId", tagId);
            MQData mqData = new MQData();
            mqData.setType(MType.UPDATE_SIGN.name());
            mqData.setData(param);
            mqProducer.send(RocketMqMessageConstant.MANAGE_LIST,null,JSON.toJSONString(mqData));
        } else {
            return AjaxResult.getErrorWithMsg("tagId is null");
        }
        return AjaxResult.getOK(tagId);
    }

    @Override
    public AjaxResult queryMerchantRelease(int pageIndex, int pageSize, MerchantVO record) {
        // 获取销售所有的私海id
        PageHelper.startPage(pageIndex,pageSize);
        List<MerchantVO> merchants = merchantMapper.queryAllMerchantVO(record);
        // 设置销售角色
        boolean roleTag = isAreaSA() || isSA() || isSaleSA();
        if(roleTag){
            merchants.forEach(m -> m.setRoleTag(true));
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(merchants));
    }

    @Override
    public AjaxResult queryMerchantWightLight() {
        MerchantReq merchantReq = new MerchantReq();
        Integer adminId = super.getAdminId();
        // 查询客户激励配置
        CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(adminId);
        if(Objects.isNull(crmBdConfig)){
            return AjaxResult.getErrorWithMsg("请先前往管理后台,配置bd激励指标");
        }
        // 查询客户私海容量
        int privateNum = followUpRelationMapper.selectPrivateNum(adminId,null);
        // 去除白名单客户
        int whiteNum = followWhiteListMapper.selectNumByBd(adminId,null);
        // 查询数据更新时间
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
        merchantReq.setDataUpdateTime(thisMonth.getDateFlag());
        merchantReq.setPrivateNum(crmBdConfig.getPrivateSeaLimit());
        merchantReq.setCurPrivateNum(privateNum - whiteNum);
        return AjaxResult.getOK(merchantReq);
    }

    @Override
    public AjaxResult queryBDPrivateSea(int type, MerchantVO record) {

        Integer adminId = getAdminId();
        Integer reassign = 0;
        if (type == 1 && (isAreaSA() || isSaleSA() || isSA())) {
            adminId = null;
            reassign = null;
        }
        record.setAdminId(adminId);
        record.setReassign(reassign);
        List<MerchantVO> merchantVOS = merchantMapper.queryBDPrivateSea(record);
        for (MerchantVO merchantVO : merchantVOS) {
            List<String> labelList = this.selectMerchantAllLabelByMid(merchantVO.getmId());
            if(CollectionUtil.isNotEmpty(labelList)){
                merchantVO.setMerchantLabelList(labelList);
            }
        }
        return AjaxResult.getOK(merchantVOS);
    }

    @Override
    public AjaxResult existMerchantName(Long leadId, String mname) {
        Map<String, String> condition = new HashMap<>();
        condition.put("mname", mname);
        Merchant merchant = merchantMapper.selectOne(condition);

        boolean flag = false;
        if (merchant != null) {
            flag = true;
        } else {
            MerchantLeads leads = merchantLeadsMapper.selectByMname(mname);
            if (leads != null) {
                if (leadId != null) {
                    //判断是否和自己名称相同
                    flag = !Objects.equals(leadId, leads.getId());
                }
            }
        }

        return AjaxResult.getOK(flag);
    }

    @Override
    public AjaxResult querySimilarMerchant(Merchant merchant) {
        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
        } catch (Exception e) {
            throw new DefaultServiceException("es连接异常");
        }

        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder
                .filter(new TermQueryBuilder("islock", 0))
                .must(new TermQueryBuilder("contacts.status", 1))
                .must(new MatchQueryBuilder("contacts.city", merchant.getCity()))
                .must(new MatchQueryBuilder("contacts.province", merchant.getProvince()).analyzer("ik_max_word"));
        if(StringUtils.isNotBlank(merchant.getArea())){
            queryBuilder.must(new MatchQueryBuilder("contacts.area", merchant.getArea()));
        }
        if(StringUtils.isNotBlank(merchant.getAddress())){
            queryBuilder.must(new MatchQueryBuilder("contacts.address", merchant.getAddress()));
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.from(0).size(3);
        searchSourceBuilder.query(queryBuilder);


        SearchRequest searchRequest = new SearchRequest(EsIndexContext.INDEX_MERCHANT);
        searchRequest.source(searchSourceBuilder);

        List<MerchantVO> result = new ArrayList<>();
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            response.getHits().forEach(el -> {
                MerchantVO vo = JSONObject.parseObject(el.getSourceAsString(), MerchantVO.class);
                //保留有效地址
                vo.setContacts(vo.getContacts().stream().filter(contact -> Objects.equals(1, contact.getStatus())).collect(Collectors.toList()));

                //BD、上次下单时间
                FollowUpRelation condition = new FollowUpRelation();
                condition.setmId(vo.getmId());
                FollowUpRelation followUpRelation = followUpRelationMapper.selectOne(condition);
                if (followUpRelation != null) {
                    vo.setAdminRealname(followUpRelation.getAdminName());
                }
                Orders orders = ordersMapper.selectLast(vo.getmId());
                if (orders != null) {
                    vo.setLastOrderTime(orders.getOrderTime());
                }

                result.add(vo);
            });
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            EsClientPoolUtil.returnClient(client);
        }

        return AjaxResult.getOK(result);
    }

    @Override
    public AjaxResult addMerchant(Merchant merchant) {
        String phone = merchant.getPhone();
        if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(merchant.getMname())) {
            return AjaxResult.getErrorWithMsg("该店铺名或者手机号为空");
        }
        Merchant queryOneMerchant = merchantMapper.selectIsUserName(null, phone);
        HashMap selectKey = new HashMap();
        selectKey.put("phone", phone);
        List<MerchantSubAccount> result2 = merchantSubAccountMapper.selectList(selectKey);
        if (queryOneMerchant != null || !CollectionUtils.isEmpty(result2)) {
            return AjaxResult.getErrorWithMsg("该手机号已经注册");
        }
        Merchant queryMerchant = merchantMapper.selectIsUserName(merchant.getMname(), null);
        if (queryMerchant != null) {
            return AjaxResult.getErrorWithMsg("该店铺名已经注册");

        }
        merchant.setOpenid(merchant.getPhone());
        merchant.setRegisterTime(new Date());
        ProvinceConveter.convert(merchant, "addMerchant");
        merchantMapper.insertSelective(merchant);
        MerchantSubAccount merchantSubAccount = new MerchantSubAccount();
        merchantSubAccount.setMId(merchant.getmId());
        merchantSubAccount.setType(0);
        merchantSubAccount.setContact(merchant.getMcontact());
        merchantSubAccount.setPhone(phone);
        merchantSubAccount.setOpenid(phone);
        merchantSubAccount.setRegisterTime(new Date());
        merchantSubAccountMapper.insertRecord(merchantSubAccount);
        //添加关联
        if(!Objects.isNull(merchant.getmId())){
            FollowUpRelation followUpRelation = new FollowUpRelation(merchant.getmId(), 1, "杨晗");
            followUpRelation.setReassign(true);
            followUpRelation.setReason("新注册用户");
            followUpRelationService.updateAndInsertFollow(followUpRelation);
        }

        return AjaxResult.getOK();
    }

    @Override
    @Transactional
    public AjaxResult addContact(Contact contact) {
        contact.setStatus(3);
        if (contact.getContactAddressRemark() != null) {
            contact.setAddressRemark(JSONUtil.toJsonStr(contact.getContactAddressRemark()));
        }
        contactMapper.insertSelective(contact);
        //日志
        ContactOperateLog contactOperateLog = new ContactOperateLog();
        contactOperateLog.setMId(contact.getmId());
        contactOperateLog.setContactId(contact.getContactId());
        contactOperateLog.setCreateTime(LocalDateTime.now());
        contactOperateLog.setUpdateTime(LocalDateTime.now());
        contactOperateLog.setContext("创建联系人");
        contactOperateLog.setOperateType(ContactLogEnum.OperateType.ADD.ordinal());
        contactOperateLog.setOperateName(getAdminName());
        contactOperateLog.setOperateSource(ContactLogEnum.OperateSource.MANAGE.ordinal());
        contactOperateLogMapper.insertSelective(contactOperateLog);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult queryTodayRegister(String mname) {
        List<MerchantVO> merchantList = merchantMapper.selectRegisterPrivateSea(mname, LocalDate.now(), getAdminId());
        merchantList.forEach(el -> {
            Contact query = new Contact();
            query.setmId(el.getmId());
            query.setStatus(1);
            List<Contact> contacts = contactMapper.select(query);
            el.setContacts(contacts);
        });
        return AjaxResult.getOK(merchantList);
    }


    @Override
    public AjaxResult queryCluePool(Merchant merchant) {

        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
        } catch (Exception e) {
            throw new DefaultServiceException("es连接异常");
        }
        String poiNote = merchant.getPoiNote();
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        //排除已经绑定数据
        queryBuilder
                .mustNot(new TermQueryBuilder("manage", 1))
                .must(new MatchQueryBuilder("shop_name",merchant.getMname()));
        if(StringUtils.isNotBlank(merchant.getCity())){
            queryBuilder.should(new MultiMatchQueryBuilder(merchant.getCity().replace("市", ""), "city", "district"));
        }

        //根据poi两点间距离排序
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if(!StringUtils.isEmpty(poiNote)){
            String[] split = poiNote.split(",");
            GeoDistanceSortBuilder sortBuilder =
                    SortBuilders.geoDistanceSort("poi",Double.valueOf(split[1]), Double.valueOf(split[0]))
                            .unit(DistanceUnit.METERS)
                            .order(SortOrder.ASC);
            searchSourceBuilder.sort(sortBuilder);
        }

        searchSourceBuilder.from(0).size(10);
        searchSourceBuilder.query(queryBuilder);
        SearchRequest searchRequest = new SearchRequest(net.summerfarm.common.util.es.EsIndexContext.INDEX_CRUL_POOL);
        searchRequest.source(searchSourceBuilder);
        List<Area> areas = areaMapper.selectSecond();
        List<CluePoolDO> result = new ArrayList<>();
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits hits = response.getHits();
            //根据poi和店铺名称搜索数据
            List<SearchHit> arrayHit = Arrays.asList(hits.getHits());
            //整体数据
            List<SearchHit> searchHits = new ArrayList<>();
            searchHits.addAll(arrayHit);
            // 根据地址搜索周边
            if(!StringUtils.isEmpty(poiNote)){
                searchHits.addAll(searchByPoi(arrayHit,client,searchRequest,searchSourceBuilder));
            }
            for (SearchHit hit : searchHits) {
                String sourceAsString = hit.getSourceAsString();
                String id = hit.getId();
                CluePoolDO curlPoolDO = JSONObject.parseObject(sourceAsString, CluePoolDO.class);
                curlPoolDO.setAreaNo(Global.matchAreaNo(curlPoolDO.getProvince(), curlPoolDO.getCity()+"市", curlPoolDO.getDistrict(), areas));
                curlPoolDO.setId(id);
                String phone = curlPoolDO.getPhone();
                if(!net.summerfarm.common.util.StringUtils.isMobile(phone)){
                    curlPoolDO.setPhone(null);
                }
                curlPoolDO.setCity(curlPoolDO.getCity()+"市");
                result.add(curlPoolDO);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            EsClientPoolUtil.returnClient(client);
        }
        return AjaxResult.getOK(result);
    }

    @Async
    @Override
    public  AjaxResult updateCurlPool( MerchantCluePool merchantCluePool,Integer manage,String oldEsId){

        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
        } catch (Exception e) {
            throw new DefaultServiceException("es连接异常");
        }
        try {
            //更新绑定信息
            if(merchantCluePool != null){
                UpdateRequest updateRequest = new UpdateRequest();
                updateRequest.index(net.summerfarm.common.util.es.EsIndexContext.INDEX_CRUL_POOL);
                updateRequest.id(merchantCluePool.getEsId());
                updateRequest.doc(XContentFactory.jsonBuilder().startObject().field("manage",manage).endObject());
                UpdateResponse update = client.update(updateRequest, RequestOptions.DEFAULT);
                merchantCluePool.setEsId( update.getId());
            }

            //取消原来的绑定
            if(!StringUtils.isEmpty(oldEsId)){
                UpdateRequest updateOldRequest = new UpdateRequest();
                updateOldRequest.index(net.summerfarm.common.util.es.EsIndexContext.INDEX_CRUL_POOL);
                updateOldRequest.id(oldEsId);
                updateOldRequest.doc(XContentFactory.jsonBuilder().startObject().field("manage",0).endObject());
                client.update(updateOldRequest,RequestOptions.DEFAULT);
            }


        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            EsClientPoolUtil.returnClient(client);
        }

        //取消线索池关联
        if(!StringUtils.isEmpty(oldEsId)){
            MerchantCluePool updatePool = new MerchantCluePool();
            updatePool.setEsId(oldEsId);
            updatePool.setStatus(MerchantCluePool.CLUE_POOL_NOT_EFFICACY);
            merchantCluePoolMapper.updateMerchantCluePool(updatePool);
        }
        //插入关联数据
        if(merchantCluePool != null){
            merchantCluePool.setStatus(MerchantCluePool.CLUE_POOL_EFFICACY);
            merchantCluePoolMapper.insertMerchantCluePool(merchantCluePool);
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult queryBDArea() {
        Integer adminId = getAdminId();
        List<Area> areas = areaMapper.selectBdArea(adminId);
        return AjaxResult.getOK(areas);
    }

    /**
     * 根据mname,phone验证是否存在，若是存在则返回mId
     * @param input
     * @return
     */
    @Override
    public AjaxResult infoIsTrue(FeedbackWoOrderInput input) {
        String mname = input.getMname();
        String phone = input.getPhone();
        Merchant merchant = null;
        if(StringUtils.isNotBlank(mname) && StringUtils.isNotBlank(phone)){
            throw new DefaultServiceException("mname和phone不能同时入参！");
        }
        if(StringUtils.isBlank(mname) && StringUtils.isBlank(phone)){
            throw new DefaultServiceException("mname，phone 不能同时为空！");
        }
        Merchant queryContent = new Merchant();

        //根据phone查询唯一的mId，若是没有则data内为空
        if(StringUtils.isNotBlank(phone)){
            queryContent.setPhone(phone);
            List<Merchant> merchants = merchantMapper.selectIssueUser(phone);
            if(CollectionUtils.isEmpty(merchants)){
                return AjaxResult.getOK();
            }
            if(merchants.size() > 1){
                throw  new DefaultServiceException("入参的手机号对应的门店不唯一！");
            }
            merchant = merchants.get(0);
        }

        //查询根据mname精准查询对应的mId，若是没有则data内为空
        if(StringUtils.isNotBlank(mname)){
            queryContent.setMname(mname);
            Merchant merchantByName = merchantMapper.selectByMname(mname);
            if(Objects.isNull(merchantByName)){
                return AjaxResult.getOK();
            }
            merchant = merchantByName;
        }
        if (null == merchant) {
            return AjaxResult.getOK();
        } else {
            return AjaxResult.getOK(merchant.getmId());
        }
    }


    /**
     * 根据poi搜索es数据并筛选
     * @param arrayHit poi和店铺名称搜素数据
     * @param client es链接
     * @param searchRequest 请求信息
     * @param searchSourceBuilder 搜索信息
     * @return 合并poi筛选数据
     */
    private List<SearchHit> searchByPoi(List<SearchHit> arrayHit,RestHighLevelClient client,
                                        SearchRequest searchRequest,SearchSourceBuilder searchSourceBuilder){

        List<SearchHit> searchHits = new ArrayList<>();
        //数据大于搜索上限
        if(!CollectionUtils.isEmpty(arrayHit) && arrayHit.size() >= Global.QUERY_CLUE_POOL_SIZE){
            return searchHits;
        }
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        //排除已经绑定数据
        queryBuilder.filter(new TermQueryBuilder("manage", 0));
        searchSourceBuilder.query(queryBuilder);
        searchRequest.source(searchSourceBuilder);
        //只根据poi搜索数据
        try {
            SearchResponse search = client.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits queryHits = search.getHits();
            SearchHit[] poiHits = queryHits.getHits();
            List<SearchHit> queryByPoi = Arrays.asList(poiHits);
            //为空直接返回数据
            if(CollectionUtils.isEmpty(arrayHit)){
                searchHits.addAll(queryByPoi);
                return searchHits;
            }
            //可查询数据数量
            Integer length = Global.QUERY_CLUE_POOL_SIZE - arrayHit.size();
            //遍历移除相同数据
            for (SearchHit documentFields : queryByPoi) {
                boolean isSameEsId = false;
                for (SearchHit searchHit : arrayHit) {
                    //es_id相同
                    if (Objects.equals(documentFields.getId(), searchHit.getId())) {
                        isSameEsId = true;
                        break;
                    }
                }
                //es_id相同则剔除
                if (!isSameEsId) {
                    searchHits.add(documentFields);
                }
                //超过数量结束
                if( searchHits.size() >= length){
                    break;
                }
            }
        }catch (IOException e) {
            logger.info("err Msg={}",e.getMessage());
        }

        return searchHits;
    }

    /**
     * 外部平台订单同步批量门店账号导入模板下载
     * @return
     */
    @Override
    public void templateDownload() {
        try {
            CommonFileUtils.exportFile(RequestHolder.getResponse(), Global.TEMPLATE_DIR, "外部平台订单同步店铺关联.xls");
        } catch (Exception e) {
            logger.error(Global.collectExceptionStackMsg(e));
            throw new DefaultServiceException("导出异常");
        }
    }

    /**
     * 批量处理茶百道门店账号
     * @param file
     * @return
     */
    @Override
    @Transactional
    @XmLock(waitTime = 1000 * 60, key = "(MerchantService.handleBatchMerchant)")
    public AjaxResult handleBatchMerchant(MultipartFile file)  {
        //将表格内容放入List<MerchantBatchHandlerVO>
        ExcelReader reader = null;
        try {
            reader = ExcelUtil.getReader(file.getInputStream(),0);
        } catch (Exception ioException) {
            throw new DefaultServiceException("读取excel表格失败！");
        }
        HashMap<String, String> headerAlias = new HashMap<>(9);
        headerAlias.put("*鲜沐门店名称","mname");
        //headerAlias.put("*外部门店编码","outerNo");
        headerAlias.put("合作模式","type");
        headerAlias.put("*联系人","mContact");
        headerAlias.put("*联系电话","phone");
        headerAlias.put("*所属总部编号","leaderMerchant");
        headerAlias.put("*省","province");
        headerAlias.put("*市","city");
        headerAlias.put("*区","area");
        headerAlias.put("*详细地址","address");
        headerAlias.put("*大客户发票id","invoiceId");
        reader.setHeaderAlias(headerAlias);
        List<MerchantBatchHandlerVO> allList = reader.readAll(MerchantBatchHandlerVO.class);
        if(allList.isEmpty()){
            throw new DefaultServiceException("需要导入的数据项为空！");
        }
        Integer allListSize = Optional.of(allList.size()).orElse(0);

        //返回错误的集合
        List<MerchantBatchHandlerVO> errorList = new ArrayList<>();

        //获取到已处理的list
        List<MerchantBatchHandlerVO> handlerList = new ArrayList<>();
        //对allList内每一个字符批量去除两边空白字符在插入的时候和查询时考虑去除，对店铺名去除特殊符号;
        allList.forEach(item -> {
            String outerPlatformName = item.getOuterPlatformName();
            String mname = item.getMname();
            String mnameStr = StringUtils.trimToNull(net.summerfarm.common.util.StringUtils.replaceSpecialCharacters(mname));
            String phone = StringUtils.trimToNull(item.getPhone());
            String mContact = StringUtils.trimToNull(item.getmContact());
            String outerNo = StringUtils.trimToNull(item.getOuterNo());
            String province = StringUtils.trimToNull(item.getProvince());
            String leaderMerchant = StringUtils.trimToNull(item.getLeaderMerchant());
            String city = StringUtils.trimToNull(item.getCity());
            String area = StringUtils.trimToNull(item.getArea());
            String address = StringUtils.trimToNull(item.getAddress());
            String type= StringUtils.trimToNull(item.getType());
            Integer invoiceId = item.getInvoiceId();

            item = new MerchantBatchHandlerVO(outerPlatformName,mnameStr, outerNo, mContact, phone, leaderMerchant, province, city, area, address);
            item.setType(type);
            item.setInvoiceId(invoiceId);
            //必填项未填
            if(/*Objects.isNull(outerPlatformName) ||*/ Objects.isNull(mnameStr) || Objects.isNull(phone) || Objects.isNull(mContact) /*|| Objects.isNull(outerNo)*/ ||
                    Objects.isNull(province) || Objects.isNull(leaderMerchant) || Objects.isNull(city) || Objects.isNull(area) || Objects.isNull(address) || Objects.isNull(invoiceId)){
                item.setFailRemark("必填项不为空");
                errorList.add(item);
            }
            //手机号不为11位数字
            if( !errorList.contains(item) && Objects.nonNull(phone) && !net.summerfarm.common.util.StringUtils.isMobile(phone)){
                item.setFailRemark("联系方式错误");
                errorList.add(item);
            }
            //根据外部平台名称查询
            /*OuterPlatformVo  outerPlatformVo = outerPlatformMapper.selectOuterPlatformByName(item.getOuterPlatformName());
            if(outerPlatformVo ==null){
                item.setFailRemark("外部平台信息错误");
                errorList.add(item);
            }else {
                item.setOuterPlatformId(outerPlatformVo.getOuterPlatformId());
            }*/
            //所属总部编号校验
            if(!net.summerfarm.common.util.StringUtils.isNumber(leaderMerchant)){
                item.setFailRemark("所属总部，请填写鲜沐品牌编号");
                errorList.add(item);
            }else {
                Admin admin = adminMapper.selectByPrimaryKey(Integer.parseInt(leaderMerchant));
                if (admin == null) {
                    item.setFailRemark("所属总部，请填写鲜沐品牌编号");
                    errorList.add(item);
                }
            }
            handlerList.add(item);
        });


        if(!CollectionUtils.isEmpty(errorList)){
            handlerList.removeAll(errorList);
        }


        //判断handlerList中用户名或是手机号重复的 linkList
        List<MerchantBatchHandlerVO> linkList = new ArrayList<>();

        List<String> mnameList = handlerList.stream().map(MerchantBatchHandlerVO::getMname).distinct().collect(Collectors.toList());
        List<String> phoneList = handlerList.stream().map(MerchantBatchHandlerVO::getPhone).distinct().collect(Collectors.toList());
        if( !Objects.equals(handlerList.size(),mnameList.size()) || !Objects.equals(handlerList.size(),phoneList.size())){
            throw  new DefaultServiceException("表格内要插入的店铺名重复或是手机号码重复！");
        }

        //若是用户名或是手机号码已在数据库中已存在的处理
        MerchantVO queryMerchant = new MerchantVO();
        List<MerchantVO> mnameMerchantList = null;
        if(!CollectionUtils.isEmpty(mnameList)){
            queryMerchant.setMnameList(mnameList);
            mnameMerchantList = merchantMapper.selectMerchantsBySelectKeys(queryMerchant);
        }
        List<MerchantVO> phoneMerchantList = null;
        if(!CollectionUtils.isEmpty(phoneList)){
            queryMerchant.setMnameList(null);
            queryMerchant.setPhoneList(phoneList);
            phoneMerchantList = merchantMapper.selectMerchantsBySelectKeys(queryMerchant);
        }

        if(!CollectionUtils.isEmpty(mnameMerchantList)){
            Map<String, List<MerchantVO>> mnameListMap = mnameMerchantList.stream().collect(Collectors.groupingBy(MerchantVO::getMname));
            Set<String> existSet = mnameListMap.keySet();
            handlerList.forEach(el ->{
                if(existSet.contains(el.getMname())){
                    MerchantVO merchantVO = mnameListMap.get(el.getMname()).get(0);
                    el.setExId(merchantVO.getmId());
                    linkList.add(el);
                }
            });
        }

        if(!CollectionUtils.isEmpty(phoneMerchantList)){
            Map<String, List<MerchantVO>> phoneListMap = phoneMerchantList.stream().collect(Collectors.groupingBy(MerchantVO::getPhone));
            Set<String> existSet = phoneListMap.keySet();
            handlerList.forEach(el ->{
                if(existSet.contains(el.getPhone())){
                    MerchantVO merchantVO = phoneListMap.get(el.getPhone()).get(0);
                    if(!linkList.contains(el)){
                        el.setExId(merchantVO.getmId());
                        linkList.add(el);
                    }
                }
            });
        }

        // 得到可以正常操作：插入门店，联系人，安佳联系的 可插入的List
        List<MerchantBatchHandlerVO> insertList = new ArrayList<>(handlerList);
        if(!CollectionUtils.isEmpty(linkList)){
            insertList.removeAll(linkList);
        }

        //若是不为空，则对未联接的进行联接 暂时移除
/*        if(!CollectionUtils.isEmpty(linkList)){
            for (MerchantBatchHandlerVO item : linkList) {
                try {
                    MerchantOuterDO merchantOuterDO=merchantOuterMapper.selectByOuterNo(item.getOuterNo(),item.getOuterPlatformId());
                    Merchant merchantVo = merchantMapper.selectByPrimaryKey(item.getExId());
                    if (merchantOuterDO != null && !merchantOuterDO.getmId().equals(item.getExId())) {
                        item.setFailRemark("该"+item.getOuterPlatformName()+"门店编号已与其他门店“" + merchantVo.getMname() + "”关联");
                        errorList.add(item);
                        continue;
                    }
                    MerchantOuterDO queryMerchantOuterDO = merchantOuterMapper.selectByMid(item.getExId(),item.getOuterPlatformId());
                    if(queryMerchantOuterDO == null) {
                        MerchantOuterDO insertContent = new MerchantOuterDO();
                        insertContent.setmId(item.getExId());
                        insertContent.setOuterPlatformId(item.getOuterPlatformId());
                        insertContent.setOuterNo(item.getOuterNo());
                        insertContent.setRemark("外部映射导入门店编号");
                        merchantOuterMapper.insert(insertContent);
                        // 外部对接，向外部平台推送门店关联城市
                        orderOuterInfoService.mqPushStore("storeadd",item.getOuterNo(),item.getOuterPlatformId(),merchantVo.getAreaNo());
                    }else{
                        MerchantOuterDO updateContent = new MerchantOuterDO();
                        updateContent.setId(queryMerchantOuterDO.getId());
                        updateContent.setOuterPlatformId(item.getOuterPlatformId());
                        updateContent.setOuterNo(item.getOuterNo());
                        merchantOuterMapper.update(updateContent);
                        if (!queryMerchantOuterDO.getOuterNo().equals(item.getOuterNo()) ||
                                !queryMerchantOuterDO.getOuterPlatformId().equals(item.getOuterPlatformId())) {
                            // 外部对接，向外部平台推送删除门店
                            orderOuterInfoService.mqPushStore("storedel",queryMerchantOuterDO.getOuterNo(),queryMerchantOuterDO.getOuterPlatformId(),merchantVo.getAreaNo());
                        }
                        // 外部对接，向外部平台推送门店关联城市 批量导入可重复推送
                        orderOuterInfoService.mqPushStore("storeadd", item.getOuterNo(), item.getOuterPlatformId(), merchantVo.getAreaNo());
                    }
                }catch(Exception e){
                    item.setFailRemark("系统错误导致关联失败");
                    errorList.add(item);
                    logger.info("门店账号链接导入失败：MId:{},店铺名{},链接号{}\n异常：",item.getExId(), item.getMname(), item.getOuterNo(), e);
                }
            }*/
        //}

        if(!CollectionUtils.isEmpty(insertList)) {
            for (MerchantBatchHandlerVO itemVo : insertList) {
                try {
                    allInsertProcess(itemVo);
                } catch (Exception e) {
                    itemVo.setFailRemark("门店已新增，但外部门店编号已被占用，映射失败，门店管理待审核展示新增门店信息");
                    errorList.add(itemVo);
                    logger.info("茶百道账号导入失败：店铺名{}, 门店编码{}\n异常：", itemVo.getMname(), itemVo.getOuterNo(),e);
                }
            }
        }

        //若是errorList为空则返回为正常的。若是不为空则将
        String exportId = null;
        int errorListSize = 0;
        if (!CollectionUtils.isEmpty(errorList)) {
            exportId = String.valueOf(SnowflakeUtil.nextId());
            errorListSize = errorList.size();
            redisTemplate.opsForHash().put(BATCH_FAIL_MERCHANT, exportId, JSONObject.toJSONString(errorList));
            redisTemplate.expire(BATCH_FAIL_MERCHANT, 300L, TimeUnit.SECONDS);
        }
        //返回数据处理
        Map<String, Object> result = new HashMap<>();
        result.put("fail", errorListSize);
        result.put("success", allListSize - errorListSize);
        result.put("exportId", exportId);
        return AjaxResult.getOK(result);

    }

    @Override
    public void downloadFailResult(String exportId, Integer fail, Integer success) {
        Workbook workbook = merchantExcelTitle();
        workbook.setSheetName(0, "外部平台店铺关联失败表");
        Sheet sheet = workbook.getSheetAt(0);
        sheet.getRow(0).setRowStyle(workbook.createCellStyle());

        sheet.getRow(0).createCell(10).setCellValue("备注");

        if (exportId != null) {
            String jsonString = (String) redisTemplate.opsForHash().get(BATCH_FAIL_MERCHANT, exportId);
            List<MerchantBatchHandlerVO> errorList = JSONArray.parseArray(jsonString, MerchantBatchHandlerVO.class);
            for (int i = 0; i < errorList.size(); i++){
                MerchantBatchHandlerVO element = errorList.get(i);
                Row row = sheet.createRow(i + 1);
                row.createCell(0).setCellValue(element.getOuterPlatformName());
                row.createCell(1).setCellValue(element.getOuterNo());
                row.createCell(2).setCellValue(element.getMname());
                row.createCell(3).setCellValue(element.getmContact());
                row.createCell(4).setCellValue(element.getPhone());
                row.createCell(5).setCellValue(element.getLeaderMerchant());
                row.createCell(6).setCellValue(element.getProvince());
                row.createCell(7).setCellValue(element.getCity());
                row.createCell(8).setCellValue(element.getArea());
                row.createCell(9).setCellValue(element.getAddress());
                row.createCell(10).setCellValue(element.getFailRemark());
            }
        }

        String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtils.NUMBER_DATE_FORMAT));
        try {
            ExcelUtils.outputExcel(workbook, "批量新增成功" + success +"条，" + "失败" + fail + "条" +
                    time + ".xls", RequestHolder.getResponse());
        } catch (IOException e) {
            logger.warn(Global.collectExceptionStackMsg(e));
            throw new DefaultServiceException("导出异常");
        }

    }

    @Override
    public AjaxResult saveBatchMerchantCBD(MultipartFile file) {

        ExcelReader reader = null;
        try {
            reader = ExcelUtil.getReader(file.getInputStream(),0);
        } catch (Exception ioException) {
            throw new DefaultServiceException("读取excel表格失败！");
        }
        HashMap<String, String> headerAlias = new HashMap<>(8);
        headerAlias.put("门店名称","mname");
        headerAlias.put("联系方式","phone");
        headerAlias.put("所属总部(大客户编码)","adminId");
        headerAlias.put("连锁类型","enterpriseScale");
        headerAlias.put("合作模式","type");
        reader.setHeaderAlias(headerAlias);
        List<MerchantBatchHandlerVO> allList = reader.readAll(MerchantBatchHandlerVO.class);
        if(allList.isEmpty()){
            throw new DefaultServiceException("需要导入的数据项为空！");
        }

        //处理数据
        List<Merchant>  merchants = new ArrayList<>();
        try {
            String checkoutString = handleHandlerVO(allList, merchants);
            if(!StringUtils.isEmpty(checkoutString)){
                return AjaxResult.getErrorWithMsg("格式错误：" + checkoutString);
            }
        } catch (Exception e) {
            throw new DefaultServiceException("导入错误");
        }

        StringJoiner stringJoiner = new StringJoiner(",");
        //处理插入店铺信息
        Integer length = NumberUtils.INTEGER_ONE;
        for (Merchant merchant : merchants) {
            length += 1;
            String phone = merchant.getPhone();
            //校验手机号
            if(Objects.nonNull(phone) && !net.summerfarm.common.util.StringUtils.isMobile(phone)){
                stringJoiner.add(String.valueOf(length));
                continue;
            }
            try {
                AjaxResult result = addMerchant(merchant);
                //新增错误则添加到异常信息中
                if(!AjaxResult.isSuccess(result)){
                    stringJoiner.add(String.valueOf(length));
                }
            } catch ( Exception e){
                stringJoiner.add(String.valueOf(length));
            }

        }
        StringBuilder msg = new StringBuilder("执行错误（手机号或店铺名重复）行数:");
        if(stringJoiner.length() >= 1){
            msg.append(stringJoiner);
            return AjaxResult.getErrorWithMsg(msg.toString());
        }

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult templateDownloadToCBD() {
//        Workbook workbook = merchantTitleToCBD();
//        workbook.setSheetName(0,"茶百道批量新增门店表");
//        try {
//            ExcelUtils.outputExcel(workbook, "茶百道批量新增门店模板.xls", RequestHolder.getResponse());
//        } catch (IOException e) {
//            logger.error(Global.collectExceptionStackMsg(e));
//            throw new DefaultServiceException("导出异常");
//        }
        CommonFileUtils.exportFile(RequestHolder.getResponse(), Global.TEMPLATE_DIR, "批量新增连锁门店.xls");
        return null;
    }

    @Override
    public AjaxResult matchContact(Long contactId) {

        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        Integer areaNo = fenceService.getAreaNo(null, contact);
        return AjaxResult.getOK(areaNo);
    }

    @Override
    public AjaxResult getAreaByMerchantId(Long merchantId) {

        Merchant merchant = merchantMapper.selectByMId(merchantId);
        Contact contact = new Contact();
        contact.setArea(merchant.getArea());
        contact.setCity(merchant.getCity());
        //获取对应的运营服务区域
        Integer storeNo = fenceService.getStoreNo(null, contact);
        List<Integer> areaNoList = fenceService.selectAreaNoByStoreNo(storeNo);
        return AjaxResult.getOK(areaNoList);
    }

    @Override
    public AjaxResult selectByCategory(Long mId, Integer dateNo) {
        // 订单类目top10集合
        List<CustomerAnalysisVO> categoryTOP = new ArrayList<>(10);
        // 默认展示本月信息，可切换至近三月、近半年
        LocalDateTime startTime = LocalDateTime.of(LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), 1), LocalTime.MIN);
        if(Objects.equals(dateNo,3)){
            startTime = startTime.minusDays(90);
        }else if(Objects.equals(dateNo,6)){
            startTime = startTime.minusDays(180);
        }
        // 商户订单类目top10
        List<CustomerAnalysisVO> customerTOP = merchantMapper.merchantCategoryTOP(mId, startTime);
        // 同区域同行业购买二级类目下单商户数top10
        List<CustomerAnalysisVO> peerCustomerTOP = merchantMapper.peerCategoryTOP(mId, startTime);
        categoryTOP.addAll(peerCustomerTOP);
        // 是否购买标识
        for (int i = 0; i < peerCustomerTOP.size(); i++) {
            Integer count = merchantMapper.whetherNotToBuy(mId, startTime, peerCustomerTOP.get(i).getId());
            if(null != count && count > 0){
                categoryTOP.get(i).setPurchase(true);
            }
        }
        // 商户信息
        for (int i = 0; i < customerTOP.size(); i++) {
            if(i >= categoryTOP.size()){
                categoryTOP.add(customerTOP.get(i));
            }
            categoryTOP.get(i).setTradeName(customerTOP.get(i).getTradeName());
        }
        return AjaxResult.getOK(categoryTOP);
    }

    @Override
    public AjaxResult selectBySPU(Long mId, Integer dateNo) {
        // spu类目top10
        List<CustomerAnalysisVO> spuTOP = new ArrayList<>(10);
        // 默认展示本月信息，可切换至近三月、近半年
        LocalDateTime startTime = LocalDateTime.of(LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), 1), LocalTime.MIN);
        if(Objects.equals(dateNo,3)){
            startTime = startTime.minusDays(90);
        }else if(Objects.equals(dateNo,6)){
            startTime = startTime.minusDays(180);
        }
        // 商户订单spu数量top10
        List<CustomerAnalysisVO> customerTOP = merchantMapper.merchantSPUTOP(mId, startTime);
        spuTOP.addAll(customerTOP);
        // 同区域同行业spu下单商户数top10
        List<CustomerAnalysisVO> peerCustomerTOP = merchantMapper.peerSPUTOP(mId, startTime);
        // 商户订单spu数量top10在同区域行业内单个客户下单最大量
        for (int i = 0; i < customerTOP.size(); i++) {
            CustomerAnalysisVO peerSPUMax = merchantMapper.peerSPUMax(mId, startTime, customerTOP.get(i).getId());
            spuTOP.get(i).setPeerPurchaseQuantity(peerSPUMax.getPeerPurchaseQuantity());
        }
        // 是否购买标记
        for (int i = 0; i < peerCustomerTOP.size(); i++) {
            if(i >= spuTOP.size()){
                spuTOP.add(peerCustomerTOP.get(i));
            }
            spuTOP.get(i).setPeerBrandName(peerCustomerTOP.get(i).getPeerBrandName());
            Integer count = merchantMapper.whetherNotToBuySPU(mId, startTime, peerCustomerTOP.get(i).getId());
            if(null != count && count > 0){
                spuTOP.get(i).setPurchase(true);
            }
        }
        return AjaxResult.getOK(spuTOP);
    }



    @Override
    public AjaxResult selectListPanicBuy(Long mId){
        // 查询商户所在地区的所有秒杀活动id
        List<Integer> panicIds = panicBuyMapper.selectByMid(mId);
        if(CollectionUtils.isEmpty(panicIds)){
            return AjaxResult.getOK();
        }
        // 商户所在地区所有秒杀活动中，同时在圈人中的
        List<Integer> mIdCircles = null;
        List<Integer> info = circlePeopleRuleMapper.selectActMid(panicIds,null);
        if(info.size()>0){
            //商户可参与的在圈人中的秒杀活动
            mIdCircles = circlePeopleRuleMapper.selectActMid(info, mId);
            //商户不可参与的在圈人中的秒杀活动
            info.removeAll(mIdCircles);
            //商户所在地区所有秒杀活动中，排除圈人中不可参与的，剩下均是可参与的
            panicIds.removeAll(info);
        }
        if(CollectionUtils.isEmpty(panicIds)){
            return AjaxResult.getOK();
        }
        // 获取秒杀活动信息
        List<PanicBuyVO> panicBuyVOS = panicBuyMapper.selectByPanicId(panicIds,mId);
        // 设置圈人标识
        if(!CollectionUtils.isEmpty(mIdCircles)){
            for (PanicBuyVO panicBuyVO : panicBuyVOS) {
                if(mIdCircles.contains(panicBuyVO.getId())){
                    panicBuyVO.setCirclePeople(true);
                }
            }
        }
        List<PanicBuyGroupVo> panicBuyGroupVos = new ArrayList<>();
        // 以秒杀开始时间分组，并将开始时间集合排序
        Map<LocalDateTime, List<PanicBuyVO>> panicStartTimes = panicBuyVOS.stream().collect(Collectors.groupingBy(PanicBuyVO::getStartTime));
        List<LocalDateTime> starts = new ArrayList<>(panicStartTimes.keySet());
        starts.sort(LocalDateTime::compareTo);
        // 每个开始时间组内，以秒杀结束时间分组，并将结束时间集合排序
        for (LocalDateTime start : starts) {
            List<PanicBuyVO> pbv = panicStartTimes.get(start);
            Map<LocalDateTime, List<PanicBuyVO>> panicEndTimes = pbv.stream().collect(Collectors.groupingBy(PanicBuyVO::getEndTime));
            List<LocalDateTime> ends = new ArrayList<>(panicEndTimes.keySet());
            ends.sort(LocalDateTime::compareTo);

            for (LocalDateTime end : ends) {
                PanicBuyGroupVo pbg = new PanicBuyGroupVo();
                pbg.setStartTime(start);
                pbg.setEndTime(end);
                pbg.setPanicBuyVoList(panicEndTimes.get(end));
                if(Objects.equals(panicEndTimes.get(end).get(0).getCirclePeople(),true)){
                    pbg.setCirclePeople(true);
                }
                // 此时添加顺序为，相同开始结束时间的活动在同一个集合，并按开始时间、结束时间排序后依次放入上层集合中
                panicBuyGroupVos.add(pbg);
            }
        }
        return AjaxResult.getOK(panicBuyGroupVos);
    }
    @Transactional
    public void  allInsertProcess(MerchantBatchHandlerVO itemVo) {

        Merchant merchantInsert = new Merchant();
        merchantInsert.setMname(itemVo.getMname());
        merchantInsert.setMcontact(itemVo.getmContact());
        merchantInsert.setOpenid(itemVo.getPhone());
        merchantInsert.setPhone(itemVo.getPhone());
        merchantInsert.setRegisterTime(new Date());
        merchantInsert.setInvitecode("seeegj");
        merchantInsert.setProvince(itemVo.getProvince());
        merchantInsert.setCity(itemVo.getCity());
        merchantInsert.setArea(itemVo.getArea());
        merchantInsert.setAddress(itemVo.getAddress());
        merchantInsert.setAdminId(Integer.parseInt(itemVo.getLeaderMerchant()));
        merchantInsert.setSize("大客户");

        String type = itemVo.getType();
        String stringDirect = type.substring(NumberUtils.INTEGER_ZERO,2);
        String stringShow = type.substring(NumberUtils.INTEGER_TWO, 4);
        Integer direct = Objects.equals(stringDirect,"账期") ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_TWO;
        Integer show = Objects.equals(stringShow,"定量") ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_TWO;
        boolean check = checkoutType(stringDirect, stringShow, itemVo);
        boolean skuShow = checkDirect(String.valueOf(itemVo.getLeaderMerchant()), direct, show);
        ProvinceConveter.convert(merchantInsert, "allInsertProcess");
        merchantInsert.setDirect(direct);
        merchantInsert.setSkuShow(show);
        merchantInsert.setIslock((byte)0);
        merchantMapper.insertSelective(merchantInsert);

        MerchantSubAccount merchantSubAccount = new MerchantSubAccount();
        merchantSubAccount.setMId(merchantInsert.getmId());
        merchantSubAccount.setType(0);
        merchantSubAccount.setDeleteFlag(1);
        merchantSubAccount.setStatus(0);
        merchantSubAccount.setContact(itemVo.getmContact());
        merchantSubAccount.setPhone(itemVo.getPhone());
        merchantSubAccount.setOpenid(itemVo.getPhone());
        merchantSubAccount.setRegisterTime(new Date());
        merchantSubAccountMapper.insertRecord(merchantSubAccount);

        //发票
        InvoiceMerchantRelation invoiceMerchantRelation = new InvoiceMerchantRelation();
        invoiceMerchantRelation.setMerchantId(merchantInsert.getmId());
        invoiceMerchantRelation.setInvoiceId(Long.valueOf(itemVo.getInvoiceId()));
        invoiceMerchantRelationMapper.insert(invoiceMerchantRelation);
        Contact contact = new Contact();

        Contact record = new Contact();
        record.setmId(merchantInsert.getmId());
        record.setContact(itemVo.getmContact());
        record.setPhone(itemVo.getPhone());
        record.setProvince(itemVo.getProvince());
        record.setCity(itemVo.getCity());
        record.setArea(itemVo.getArea());
        record.setAddress(itemVo.getAddress());
        record.setPoiNote(contact.getPoiNote());
        record.setStatus(1);
        record.setIsDefault(1);
        Integer storeNo = fenceService.getStoreNo(null, record);
        record.setStoreNo(storeNo);
        String address = itemVo.getProvince()+itemVo.getCity()+itemVo.getArea()+itemVo.getAddress();
        JSONObject jsonObject = GaoDeUtil.getGeoCode(address);
        if ("1".equals(jsonObject.getString("status")) && !Objects.equals(jsonObject.getInteger("count"), 0)) {
            JSONArray geocodes = jsonObject.getJSONArray("geocodes");
            JSONObject geocode = geocodes.getJSONObject(0);
            String location = geocode.getString("location");
            record.setPoiNote(location);

            // 获取联系地址与仓库的距离
            BigDecimal distance = BigDecimal.ZERO;
            distance = this.getDistance(record.getPoiNote(),storeNo,distance);
            record.setDistance(distance);
        }

        contactMapper.insertSelective(record);


        //暂时移除
        /*MerchantOuterDO insertContent = new MerchantOuterDO();
        insertContent.setmId(merchantInsert.getmId());
        insertContent.setOuterPlatformId(itemVo.getOuterPlatformId());
        insertContent.setOuterNo(itemVo.getOuterNo());
        insertContent.setRemark("外部映射导入门店编号");
        merchantOuterMapper.insert(insertContent);
        // 外部对接，向外部平台推送门店关联城市
        orderOuterInfoService.mqPushStore("storeadd",itemVo.getOuterNo(),itemVo.getOuterPlatformId(),merchantInsert.getAreaNo());
   */ }

    @Override
    public void improveStoreExamine() {
        LocalDateTime oneDayBefore = LocalDateTime.now().minusDays(1);
        List<NotExamineStoreVO> lessThanOneList = merchantMapper.selectNotExamineMerchantInOneDayList(oneDayBefore);
        List<NotExamineStoreVO> moreThanOneDayList = merchantMapper.selectNotExamineMerchantMoreThanOneDayList(oneDayBefore);
        List<NotExamineStoreVO> notExamineStoreVOList = merchantMapper.selectNotExamineMerchantList();
        String url = configMapper.selectOne(EXAMINE_STORE_ROBOT_CONFIG).getValue();
        this.sendAll(notExamineStoreVOList, url);
        this.sendPrivate(moreThanOneDayList, lessThanOneList);
    }
    /**
     * 修改免邮日到记录表
     * @param record 用户信息
     * @return 是否成功提示
     */
    @Override
    @Transactional
    public AjaxResult updateFreeday(MerchantExt record) {
        Integer adminId = getAdminId();
        MerchantExt merchantExt = merchantExtMapper.selectByMid(record.getMId());
        if (!Objects.isNull(merchantExt)) {
            merchantExt.setFreeDay(record.getFreeDay());
            merchantExt.setUpdater(getAdminName());
            merchantExt.setClickFlag(FreeDayLogEnmu.clickFlag.TRUE.ordinal());
            merchantExtMapper.updateByPrimaryKeySelective(merchantExt);
        }else{
            //插入到表中
            record.setCreator(getAdminName());
            record.setUpdater(getAdminName());
            record.setClickFlag(FreeDayLogEnmu.clickFlag.TRUE.ordinal());
            merchantExtMapper.insertSelective(record);
        }
        return AjaxResult.getOK();
    }
    /**
     * 根据地址查看围栏配送周期
     * @param contact
     * @return 配送周期
     */
    @Override
    public AjaxResult selectDeliveryFrequent(Contact contact) {
        if(StringUtils.isBlank(contact.getArea()) || StringUtils.isBlank(contact.getCity()) || StringUtils.isBlank(contact.getDeliveryFrequent())){
            return AjaxResult.getOK();
        }
        List<Contact> contacts = contactMapper.selectSimilar(contact);
        if(CollectionUtils.isEmpty(contacts)){
            return AjaxResult.getOK();
        }
        return AjaxResult.getOK(contacts.get(0).getDeliveryFrequent());
    }

/*    @Override
    public AjaxResult selectDeliveryFrequentByArea(Contact contact) {
        if(StringUtils.isBlank(contact.getArea()) || StringUtils.isBlank(contact.getCity())){
            return AjaxResult.getOK();
        }
        FenceVO fenceVO = fenceService.selectFenceByCityArea(contact.getArea(), contact.getCity());
        if (Objects.isNull(fenceVO)) {
            return AjaxResult.getOK();
        }
        return AjaxResult.getOK(fenceVO.getDeliveryFrequent());
    }*/

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateOperatingState(Long mId,Integer operateStatus){
        if(Objects.isNull(operateStatus) || Objects.isNull(mId)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT,"参数错误,请联系管理员解决");
        }
        // 设置店铺状态
        Merchant merchant = new Merchant();
        merchant.setmId(mId);
        merchant.setOperateStatus(operateStatus);
        merchant.setUpdater(getAdminId());
        merchantMapper.updateByPrimaryKeySelective(merchant);
        // 发送钉钉消息
        if(!Objects.equals(operateStatus,MerchantOperatingStateEnum.OPERATE_STATE.getId())){
            // 客户倒闭,将其流出私海
            followUpRelationService.reassign(Arrays.asList(mId),null,"客户倒闭",RELEASE_TYPE);
        }
        return AjaxResult.getOK();
    }
    /**
     * 24小时内推送到群里
     * @param notExamineStoreVOList 所有待审核店铺集合
     * @param url 钉钉群地址
     */
    private void sendAll(List<NotExamineStoreVO> notExamineStoreVOList, String url) {
        Integer total = 0;
        StringBuilder sb = new StringBuilder();
        Map<String, String> md = new HashMap<>(2);

        md.put("title", "【今日待审核店铺提醒】");
        sb.append("####【今日待审核店铺提醒】\n");
        if (!CollectionUtils.isEmpty(notExamineStoreVOList)){
            total = notExamineStoreVOList.stream().mapToInt(NotExamineStoreVO::getNotExamineStoreNum).sum();
            notExamineStoreVOList = notExamineStoreVOList.stream().sorted(Comparator.comparing(NotExamineStoreVO::getNotExamineStoreNum).reversed()).collect(Collectors.toList());
        }
        sb.append("今日待审核店铺总共 ").append(total).append(" 家\n");
        for (NotExamineStoreVO notExamineStoreVO : notExamineStoreVOList) {
            sb.append("\n #### ").append(notExamineStoreVO.getCity()).append(" : ").append(notExamineStoreVO.getNotExamineStoreNum()).append("家");
        }
        md.put("text", sb.toString());

        DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN, url, () -> md);
    }

    /**
     * 超过24小时未审核的推送到个人
     * @param moreThanOneDayList 超过24小时待审核店铺集合
     * @param lessThanOneList 24小时内待审核店铺集合
     */
    private void sendPrivate(List<NotExamineStoreVO> moreThanOneDayList, List<NotExamineStoreVO> lessThanOneList) {
        List<ManagerAreaVO> managerAreaVOS = crmManageAreaMapper.selectManagerAreaList();
        Map<Integer, List<ManagerAreaVO>> phoneAreaNoMap = managerAreaVOS.stream().collect(Collectors.groupingBy(ManagerAreaVO::getManageAdminId));
        for (Integer adminId : phoneAreaNoMap.keySet()) {
            Integer lessThan24 = 0;
            for (NotExamineStoreVO notExamineStoreVO : lessThanOneList) {
                List<ManagerAreaVO> managerAreaVOList = phoneAreaNoMap.get(adminId);
                if (!CollectionUtils.isEmpty(managerAreaVOList)){
                    List<Integer> areaNoList = managerAreaVOList.stream().map(ManagerAreaVO::getAreaNo).collect(Collectors.toList());
                    if (areaNoList.contains(notExamineStoreVO.getAreaNo())) {
                        lessThan24 += notExamineStoreVO.getNotExamineStoreNum();
                    }
                }
            }

            Integer moreThan24 = 0;
            for (NotExamineStoreVO notExamineStoreVO : moreThanOneDayList) {
                List<ManagerAreaVO> managerAreaVOList = phoneAreaNoMap.get(adminId);
                if (!CollectionUtils.isEmpty(managerAreaVOList)){
                    List<Integer> areaNoList = managerAreaVOList.stream().map(ManagerAreaVO::getAreaNo).collect(Collectors.toList());
                    if (areaNoList.contains(notExamineStoreVO.getAreaNo())) {
                        moreThan24 += notExamineStoreVO.getNotExamineStoreNum();
                    }
                }
            }
            String msg = " 您今日待审核店铺剩余 " + (lessThan24+moreThan24) + " 家，其中 " + moreThan24 + " 家待审核时间已超24小时，请尽快审核";
            DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.TXT.getType(), null, msg);
            DingTalkMsgReceiverIdBO fushuMsgBO = new DingTalkMsgReceiverIdBO(dingTalkMsgBO);
            fushuMsgBO.setReceiverIdList(Collections.singletonList(adminId.longValue()));
            dingTalkMsgSender.sendMessageWithFeiShu(fushuMsgBO);
        }
    }

    private Workbook merchantExcelTitle(){
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("*外部平台");
        title.createCell(1).setCellValue("*外部门店编码");
        title.createCell(2).setCellValue("*鲜沐门店名称");
        title.createCell(3).setCellValue("*联系人");
        title.createCell(4).setCellValue("*联系电话");
        title.createCell(5).setCellValue("*所属总部编号");
        title.createCell(6).setCellValue("*省");
        title.createCell(7).setCellValue("*市");
        title.createCell(8).setCellValue("*县");
        title.createCell(9).setCellValue("*详细地址");
        return workbook;
    }

    //处理生成数据
    private String handleHandlerVO( List<MerchantBatchHandlerVO> allList,List<Merchant>  merchants){
        Integer length = NumberUtils.INTEGER_TWO;
        //校验表中的数据是否店名或手机号重复
        HashMap<String, MerchantBatchHandlerVO> nameMap = new HashMap<>();
        HashMap<String, MerchantBatchHandlerVO> phoneMap = new HashMap<>();
        StringJoiner joiner = new StringJoiner(",");
        //循环处理数据
        for (MerchantBatchHandlerVO vo : allList) {
            String phone = StringUtils.trimToNull(vo.getPhone());
            String mname = StringUtils.trimToNull(vo.getMname());
            String adminId = StringUtils.trimToNull(vo.getAdminId());
            String type = StringUtils.trimToNull(vo.getType());
            String enterpriseScale = StringUtils.trimToNull(vo.getEnterpriseScale());
            if(StringUtils.isEmpty(type) || !Objects.equals(type.length(),4)){
                joiner.add(length.toString());
                length +=1;
                continue;
            }
            String stringDirect = type.substring(NumberUtils.INTEGER_ZERO,2);
            String stringShow = type.substring(NumberUtils.INTEGER_TWO, 4);
            Integer direct = Objects.equals(stringDirect,"账期") ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_TWO;
            Integer show = Objects.equals(stringShow,"定量") ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_TWO;
            boolean check = checkoutType(stringDirect, stringShow, vo);
            boolean skuShow = checkDirect(adminId, direct, show);
            MerchantBatchHandlerVO nameVO = nameMap.get(mname);
            MerchantBatchHandlerVO phoneVO = phoneMap.get(mname);
            //校验格式，大客户信息， 手机号，店名称重复
            if(!check || !skuShow || Objects.nonNull(nameVO)|| Objects.nonNull(phoneVO)){
                joiner.add(length.toString());
                length +=1;
                continue;
            }
            Merchant merchant = new Merchant();
            merchant.setDirect(direct);
            merchant.setSkuShow(show);
            merchant.setMerchantType("KA");
            merchant.setEnterpriseScale(enterpriseScale);
            merchant.setInvitecode("seeegj");
            merchant.setSize("大客户");
            merchant.setServer(1);
            merchant.setCompanyBrand("茶百道");
            merchant.setType("茶饮");
            merchant.setAdminId(Integer.valueOf(adminId));
            merchant.setMname(mname);
            merchant.setPhone(phone);
            merchant.setIslock(Byte.valueOf("0"));
            merchant.setPreRegisterFlag(1);
            merchants.add(merchant);
            length +=1;
            nameMap.put(mname,vo);
            phoneMap.put(phone,vo);
        }
        //获取所有格式错误的行
        if(joiner.length() > 0){
            return joiner.toString();
        }
        return null;
    }
    /**
     * 校验类型信息
     */
    private boolean checkoutType(String direct,String show ,MerchantBatchHandlerVO vo){
        //校验客户属性

        String phone = StringUtils.trimToNull(vo.getPhone());
        String mname = StringUtils.trimToNull(vo.getMname());
        String adminId = StringUtils.trimToNull(vo.getAdminId());
        if(StringUtils.isEmpty(phone) || StringUtils.isEmpty(mname) || StringUtils.isEmpty(adminId)){
            return false;
        }
        if(!Objects.equals(direct,"账期") && !Objects.equals(direct,"现结")){
            return false;
        }
        if(!Objects.equals(show,"定量") && !Objects.equals(show,"全量")){
            return false;
        }
        return !StringUtils.isEmpty(adminId);

    }


    private Workbook merchantTitleToCBD(){
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("店铺名称");
        title.createCell(1).setCellValue("联系方式");
        title.createCell(2).setCellValue("所属总部(大客户编码)");
        title.createCell(3).setCellValue("客户属性(例:账期定量)");
        return workbook;
    }

    /**
     * 校验大客户信息
     */
    private boolean checkDirect(String adminId,Integer direct,Integer show){
        Admin admin = adminMapper.selectByPrimaryKey(Integer.valueOf(adminId));
        if(Objects.isNull(admin)){
            return Boolean.FALSE;
        }
        String contractMethod = admin.getContractMethod();
        String replace = contractMethod.replace("\"", "");
        String method = direct + ":" + show;
        boolean contains = replace.contains(method);
        return  contains;
    }

    @Override
    public AjaxResult fuzzyByName(String name) {
        List<Merchant> merchantList = merchantMapper.fuzzySelectByName(name);
        return AjaxResult.getOK(merchantList);
    }

    @Override
    public AjaxResult selectByMnameSaler(String mname) {
        List<MerchantVO> merchantList = merchantMapper.selectByMnameSaler(mname);
        return AjaxResult.getOK(merchantList);
    }

    @Override
    public AjaxResult selectMerchantDetail(Long mId) {
        Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
        if (null == merchant) {
            return AjaxResult.getErrorWithMsg("门店：[" + mId + "]不存在");
        }
        MerchantVO query = new MerchantVO();
        query.setmId(mId);
        query.setIslock(merchant.getIslock());
        MerchantVO merchantVO = merchantMapper.selectDetail(query);;
        String monthPurchaseMoney  = merchantMonthPurmoneyMapper.selectByMId(merchantVO.getmId());
        Integer adminId = merchantVO.getAdminId();
        Admin admin = adminMapper.selectByPrimaryKey(adminId);
        if (Objects.nonNull(admin)) {
            merchantVO.setRealName(admin.getRealname());
            merchantVO.setContractMethod(admin.getContractMethod());
        }
        merchantVO.setMonthPurmoney(monthPurchaseMoney);
        if (StringUtils.isNotBlank(merchantVO.getInviterChannelCode())) {
            Map<String, Object> inviterSelectKeys = new HashMap<>();
            inviterSelectKeys.put("channelCode", merchantVO.getInviterChannelCode());
            Merchant inviter = merchantMapper.selectOne(inviterSelectKeys);
            if (inviter != null) {
                merchantVO.setInviterPhone(inviter.getMname() + "-" + inviter.getPhone());
            } else {
                merchantVO.setInviterPhone(merchantVO.getInviterChannelCode());
            }
        }

        //地址
        Contact select = new Contact();
        select.setStatus(1);
        select.setmId(merchantVO.getmId());
        List<Contact> contacts = contactMapper.select(select);
        for(Contact contact: contacts){
            try{
                Integer areaNo = fenceService.getAreaNo(contact.getCity(), contact.getArea());
                if(areaNo != null){
                    Area area = areaMapper.queryByAreaNo(areaNo);
                    if(area != null){
                        contact.setAreaName(area.getAreaName());
                        contact.setAreaNo(area.getAreaNo());
                    }
                }
            } catch (Exception e){
                logger.warn("meet error", e);
            }
            try {
                //获取运费规则
                DistributionRulesInfoReq distributionRulesInfoReq = new DistributionRulesInfoReq();
                distributionRulesInfoReq.setTypeId(contact.getContactId());
                distributionRulesInfoReq.setType(DistributionRulesTypeEnum.MERCHANT.getCode());
                DistributionRulesDTO info = distributionRulesFacade.getInfo(distributionRulesInfoReq);
                contact.setDistributionRulesDTO(info);
            } catch (Exception e) {
                logger.warn("MerchantServiceImpl[]selectMerchantDetail[]distributionRulesFacade[]error", e);
            }
            contact.initAddrRemark();
        }
        merchantVO.setContacts(contacts);

        // 外部对接 门店外部平台映射不为空 返回外部映射信息
        List<Long> mIdList=new ArrayList<>();
        mIdList.add(merchantVO.getmId());
        List<MerchantOuterDO> merchantOuterList=merchantOuterMapper.selectByMIdList(mIdList);
        if(!CollectionUtils.isEmpty(merchantOuterList)) {
            List<MerchantOuterVO>  merchantOuterVOs=new ArrayList<>();
            merchantOuterList.forEach(merchantOuter ->{
                MerchantOuterVO merchantOuterVO=new MerchantOuterVO();
                merchantOuterVO.setOuterPlatformId(merchantOuter.getOuterPlatformId());
                merchantOuterVO.setOuterNo(merchantOuter.getOuterNo());
                merchantOuterVOs.add(merchantOuterVO);
            });
            merchantVO.setOuterMappings(merchantOuterVOs);
        }

        // 免邮日
        MerchantExt merchantExt = merchantExtMapper.selectByMid(merchantVO.getmId());
        if(!Objects.isNull(merchantExt)){
            merchantVO.setFreeDay(merchantExt.getFreeDay());
            merchantVO.setGroupHeadFlag(merchantExt.getGroupHeadFlag());
            merchantVO.setGroupBuyAreaNo(merchantExt.getGroupBuyAreaNo());
        }

        List<DistributionRule> distributionRules = distributionRuleMapper.selectByAdminId(merchantVO.getAdminId());
        if(!CollectionUtils.isEmpty(distributionRules)){
            merchantVO.setShowFreeDay(NumberUtils.INTEGER_ZERO);
            merchantVO.setFreeDay(null);
        }

        // 线索池
        MerchantCluePool queryPool = new MerchantCluePool();
        queryPool.setMId(mId);
        MerchantCluePool merchantCluePool = merchantCluePoolMapper.queryMerchantClue(queryPool);
        if (Objects.nonNull(merchantCluePool)) {
            merchantVO.setEsId(merchantCluePool.getEsId());
            merchantVO.setClueMName(merchantCluePool.getMName());
            merchantVO.setClueAddress(merchantCluePool.getAddress());
            merchantVO.setCluePhone(merchantCluePool.getPhone());
        }

        // 商户标签
        List<String> list = this.selectMerchantAllLabelByMid(mId);
        merchantVO.setMerchantLabelList(list);

        //地推人员信息
        FollowUpRelation upRelation = followUpRelationService.getInfoByMid(merchantVO.getmId());
        if (Objects.nonNull(upRelation)) {
            merchantVO.setAdminRealName(upRelation.getReassign() ? "默认邀请码" : upRelation.getAdminName());//NOSONAR
        }
        return AjaxResult.getOK(merchantVO);
    }

    @Override
    public void sendNormalOperationMsg(DtsModel dtsModel) {
        if (dtsModel == null || dtsModel.getOld() == null) {
            logger.warn("DtsModel or its old data is null");
            return;
        }

        List<Map<String, String>> oldDataList = dtsModel.getOld();
        for (int i = 0; i < oldDataList.size(); i++) {
            String id = DtsUtils.searchChangeId(dtsModel, i, STATUS, ID);
            if (Objects.isNull(id)) {
                continue;
            }

            Map<String, String> oldDataMap = oldDataList.get(i);
            if (oldDataMap == null) {
                logger.warn("门店:{}的运营状态原状态为空，不进行发送消息", id);
                continue;
            }

            String oldOperateStatus = oldDataMap.get(STATUS);
            if (!Objects.equals(oldOperateStatus, String.valueOf(MerchantOperatingStateEnum.CLOSE_DOWN.getId()))) {
                logger.info("门店:{}运营状态原状态非已倒闭，不进行发送消息", id);
                continue;
            }

            long mId = Long.parseLong(id);
            Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
            if (merchant == null || !Objects.equals(merchant.getOperateStatus(), MerchantOperatingStateEnum.OPERATE_STATE.getId())) {
                logger.info("门店:{}运营状态非正常状态，不进行发送消息", id);
                continue;
            }

            // 查询倒闭商户对应的城市负责人
            BDExt bdExt = crmManageBdMapper.selectRealName(merchant.getAreaNo());
            Long adminId = LAST_FEISHU_ADMIN_ID;
            if (Objects.nonNull(bdExt)) {
                adminId = bdExt.getAdminId().longValue();
            }
            Integer updaterAdminId = merchant.getUpdater();
            Admin updater = adminMapper.selectByPrimaryKey(updaterAdminId);
            String updaterName = Optional.ofNullable(updater).map(Admin::getRealname).orElse("未知");
            // 拼接信息
            String title = "倒闭客户恢复经营通知";
            StringBuilder text = new StringBuilder(title);
            text.append("\n")
                .append("> ###### 商户:").append(merchant.getMname()).append("的经营状态已从倒闭变更为正常经营!").append("\n")
                .append("> ###### 变更人:").append(updaterName).append("\n")
                .append("> ###### 如有需要，请关注。");

            DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), null, title, text.toString());
            DingTalkMsgReceiverIdBO feishuMsg = new DingTalkMsgReceiverIdBO(dingTalkMsgBO);
            feishuMsg.setReceiverIdList(Collections.singletonList(adminId));
            dingTalkMsgSender.sendMessageWithFeiShu(feishuMsg);
        }
    }

    @Override
    public List<MerchantVO> getListByName(MerchantReq merchantReq) {
        List<Merchant> merchants =  merchantMapper.getListByName(merchantReq.getMName(), merchantReq.getSize());
        if (CollectionUtils.isEmpty(merchants)) {
            return Collections.emptyList();
        }
        List<MerchantVO> merchantVOS = new ArrayList<>(merchants.size());
        merchants.stream().forEach(e -> {
            MerchantVO merchantVO = new MerchantVO();
            merchantVO.setmId(e.getmId());
            merchantVO.setMname(e.getMname());
            merchantVOS.add(merchantVO);
        });
        return merchantVOS;
    }

    @Override
    @Transactional
    public AjaxResult<Boolean> updateContactDetail(ContactUpdateVO contactUpdateVO) {
        Long mid = contactUpdateVO.getMId();
        Merchant merchant = merchantMapper.selectByPrimaryKey(mid);
        if (merchant == null){
            return AjaxResult.getErrorWithMsg("店铺不存在");
        }
        List<Contact> contacts = new ArrayList<>();
        contacts.add(ContactUpdateConverter.toContact(contactUpdateVO));
        StringJoiner dingTalkMessage = changeContact(contacts, merchant);

        //发送飞书群消息
        if(StringUtils.isNotBlank(dingTalkMessage.toString())){
            contactFrequentDingTalkSend(merchant,dingTalkMessage);
        }
        return AjaxResult.getOK(true);
    }

    @Override
    @Transactional
    public CommonResult<Boolean> updateAddressRemark(ContactAddressRemarkUpdateVO contactUpdateVO) {
        ContactAddressRemark contactAddressRemark = contactUpdateVO.getContactAddressRemark();
        if (contactAddressRemark == null || contactUpdateVO.getContactId() == null) {
            return CommonResult.ok();
        }
        Long contactId = contactUpdateVO.getContactId();
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        if (contact == null){
            return CommonResult.ok(false);
        }
        Contact updateContact = new Contact();
        updateContact.setAddressRemark(JSONUtil.toJsonStr(contactAddressRemark));
        updateContact.setContactId(contactId);
        contactMapper.updateByPrimaryKeySelective(updateContact);

        ContactOperateLog contactOperateLog = new ContactOperateLog();
        contactOperateLog.setMId(contact.getmId());
        contactOperateLog.setContactId(contact.getContactId());
        contactOperateLog.setCreateTime(LocalDateTime.now());
        contactOperateLog.setUpdateTime(LocalDateTime.now());
        contactOperateLog.setContext("修改地址备注信息");
        contactOperateLog.setOperateType(ContactLogEnum.OperateType.UPDATE.ordinal());
        contactOperateLog.setOperateName(getAdminName());
        contactOperateLog.setOperateSource(ContactLogEnum.OperateSource.MANAGE.ordinal());
        contactOperateLogMapper.insertSelective(contactOperateLog);
        return CommonResult.ok(true);
    }

    @Override
    public CommonResult<PageInfo<ContactOperateLog>> queryContactLog(ContactQueryVO queryVO) {
        PageHelper.startPage(queryVO.getPageIndex(), queryVO.getPageSize());
        List<ContactOperateLog> contactOperateLogs = contactOperateLogMapper.selectByContactId(queryVO.getContactId());
        return CommonResult.ok(PageInfoHelper.createPageInfo(contactOperateLogs));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMerchantAreaBatch(String province, String city, List<String> fenceAreas, Integer newAeaNo) {
        for (String area : fenceAreas) {
            merchantMapper.updateMerchantArea(province, city, area, newAeaNo, MerchantEnum.BusinessLineEnum.XM.getCode());
        }
    }

    @Override
    public void urgeAudit(Long mId) {
        if (mId == null) {
            logger.info("催审消息参数缺失");
            return;
        }
        Merchant merchant = merchantMapper.selectByMId(mId);
        if (merchant == null) {
            logger.info("催审未查询到门店信息, mid:{}", mId);
            return;
        }
        if (!Objects.equals(merchant.getOperateStatus(), MerchantOperatingStateEnum.PENDING_VERIFICATION.getId())) {
            logger.info("门店:{}当前状态:{}, 非待核验, 无需催审", mId, merchant.getOperateStatus());
            return;
        }
        String storeName = merchant.getMname();
        Date registerTime = merchant.getRegisterTime();
        String registerTimeStr = DateUtil.format(registerTime, "yyyyMMdd");
        Long adminId = crmBdOrgMapper.getAdminIdByCityAndAreaAndRank(merchant.getCity(), merchant.getArea(), 3);
        if (adminId == null) {
            logger.error("未查询到门店所在城市区域的BD, mid:{}, city:{}, area:{}", mId, merchant.getCity(), merchant.getArea());
            return;
        }
        String title = "加急处理-新注册门店核验";
        StringBuilder text = new StringBuilder();
        text.append("\n");
        text.append("> ###### 标题：").append(title).append("\n");
        text.append("> ###### 门店ID：").append(mId).append("\n");
        text.append("> ###### 门店名称：").append(storeName).append("\n");
        text.append("> ###### 注册时间：").append(registerTimeStr).append("\n");
        DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), null, title, text.toString());
        DingTalkMsgReceiverIdBO feishuMessage = new DingTalkMsgReceiverIdBO(dingTalkMsgBO);
        feishuMessage.setReceiverIdList(Collections.singletonList(adminId));
        dingTalkMsgSender.sendMessageWithFeiShu(feishuMessage);
    }


    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult merchantInfoVerify(int id, MerchantVO merchantVO){
        logger.info("开始门店信息核验：id：{}, merchantVO:{}", id, JSON.toJSON(merchantVO));
        //状态值合理判断，0通过，1未审核，2审核不通过
        if (id < 0 && (merchantVO.getState() != 0 || merchantVO.getState() != 2)) {
            logger.error("核验状态异常");
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        // 审核通过  or  审核拒绝
        boolean approved = merchantVO.getState() == 0;

        //判断id对应用户是否存在
        Merchant merchant = merchantMapper.selectByPrimaryKey((long) id);
        if (merchant == null) {
            logger.error("门店不存在");
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        //只有待核验状态才能被审核
        if (merchant.getOperateStatus() != 3 && merchant.getOperateStatus() != 2) {
            logger.error("当前门店不是可核验的状态");
            return AjaxResult.getError(ResultConstant.REQUEST_DONE);
        }

        if(!StringUtils.isEmpty(merchantVO.getEsId())){
            int amount = merchantCluePoolMapper.queryEsIdNumber(merchantVO.getEsId());
            if(amount > 0){
                return AjaxResult.getErrorWithMsg("当前线索已经被其他用户绑定");
            }
        }
        if (merchantVO.getMname() == null || "".equals(merchantVO.getMname())) {
            return AjaxResult.getErrorWithMsg("商铺名不能为空");
        } else if (!merchantVO.getMname().equals(merchant.getMname())) {
            HashMap selectKey = new HashMap();
            selectKey.put("mname", merchantVO.getMname());
            Merchant result = merchantMapper.selectOne(selectKey);
            if (result != null) {
                return AjaxResult.getErrorWithMsg("该商铺名已存在");
            }
        }
        if (merchantVO.getMcontact() == null || "".equals(merchantVO.getMcontact())) {
            return AjaxResult.getErrorWithMsg("联系人不能为空");
        }

        // 验证新增字段
        AjaxResult validationResult = validateNewFields(merchantVO);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }
        if (merchantVO.getPhone() == null || "".equals(merchantVO.getPhone())) {
            return AjaxResult.getErrorWithMsg("手机号不能为空");
        } else if (!merchantVO.getPhone().equals(merchant.getPhone())) {
            HashMap selectKey = new HashMap();
            selectKey.put("phone", merchantVO.getPhone());
            Merchant result = merchantMapper.selectOne(selectKey);
            if (result != null) {
                return AjaxResult.getErrorWithMsg("该手机号已被注册");
            }
        }
        Pattern phonePattern = ManagerConstant.PHONE_PATTERN;
        if (!phonePattern.matcher(merchantVO.getPhone()).find()) {
            return AjaxResult.getErrorWithMsg("手机号格式不正确");
        }

        if (approved && StringUtils.isBlank(merchantVO.getPoiNote())) {
            throw new DefaultServiceException("请先选择poi地址");
        }

        // 审核不通过需要校验当前是否存在未完结订单
        if(!approved) {
            MerchantCancelInputReq merchantCancelInputReq = new MerchantCancelInputReq();
            merchantCancelInputReq.setMId(merchant.getmId());
            List<String> check = merchantCancelFacade.check(merchantCancelInputReq);
            if(CollUtil.isNotEmpty(check)) {
                logger.error("门店{}暂不允许审核拒绝。校验结果：{}", merchant.getmId(), JSON.toJSONString(check));
                return AjaxResult.getError(ManagerConstant.EXIST_UNFINISHED_ORDERS, "当前门店有未完结的订单及售后单，不可直拒绝\n" + "请先检查相关单据，如有需要，可联系客服进行处理");
            }
        }


        Integer oldAdminId = merchant.getAdminId();

        logger.info("商户:{},{}审核，审核人:{}", merchant.getMname(), approved ? "通过" : "未通过", this.getAdminName());
        ReflectUtils.copyData(merchant, merchantVO, "phone", "mname", "mcontact", "remark", "areaNo", "size", "adminId", "direct", "skuShow", "poiNote", "showPrice", "mainBusinessType", "sideBusinessType", "merchantChainType");

        if(approved) {
            logger.info("核验通过后，大客户转单店。mid：{}", merchant.getmId());
            Merchant record = new Merchant();
            record.setmId(merchant.getmId());
            record.setDirect(null);
            record.setAdminId(null);
            record.setSkuShow(null);
            record.setSize("单店");
            record.setRemark(merchantVO.getRemark());
            record.setAuditUser(this.getAdminId());
            merchantMapper.updateInfo(record);
        }
        merchant.setAuditUser(this.getAdminId());
        Date now = new Date();
        merchant.setAuditTime(now);
        merchant.setCluePool(merchantVO.getCluePool());
        if (StringUtils.isNotBlank(merchantVO.getAddress())) { //修改注册地址
            merchant.setAddress(merchantVO.getAddress());
        }
        if (StringUtils.isNotBlank(merchantVO.getDoorPic())) {
            merchant.setDoorPic(merchantVO.getDoorPic());
        }
        merchant.setHouseNumber(merchantVO.getHouseNumber());
        merchant.setEnterpriseScale(merchantVO.getEnterpriseScale());
        merchant.setType(merchantVO.getType());
        merchant.setCompanyBrand(merchantVO.getCompanyBrand());
        merchant.setMerchantType(merchantVO.getMerchantType());
        merchant.setExamineType(merchantVO.getExamineType());
        merchant.setRemark(merchantVO.getRemark());
        // 审核通过后默认转单店
        if(approved) {
            merchant.setDirect(null);
            merchant.setAdminId(null);
            merchant.setSkuShow(null);
            merchant.setGrade(memberService.getMemberGrade(merchant.getmId(), merchant.getAreaNo()));
            merchant.setSize("单店");
            merchant.setIslock(Byte.valueOf("0"));
            merchant.setOperateStatus(0);
        } else {
            merchant.setIslock(Byte.valueOf("2"));
            merchant.setOperateStatus(4);
        }

        logger.info("开始更新门店信息，merchant:{}", JSON.toJSONString(merchant));
        merchantMapper.updateByPrimaryKeySelective(merchant);


        // 审核通过,调整contact 联系人信息
        if (approved) {
            Contact contact;
            List<Contact> contacts = contactMapper.selectAllByMId(merchant.getmId());
            if(CollUtil.isNotEmpty(contacts)) {
                contact = contacts.get(0);
                buildContact(contact, merchant, merchantVO);
                contactMapper.updateByPrimaryKeySelective(contact);
            } else {
                contact = new Contact();
                buildContact(contact, merchant, merchantVO);
                contactMapper.insertSelective(contact);
            }
        }

        // 配送单
        if (merchantVO.getPrintOutTMSConfig()!=null){
            updateTmsConfig(merchant.getmId(), merchantVO.getPrintOutTMSConfig());
        }

        // esID暂无 不处理数据
        if(!StringUtils.isEmpty(merchantVO.getEsId())){
            MerchantCluePool merchantCluePool = new MerchantCluePool(merchantVO);
            merchantCluePool.setMId(merchant.getmId());
            //处理数据 更新关联关系
            MerchantCluePool query = new MerchantCluePool();
            query.setMId(merchant.getmId());
            MerchantCluePool result = merchantCluePoolMapper.queryMerchantClue(query);
            if(result == null){
                updateCurlPool(merchantCluePool,MerchantCluePool.BANDING,null);
            }
        }

        logger.info("门店核验完成，开始后置处理。门店id {}",merchant.getmId());
        // 核验通过后置处理
        if (approved) {
            this.handleOrderSizeForAuditSuccess(merchant, oldAdminId);
            this.sendMessageForAuditSuccess(merchant.getmId());
            this.sendCouponForAuditSuccess(merchant, merchant.getAreaNo());
        }
        return AjaxResult.getOK(merchant);
    }


    private void handleOrderSizeForAuditSuccess(Merchant merchant, Integer oldAdminId){
        logger.info("审核通过,开始处理客户订单的客户标记. merchant:{}", JSON.toJSONString(merchant));
        if(!dynamicConfig.getNoAuditMerchantDefaultAdminId().equals(oldAdminId)) {
            logger.info("非免审大客户，无需处理。oldAdminId：{}", oldAdminId);
            return;
        }

        // order.msize: 大客户---》单店
        int count = ordersMapper.updateOrderSizeForAuditSuccess(merchant.getmId(), oldAdminId);
        logger.info("客户订单的客户标记处理完成. count:{}", count);
    }

    private void sendMessageForAuditSuccess(Long mId){
        try {
            asyncTaskService.sendMessage(mId);
        } catch (Exception e) {
            logger.error("审核通过后，消息发送失败", e);
        }
    }

    @Override
    public AjaxResult rebackVerify(int id) {
        Merchant merchant = merchantMapper.selectByPrimaryKey(Long.valueOf(id));
        if (merchant == null || merchant.getOperateStatus() != 4) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        merchant.setAuditUser(getAdminId());
        merchant.setSize("大客户");
        merchant.setAdminId(dynamicConfig.getNoAuditMerchantDefaultAdminId());
        merchantMapper.rebackReviewNew(merchant);
        return AjaxResult.getOK();
    }

}
