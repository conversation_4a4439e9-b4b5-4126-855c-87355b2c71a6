package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PriceAdjustmentRuleArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PriceAdjustmentRuleAreaMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PriceAdjustmentRuleArea record);

    int insertSelective(PriceAdjustmentRuleArea record);

    PriceAdjustmentRuleArea selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PriceAdjustmentRuleArea record);

    int updateByPrimaryKey(PriceAdjustmentRuleArea record);

    List<Integer> selectByFieldName(@Param("fieldName") String fieldName);

    void insertByFieldName(@Param("info") List<Integer> info, @Param("fieldName") String fieldName, @Param("adminId") Integer adminId);

    void deleteAll();

    /**
     * 查询是否开启波动策略
     * @param areaNo 城市编号
     * @return t、f
     */
    boolean queryAreaIsOpen(@Param("areaNo") Integer areaNo);
}