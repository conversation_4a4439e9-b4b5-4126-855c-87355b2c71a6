package net.summerfarm.mq.business.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import net.summerfarm.common.redis.KeyConstant;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.AdminAuthExtendEnum;
import net.summerfarm.enums.SaleStockChangeTypeEnum;
import net.summerfarm.facade.tms.TmsDeliveryRuleFacade;
import net.summerfarm.facade.tms.input.DeliveryRuleQueryInput;
import net.summerfarm.facade.wms.AreaStoreFacade;
import net.summerfarm.facade.wms.dto.*;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.model.domain.*;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.mq.business.Business;
import net.summerfarm.mq.constant.MessageBusiness;
import net.summerfarm.mq.constant.MessageType;
import net.summerfarm.mq.crm.constant.MessageKeyEnum;
import net.summerfarm.service.AreaStoreService;
import net.summerfarm.service.QuantityChangeRecordService;
import net.summerfarm.service.SampleApplyService;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/18 19:56
 */
@Service
public class StockHandler implements Business {

    private static final Logger logger = LoggerFactory.getLogger(StockHandler.class);

    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    SampleSkuMapper sampleSkuMapper;
    @Resource
    ContactMapper contactMapper;
    @Resource
    RedisTemplate<String, String> redisTemplate;
    @Resource
    SampleApplyMapper sampleApplyMapper;
    @Resource
    private SampleApplyService sampleApplyService;
    @Resource
    MerchantMapper merchantMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private TmsDeliveryRuleFacade tmsDeliveryRuleFacade;
    @Resource
    private AreaStoreFacade areaStoreFacade;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void consumptionMessage(JSONObject jsonObject,String messageType) {
        if(MessageType.FROZEN_INVENTORY.equals(messageType)){
            this.frozenInventory(jsonObject);
        }else if(MessageType.UNFREEZE_INVENTORY.equals(messageType)){
            this.unFrozenInventory(jsonObject);
        }else {
            logger.error("没有识别到的消息类型:{}",messageType);
        }
    }

    /**
     * 样品解冻库存
     * @param jsonObject 需要解冻的库存
     */
    private void unFrozenInventory(JSONObject jsonObject) {
        Integer sampleId = Optional.ofNullable(jsonObject.getInteger("sampleId")).orElse(NumberUtils.INTEGER_ZERO);
        Integer contactId = Optional.ofNullable(jsonObject.getInteger("contactId")).orElse(NumberUtils.INTEGER_ZERO);
        // 幂等判断
        String redisKey = KeyConstant.SAMPLE_APPLY_REVIEW_UN_FROZEN_INVENTORY + sampleId;
        Boolean hasKey = redisTemplate.hasKey(redisKey);
        if(Objects.equals(Boolean.TRUE,hasKey)){
            logger.info("样品正在解冻中,请勿重复执行,时间:{},redisKey:{}", LocalDateTime.now(), redisKey);
            return;
        }

        List<SampleSku> sampleSkuList = sampleSkuMapper.selectBySampleId(sampleId);
        Contact contact = contactMapper.selectByPrimaryKey(Long.valueOf(contactId));

        // 恢复库存信息-替代老逻辑
        /*Contact contact = contactMapper.selectByPrimaryKey(Long.valueOf(contactId));
        Integer storeNo = contact.getStoreNo();
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        for (SampleSku sampleSku : sampleSkuList) {
            String sku = sampleSku.getSku();
            //加虚拟库存
            areaStoreService.updateOnlineStockByStoreNo(true, sampleSku.getAmount(), sampleSku.getSku(), storeNo,
                    SaleStockChangeTypeEnum.DEMO_CANCEL,null, recordMap,NumberUtils.INTEGER_ZERO);
            //减锁定库存
            areaStoreService.updateLockStockByStoreNo(-sampleSku.getAmount(), sampleSku.getSku(), storeNo,
                    SaleStockChangeTypeEnum.DEMO_CANCEL,null, recordMap);
            WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
            if(!Objects.isNull(mapping)){
                Integer warehouseNo = mapping.getWarehouseNo();
                areaStoreService.updateAreaStoreStatus(warehouseNo, sampleSku.getSku());
            }
        }
        quantityChangeRecordService.insertRecord(recordMap);*/

        //库存释放 新模型
        AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
        areaStoreUnLockReq.setContactId(Long.valueOf(contactId));
        areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.DEMO_CANCEL.getTypeName());
        areaStoreUnLockReq.setOrderNo(String.valueOf(sampleId));
        areaStoreUnLockReq.setIdempotentNo(String.valueOf(sampleId));
        areaStoreUnLockReq.setOperatorNo(String.valueOf(sampleId));
        areaStoreUnLockReq.setMerchantId(contact.getmId());
        areaStoreUnLockReq.setSource(SourceEnum.XM_SAMPLE_APPLY.getValue());
        List<OrderUnLockSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS = new ArrayList<>(sampleSkuList.size());
        sampleSkuList.stream().forEach(e -> {
            OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
            orderUnLockSkuDetailReqDTO.setSkuCode(e.getSku());
            orderUnLockSkuDetailReqDTO.setReleaseQuantity(e.getAmount());
            orderReleaseSkuDetailReqDTOS.add(orderUnLockSkuDetailReqDTO);
        });
        areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(orderReleaseSkuDetailReqDTOS);
        areaStoreFacade.storeUnLock(areaStoreUnLockReq);
        redisTemplate.opsForValue().set(redisKey,sampleId.toString(),3, TimeUnit.DAYS);
    }

    /**
     * 样品申请冻结库存
     * @param jsonObject 需要冻结的库存
     */
    private void frozenInventory(JSONObject jsonObject) {
        SampleApplyReviewDTO sampleApplyReviewDTO = jsonObject.getObject("sampleApplyReviewDTO", SampleApplyReviewDTO.class);
        Integer sampleId = sampleApplyReviewDTO.getSampleId();
        // 幂等判断
        String redisKey = KeyConstant.SAMPLE_APPLY_REVIEW_FROZEN_INVENTORY + sampleId;
        Boolean hasKey = redisTemplate.hasKey(redisKey);
        if(Objects.equals(Boolean.TRUE,hasKey)){
            logger.info("样品冻结库存中,请勿重复执行,时间:{},redisKey:{}", LocalDateTime.now(), redisKey);
            return;
        }

        Long contactId = Long.valueOf(sampleApplyReviewDTO.getContactId());
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        Integer storeNo = contact.getStoreNo();

        List<SampleSku> sampleSkuList = sampleApplyReviewDTO.getSampleSkuList();

        // key:storeNo  value:stockTaskItem(出入库任务)--去掉老的库存扣减逻辑
        /*HashMap<Integer, List<StockTaskItem>> map = new HashMap<>(16);
        HashMap<String, Integer> warehouseSkuMap = new HashMap<>(16);
        sampleSkuList.forEach(sampleSku -> {
            String sku = sampleSku.getSku();
            //获取库存使用仓
            WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
            //出入库任务
            StockTaskItem stockTaskItem = new StockTaskItem();
            stockTaskItem.setQuantity(sampleSku.getAmount());
            stockTaskItem.setSku(sampleSku.getSku());
            warehouseSkuMap.put(sku,mapping.getWarehouseNo());
            // 相同仓库的存在同一个key内
            map.merge(storeNo, Lists.newArrayList(stockTaskItem), ListUtils::union);
        });

        Map<String, QuantityChangeRecord> recordMap = new HashMap<>(16);
        try {
            map.forEach((x,taskItems) ->{
                taskItems.forEach(taskItem -> {
                    //减虚拟库存
                    areaStoreService.updateOnlineStockByStoreNo(true, -taskItem.getQuantity(), taskItem.getSku(), x,
                            SaleStockChangeTypeEnum.DEMO_OUT,null, recordMap, NumberUtils.INTEGER_ZERO);
                    //加锁定库存
                    areaStoreService.updateLockStockByStoreNo(taskItem.getQuantity(), taskItem.getSku(), x,
                            SaleStockChangeTypeEnum.DEMO_OUT,null, recordMap);
                    Integer warehouseNo = warehouseSkuMap.get(x);
                    areaStoreService.updateAreaStoreStatus(warehouseNo, taskItem.getSku());
                });
            });
            quantityChangeRecordService.insertRecord(recordMap);
        } catch (Exception e) {
            logger.error("{}样品申请冻结库存异常:{}", sampleApplyReviewDTO.getSampleId(),Global.collectExceptionStackMsg(e));
            // 样品申请审核不通过并发送钉钉消息
            closeSampleApplyAndSendMessage(sampleApplyReviewDTO.getSampleId());
            return;
        }finally {
            redisTemplate.opsForValue().set(redisKey,sampleApplyReviewDTO.getSampleId().toString(),NumberUtils.INTEGER_ONE, TimeUnit.DAYS);
        }*/
        AreaStoreLockRes areaStoreLockRes = null;
        //库存扣减 改用新模型
        try {
            AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
            storeLockReq.setOrderNo(String.valueOf(sampleId));
            storeLockReq.setOperatorNo(String.valueOf(sampleId));
            storeLockReq.setIdempotentNo(String.valueOf(sampleId));
            storeLockReq.setOrderType(SaleStockChangeTypeEnum.DEMO_OUT.getTypeName());
            storeLockReq.setContactId(contactId);
            storeLockReq.setMerchantId(contact.getmId());
            storeLockReq.setSource(SourceEnum.XM_SAMPLE_APPLY.getValue());
            List<OrderLockSkuDetailReqDTO> orderLockSkuDetailReqDTOS = new ArrayList<>();
            sampleSkuList.stream().forEach(e -> {
                OrderLockSkuDetailReqDTO orderLockSkuDetailReqDTO = new OrderLockSkuDetailReqDTO();
                orderLockSkuDetailReqDTO.setSkuCode(e.getSku());
                orderLockSkuDetailReqDTO.setOccupyQuantity(e.getAmount());
                orderLockSkuDetailReqDTOS.add(orderLockSkuDetailReqDTO);
            });
            storeLockReq.setOrderLockSkuDetailReqDTOS(orderLockSkuDetailReqDTOS);
            areaStoreLockRes = areaStoreFacade.storeLockWithOutException(storeLockReq);
        } catch (Exception e) {
            logger.error("{}样品申请冻结库存异常:{}", sampleApplyReviewDTO.getSampleId(), Global.collectExceptionStackMsg(e));
        } finally {
            redisTemplate.opsForValue().set(redisKey, sampleApplyReviewDTO.getSampleId().toString(), NumberUtils.INTEGER_ONE, TimeUnit.DAYS);
        }

        //库存不足封装不足sku明细到审核备注里面
        if (ObjectUtils.isEmpty(areaStoreLockRes)){
            logger.error("样品冻结库存异常:{}", sampleApplyReviewDTO.getSampleId());
            SampleApply notLockSampleApply = new SampleApply();
            notLockSampleApply.setSampleId(sampleId);
            notLockSampleApply.setRemark("样品冻结库存异常");
            sampleApplyMapper.updateSampleApply(notLockSampleApply);
            // 样品申请审核不通过并发送钉钉消息
            closeSampleApplyAndSendMessage(sampleApplyReviewDTO.getSampleId());
            return;
        }
        if (!CollectionUtils.isEmpty(areaStoreLockRes.getOrderNotLockSkuDetailResDTOList())){//NOSONAR
            SampleApply notLockSampleApply = new SampleApply();
            notLockSampleApply.setSampleId(sampleId);
            StringBuffer sb = new StringBuffer();
            areaStoreLockRes.getOrderNotLockSkuDetailResDTOList().forEach(e -> {
                sb.append(e.getSkuCode()).append("库存不足，最大可用库存为:").append(e.getMaxOccupyQuantity()).append("   ");
            });
            notLockSampleApply.setRemark(sb.toString());
            sampleApplyMapper.updateSampleApply(notLockSampleApply);
            // 样品申请审核不通过并发送钉钉消息
            closeSampleApplyAndSendMessage(sampleApplyReviewDTO.getSampleId());
            return;
        }

        // 更新配送时间
        Merchant merchant = merchantMapper.selectByPrimaryKey(sampleApplyReviewDTO.getMId());
        //LocalDate deliveryDate = sampleApplyService.getDeliveryDate(new Area(),merchant,null,contactId);
        logger.info("样品申请调用tms接口报文为:{}",JSON.toJSONString(DeliveryRuleQueryInput.builder()
                .source(SourceEnum.XM_SAMPLE_APPLY)
                .orderTime(LocalDateTime.now())
                .merchantId(merchant.getmId())
                .contactId(contactId)
                .build()));
        LocalDate deliveryDate = tmsDeliveryRuleFacade.queryCloudDeliveryDate(DeliveryRuleQueryInput.builder()
                .source(SourceEnum.XM_SAMPLE_APPLY)
                .orderTime(LocalDateTime.now())
                .merchantId(merchant.getmId())
                .contactId(contactId)
                .build());
        SampleApply sampleApply = new SampleApply();
        sampleApply.setStoreNo(storeNo);
        sampleApply.setDeliveryTime(deliveryDate);
        sampleApply.setSampleId(sampleApplyReviewDTO.getSampleId());
        this.sendUpdateDeliveryDate(sampleApply);


    }

    /**
     * 扣减库存成，设置样品配送时间
     */
    private void sendUpdateDeliveryDate(SampleApply sampleApply) {
        // 更新配送时间
        JSONObject msgJson = new JSONObject();
        msgJson.put("sampleApply", sampleApply);
        String msg = msgJson.toJSONString();
        // 发送消息
        MQData mqData = new MQData();
        mqData.setType(MessageType.SAMPLE_UPDATE_DELIVERY_TIME);
        mqData.setBusiness(MessageBusiness.SAMPLE);
        mqData.setData(msg);
        logger.info("发送更新样品配送时间消息至crm服务:{}",JSON.toJSONString(mqData));
        mqProducer.send(RocketMqMessageConstant.CRM_LIST,null , MessageKeyEnum.DELIVERY_DATE_UPDATE_MESSAGE.getKey(), JSON.toJSONString(mqData));
    }

    private void closeSampleApplyAndSendMessage(Integer sampleId){
        // 投递回滚消息至CRM服务
        JSONObject msgJson = new JSONObject();
        msgJson.put("sampleId", String.valueOf(sampleId));
        String msg = msgJson.toJSONString();
        // 发送消息
        MQData mqData = new MQData();
        mqData.setType(MessageType.SAMPLE_ROLLBACK);
        mqData.setBusiness(MessageBusiness.SAMPLE);
        mqData.setData(msg);
        logger.info("发送样品申请失败消息至crm服务:{}",JSON.toJSONString(mqData));
        mqProducer.send(RocketMqMessageConstant.CRM_LIST,null,MessageKeyEnum.CLOSE_SAMPLE_APPLY_MESSAGE.getKey(), JSON.toJSONString(mqData));

        // 发送钉钉消息至发起人
        SampleApply sampleApplyInfo = sampleApplyMapper.selectSampleById(sampleId);
        if(Objects.isNull(sampleApplyInfo) || Objects.isNull(sampleApplyInfo.getCreateId())){
            return;
        }
        String title = "样品无库存,审核失败";
        String text = "#### " +
                title +
                "\n" +
                "> " +
                "##### " +
                "门店名称:" + sampleApplyInfo.getMName() + "\n\n" +
                "> " +
                "##### " +
                "样品申请ID:" + sampleId + "\n\n" +
                "##### " +
                "您提交的样品申请无法通过审核,已被系统关闭,请确认";
        DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), null
                , title, text);

        DingTalkMsgReceiverIdBO dingTalkMsgReceiverIdBO = new DingTalkMsgReceiverIdBO(dingTalkMsgBO);
        dingTalkMsgReceiverIdBO.setReceiverIdList(Collections.singletonList(sampleApplyInfo.getCreateId().longValue()));
        dingTalkMsgSender.sendMessageWithFeiShu(dingTalkMsgReceiverIdBO);
    }
}
