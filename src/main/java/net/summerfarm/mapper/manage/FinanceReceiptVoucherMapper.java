package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FinanceReceiptVoucher;

import java.util.List;

/**
 * 应收-收款单-收款凭证mapper
 */
public interface FinanceReceiptVoucherMapper {

    /**
     * 根据id删除收款凭证
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 保存收款凭证
     * @param record
     * @return
     */
    int insert(FinanceReceiptVoucher record);

    int insertBatch(List<FinanceReceiptVoucher> receiptVoucherList);

    /**
     * 保存收款凭证
     * @param record
     * @return
     */
    int insertSelective(FinanceReceiptVoucher record);

    /**
     * 根据id查询收款凭证
     * @param id
     * @return
     */
    FinanceReceiptVoucher selectByPrimaryKey(Long id);

    /**
     * 更新收款凭证
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(FinanceReceiptVoucher record);

    /**
     * 更新收款凭证
     * @param record
     * @return
     */
    int updateByPrimaryKey(FinanceReceiptVoucher record);


    /**
     * 根据收款单id查询收款凭证
     * @param financeReceiptId
     * @return
     */
    List<FinanceReceiptVoucher> selectByReceiptIdList(Long financeReceiptId);

}