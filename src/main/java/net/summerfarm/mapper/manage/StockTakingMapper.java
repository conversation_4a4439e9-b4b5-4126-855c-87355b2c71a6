package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.StockTaking;
import net.summerfarm.model.domain.StockTakingListDetail;
import net.summerfarm.model.vo.StockTakingVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wjd on 2017/9/27.
 */
@Repository
public interface StockTakingMapper {

    @RequiresDataPermission(originalField = "st.area_no")
    List<StockTakingVO> select(StockTakingVO stockTakingVO);

    int insert(StockTaking stockTaking);

    void updateRemark(@Param("takingId") int takingId, @Param("remark") String remark);

    StockTaking selectById(Integer takingId);

    StockTaking selectOne(String stockTakingNo);

    //查询盘点中的sku
    List<StockTakingListDetail> selectTakingSku(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    StockTaking selectByTakingNo(String stockTakingNo);

    int updateByPrimary(StockTaking stockTaking);

    StockTaking selectBySku(Integer areaNo, String sku);

    //根据任务编号修改盘点单
    void updateByTaskNo(StockTaking stockTaking);
}
