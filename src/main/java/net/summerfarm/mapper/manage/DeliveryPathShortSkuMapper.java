package net.summerfarm.mapper.manage;

/**
 * <AUTHOR> ct
 * create at:  2020/7/14  16:36
 */

import net.summerfarm.model.domain.DeliveryPathShortSku;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface DeliveryPathShortSkuMapper {

     List<DeliveryPathShortSku> selectByDeliveryPathId(Integer deliveryPathId);

    DeliveryPathShortSku selectByDeliveryPath(DeliveryPathShortSku deliveryPathShortSku);

    List<DeliveryPathShortSku> getDataByDPIdAndSku(@Param("deliveryPathId") Integer deliveryPathId,@Param("sku") String sku);

    int insertBatch(List<DeliveryPathShortSku> list);
}
