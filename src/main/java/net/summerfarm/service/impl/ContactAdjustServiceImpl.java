package net.summerfarm.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.BeanCopyUtil;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.CommonStatus;
import net.summerfarm.enums.ContactAdjustStatusEnum;
import net.summerfarm.enums.bms.ContactLogEnum;
import net.summerfarm.facade.wnc.ContactDeliveryRuleQueryFacade;
import net.summerfarm.facade.wnc.DeliveryFenceQueryFacade;
import net.summerfarm.mapper.ContactOperateLogMapper;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.ContactAdjustMapper;
import net.summerfarm.mapper.manage.ContactMapper;
import net.summerfarm.mapper.manage.ContactRecordMapper;
import net.summerfarm.mapper.manage.MerchantMapper;
import net.summerfarm.model.DTO.merchant.ContactAddressRemark;
import net.summerfarm.model.domain.Area;
import net.summerfarm.model.domain.Contact;
import net.summerfarm.model.domain.ContactRecord;
import net.summerfarm.model.domain.Merchant;
import net.summerfarm.model.domain.crm.ContactOperateLog;
import net.summerfarm.model.vo.ContactAdjustVO;
import net.summerfarm.model.vo.ContactVO;
import net.summerfarm.model.vo.MerchantVO;
import net.summerfarm.service.ContactAdjustService;
import net.summerfarm.service.FenceService;
import net.summerfarm.service.MerchantService;
import net.summerfarm.service.contact.ContactService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> ct
 * create at:  2021/6/28  15:16
 */
@Slf4j
@Service
public class ContactAdjustServiceImpl extends BaseService implements ContactAdjustService {

    @Resource
    ContactAdjustMapper contactAdjustMapper;

    @Resource
    ContactMapper contactMapper;

    @Resource
    ContactRecordMapper contactRecordMapper;

    @Resource
    AreaMapper areaMapper;

    @Resource
    private FenceService fenceService;
    @Resource
    private ContactOperateLogMapper contactOperateLogMapper;
    @Resource
    private ContactDeliveryRuleQueryFacade contactDeliveryRuleFacade;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private DynamicConfig dynamicConfig;
    @Resource
    private MerchantService merchantService;
    @Resource
    private ContactService contactService;
    @Resource
    private DeliveryFenceQueryFacade deliveryFenceQueryFacade;


    @Override
    public AjaxResult selectList(Integer pageIndex,Integer pageSize, MerchantVO merchantVO) {

        PageHelper.startPage(pageIndex, pageSize);

        List<MerchantVO> merchantVOS = contactAdjustMapper.selectContactAdjustList(merchantVO);

        return AjaxResult.getOK(PageInfoHelper.createPageInfo(merchantVOS));
    }

    @Override
    public AjaxResult selectContactAdjust(Integer contactAdjustId) {
        ContactAdjustVO contactAdjustVO = contactAdjustMapper.selectContactAdjustDetail(contactAdjustId);
        if(Objects.isNull(contactAdjustVO)){
            return AjaxResult.getErrorWithMsg("调整记录不存在");
        }
        ContactAddressRemark contactAddressRemark = JSONUtil.toBean(contactAdjustVO.getAddressRemark(), ContactAddressRemark.class);
        contactAdjustVO.setContactAddressRemark(contactAddressRemark);

        Integer originAreaNo = contactAdjustVO.getAreaNo();
        Area resultArea = areaMapper.queryByAreaNo(originAreaNo);
        resultArea = Optional.ofNullable(resultArea).orElse(new Area());
        contactAdjustVO.setAreaName(resultArea.getAreaName());
        try{
            Integer newAreaId = fenceService.getAreaNo(contactAdjustVO.getNewCity(), contactAdjustVO.getNewArea());
            if(newAreaId != null){
                Area newArea = areaMapper.queryByAreaNo(newAreaId);
                if(newArea != null){
                    contactAdjustVO.setNewAreaName(newArea.getAreaName());
                    contactAdjustVO.setNewAreaNo(newArea.getAreaNo());
                }
            }
        }catch (Exception e){
            log.error("获取地址所属围栏区域异常:{}", Global.collectExceptionStackMsg(e));
        }
        return AjaxResult.getOK(contactAdjustVO);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public AjaxResult handleContactAdjust(ContactAdjustVO contactAdjust) {

        Integer handleType = contactAdjust.getHandleType();
        Integer id = contactAdjust.getId();
        ContactAdjustVO result = contactAdjustMapper.selectContactAdjustDetail(id);

        if(Objects.isNull(result)){
            return AjaxResult.getErrorWithMsg("调整记录不存在");
        }

        if(!Objects.equals(result.getStatus(),ContactAdjustStatusEnum.WAIT_HANDLE.getStatus())){
            return AjaxResult.getErrorWithMsg("当前状态不支持审核");
        }
        Integer status = ContactAdjustStatusEnum.RE_COMMIT.getStatus();
        //审核通过
        if(Objects.equals(handleType,0)){

            status = ContactAdjustStatusEnum.SUCCESS.getStatus();
            ContactRecord contactRecord = new ContactRecord();
            Contact contact = createContact(result,contactAdjust,contactRecord);
            //原来联系人失效 新增一个地址
            contact.setStatus(2);
            contactMapper.updateByPrimaryKeySelective(contact);

            ContactVO contactVO = contactMapper.selectById(contact.getContactId());
            Long mid = contactVO.getmId();
            Merchant merchant = merchantMapper.selectByPrimaryKey(mid);
            if (merchant == null){
                log.error("当前地址对应的门店信息不存在！contactVO:{}", JSON.toJSONString(contactVO));
                return AjaxResult.getErrorWithMsg("店铺不存在");
            }


            Contact newContact = new Contact();
            BeanCopyUtil.copyProperties(contactVO, newContact);
            newContact.setStatus(1);
            newContact.setContactId(null);
            contactMapper.insertSelective(newContact);
            //记录审核信息
            contactRecordMapper.insertContactRecord(contactRecord);
            ContactOperateLog contactOperateLog = new ContactOperateLog();
            contactOperateLog.setMId(newContact.getmId());
            contactOperateLog.setContactId(newContact.getContactId());
            contactOperateLog.setCreateTime(LocalDateTime.now());
            contactOperateLog.setUpdateTime(LocalDateTime.now());
            contactOperateLog.setContext("审核地址通过");
            contactOperateLog.setOperateType(ContactLogEnum.OperateType.APPROVE.ordinal());
            contactOperateLog.setOperateName(getAdminName());
            contactOperateLog.setOperateSource(ContactLogEnum.OperateSource.MANAGE.ordinal());
            contactOperateLogMapper.insertSelective(contactOperateLog);

            boolean popMerchant = merchantService.isPopMerchantV2(merchant.getBusinessLine());
            if(popMerchant) {
                //先根据仓网接口返回的POP城配仓属性判断是T+1还是T+2，如果是T+2则不需要调用
                Integer popFulfillmentway = deliveryFenceQueryFacade.getPopFulfillmentway(newContact.getCity(), newContact.getArea(), newContact.getPoiNote(), mid);
                if (Objects.equals(popFulfillmentway, CommonStatus.NO.getCode())) {
                    // 后置处理调用wnc保存城配仓和配送周期
                    contactDeliveryRuleFacade.popDeliveryRuleConfigSaveOrUpdate(newContact.getContactId(), newContact.getStoreNo());
                }
            }

            // 指定城配仓处理逻辑
            contactService.appointStoreNoHandle(popMerchant, newContact,contact.getContactId(), newContact.getStoreNo());
        }
        contactAdjust.setStatus(status);
        contactAdjustMapper.updateContactAdjustStatus(contactAdjust);
        return AjaxResult.getOK();
    }


    @Override
    public void updateStatus() {
        contactAdjustMapper.updateStatus();
    }


    private Contact createContact(ContactAdjustVO oldVO,ContactAdjustVO newVO, ContactRecord contactRecord){
        Contact contact = new Contact();
        contact.setContactId(oldVO.getContactId());
        contact.setPoiNote(newVO.getNewPoi());

        //构建修改记录
        contactRecord.setContactAdjustId(oldVO.getId());
        contactRecord.setOldAddress(oldVO.getAddress());
        contactRecord.setOldCity(oldVO.getCity());
        contactRecord.setOldPoiNote(oldVO.getPoiNote());
        contactRecord.setOldArea(oldVO.getArea());
        contactRecord.setOldProvince(oldVO.getProvince());
        return contact;
    }


}
