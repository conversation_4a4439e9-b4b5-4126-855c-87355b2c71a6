package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.LargeArea;
import net.summerfarm.model.input.LargeAreaQuery;
import net.summerfarm.model.vo.LargeAreaVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LargeAreaMapper {

    /**
    * 运营大区 插入
    */
    int  insertLargeArea(LargeArea largeArea);
    /**
    * 查询
    */
    List<LargeAreaVO> selectLargeArea(LargeAreaQuery largeArea);
    List<LargeAreaVO> selectLargeAreaWithArea(LargeAreaQuery largeArea);

    /**
    * 根据状态查询
    */
    List<LargeAreaVO> selectAll(@Param("status") Integer status);

    /**
    * 查询最大的区域编号
    */
    Integer selectMaxLargeAreaNo();

    /**
     * 修改区域信息
     */
    Integer updateLargeAreaNo(LargeArea largeArea);

    List<LargeArea> getByAreaNos(@Param("largeAreaNos") List<Integer> largeAreaNos);
}
