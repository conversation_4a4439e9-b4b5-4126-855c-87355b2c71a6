package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.domain.DynamicPriceFieldConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DynamicPriceFieldConfigMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(DynamicPriceFieldConfig record);

    
    int insertSelective(DynamicPriceFieldConfig record);

    
    DynamicPriceFieldConfig selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(DynamicPriceFieldConfig record);

    
    int updateByPrimaryKey(DynamicPriceFieldConfig record);


    int insertBatch(@Param("list") List<DynamicPriceFieldConfig> list);

    int deleteByModelConfigId(@Param("modelConfigId") Long modelConfigId);

    List<DynamicPriceFieldConfig> listByModelConfigId(@Param("modelConfigId") Long modelConfigId);
}