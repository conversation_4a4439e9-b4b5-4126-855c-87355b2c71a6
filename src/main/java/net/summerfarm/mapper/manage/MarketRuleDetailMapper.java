package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.market.MarketRuleDetailDTO;
import net.summerfarm.model.domain.MarketRuleDetail;
import net.summerfarm.model.vo.MarketRuleDetailVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MarketRuleDetailMapper {
    int insert(MarketRuleDetail record);

    List<MarketRuleDetailVO> selectList(Integer ruleId);


    void delete(Integer ruleId);

    List<MarketRuleDetailVO> batchSeleteList(@Param("list") List<Integer> marketRuleIds);

    int insertBatch(@Param("list") List<MarketRuleDetailVO> list);

    int insertBatchV2(@Param("list") List<MarketRuleDetail> marketRuleDetailList);

    int updateBatch(@Param("list") List<MarketRuleDetail> updateMarketRuleDetailList);

    int deleteBatch(@Param("list") List<Integer> ids);

    MarketRuleDetail selectById(Integer id);

    List<MarketRuleDetailDTO> selectByReturnIdAndSkus(@Param("ruleIds") List<Integer> marketCouponReturnIds, @Param("skus") List<String> skus);
}