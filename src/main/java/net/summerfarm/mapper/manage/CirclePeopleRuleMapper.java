package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Banner;
import net.summerfarm.model.domain.CirclePeopleRule;
import net.summerfarm.model.vo.CirclePeopleVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface CirclePeopleRuleMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CirclePeopleRule record);

    int insertSelective(CirclePeopleRule record);

    CirclePeopleRule selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CirclePeopleRule record);

    int updateByPrimaryKey(CirclePeopleRule record);

    List<CirclePeopleVo> selectByName(@Param("name") String name);

    List<String> selectByNameId(@Param("nameOrId") String nameOrId);

    List<String> selectActByType(@Param("type") int type, @Param("id") Integer id);

    /**
     * 查询商户圈人中秒杀活动
     * @param ids 当前商户所在地的所有秒杀活动id
     * @param mId 商户id
     * @return 圈人中商户能参加的秒杀活动
     */
    List<Integer> selectActMid(@Param("ids") List<Integer> ids, @Param("mId") Long mId);

    List<CirclePeopleRule> listByIds(@Param("ids") List<Integer> ids);
}