package net.summerfarm.mapper.offline;


import net.summerfarm.model.vo.AdminInfoVo;
import net.summerfarm.model.vo.SalesDataVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface CrmBdDayGmvMapper {
    /**
     * 根据销售id查询
     * @param adminId 销售id
     * @param dayTag 数据更新日期标识
     * @return 销售每日gmv信息
     */
    AdminInfoVo selectByAdminId(@Param("adminId") Integer adminId,@Param("dayTag") Integer dayTag);
}