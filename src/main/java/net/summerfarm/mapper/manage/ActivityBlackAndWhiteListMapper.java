package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.market.ActivityBlackAndWhiteListDTO;
import net.summerfarm.model.domain.ActivityBlackAndWhiteList;
import net.summerfarm.model.input.ActivityBlackAndWhiteListPageQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityBlackAndWhiteListMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ActivityBlackAndWhiteList record);

    int insertSelective(ActivityBlackAndWhiteList record);

    ActivityBlackAndWhiteList selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ActivityBlackAndWhiteList record);

    int updateByPrimaryKey(ActivityBlackAndWhiteList record);

    List<ActivityBlackAndWhiteListDTO> page(ActivityBlackAndWhiteListPageQuery activityBlackListPageQuery);

    int batchDelete(@Param("ids") List<Long> ids);

    int checkRepeat(ActivityBlackAndWhiteList activityBlackAndWhiteList);
}