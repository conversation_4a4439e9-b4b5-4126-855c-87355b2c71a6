package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.TmsTaskOperateRecord;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/7/14  15:16
 */
@Repository
public interface TmsTaskOperateRecordMapper {

    int insertBatch(List<TmsTaskOperateRecord> tmsTaskOperateRecordList);

    List<TmsTaskOperateRecord> selectByDeliveryPathId(Integer deliveryPathId);
}
