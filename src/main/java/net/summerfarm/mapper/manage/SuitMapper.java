package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Suit;
import net.summerfarm.model.input.SuitQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SuitMapper {

    int insert(Suit suit);

    Suit selectByPrimaryKey(Integer id);

    List<Suit> select(SuitQuery selectKeys);

    int update(Suit suit);

    List<Suit> selectAll();
}