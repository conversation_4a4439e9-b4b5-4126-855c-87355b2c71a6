package net.summerfarm.mapper.manage;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import net.summerfarm.model.domain.FeedbackWoProcess;
import net.summerfarm.model.vo.FeedbackWoProcessVO;
import net.summerfarm.model.vo.FeedbackWoVO;
import org.apache.ibatis.annotations.Param;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

public interface FeedbackWoProcessMapper {

    int insert(FeedbackWoProcess record);

    int insertSelective(FeedbackWoProcess record);

    FeedbackWoProcess selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FeedbackWoProcess record);

    int updateByPrimaryKey(FeedbackWoProcess record);

    /**
     * 查询出wo_id下的所有处理记录
     * @param woId
     * @return
     */
    List<FeedbackWoProcessVO> selectByWoId(@Param("woId") Long woId);

}