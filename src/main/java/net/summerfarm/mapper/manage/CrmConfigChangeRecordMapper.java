package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CrmConfigChangeRecord;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface CrmConfigChangeRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CrmConfigChangeRecord record);

    int insertSelective(CrmConfigChangeRecord record);

    CrmConfigChangeRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CrmConfigChangeRecord record);

    int updateByPrimaryKey(CrmConfigChangeRecord record);

    List<CrmConfigChangeRecord> selectByTime(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}