package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PurchaseInvoiceTime;
import net.summerfarm.model.vo.PurchaseInvoiceTimeVO;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface PurchaseInvoiceTimeMapper {

    /**
     * 删除已归档状态的采购发票的实际抵扣信息
     * @param updater
     * @param id
     * @return
     */
    int deleteByPrimaryKey(@Param("updater") String updater, @Param("id") Integer id);

    /**
     * 复核通过
     * @param purchaseInvoiceTimeVO
     * @return
     */
    int insert(PurchaseInvoiceTimeVO purchaseInvoiceTimeVO);

    int insertSelective(PurchaseInvoiceTime record);

    /**
     * 已归档状态的采购发票的实际抵扣信息
     * @param id
     * @return
     */
    PurchaseInvoiceTime selectByPrimaryKey(Integer id);

    /**
     * 归档修改
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(PurchaseInvoiceTime record);

    int updateByPrimaryKey(PurchaseInvoiceTime record);
}