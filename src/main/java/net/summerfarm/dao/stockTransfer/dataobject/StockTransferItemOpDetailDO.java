package net.summerfarm.dao.stockTransfer.dataobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockTransferItemOpDetailDO {
    /**
     * 主键id
     */
    Long id;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;

    /**
     * 关联表id
     */
    Long stockTransferItemOpId;

    /**
     * 转出数量
     */
    Long transferOutNum;

    /**
     * 转出生产日期
     */
    Long produceAt;

    /**
     * 转出保质期
     */
    Long shelfLife;

    /**
     * 转出批次
     */
    String transferOutBatch;

    /**
     * 转出库位
     */
    String transferOutCabinet;

    /**
     * 新生成的转入批次
     */
    String transferInBatch;

    /**
     * 转入库位
     */
    String transferInCabinet;
}
