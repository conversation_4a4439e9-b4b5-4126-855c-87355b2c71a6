package net.summerfarm.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.saas.OutsideOrderService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/10
 */
@Slf4j
@Component
public class SkuSynchronizedToSaasTask  extends XianMuJavaProcessor {
    @Resource
    private OutsideOrderService outsideOrderService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(context.getJobParameters());
        Boolean allSyncFlag = jsonObject.getBoolean("allSyncFlag");
        if (allSyncFlag){
            log.info("全量同步sku到saas货品任务开始------");
            outsideOrderService.firstSynchronizedSku();
            log.info("全量同步sku到saas货品任务结束------");
        }else {
            log.info("全量同步sku到saas货品任务开始------");
            outsideOrderService.skuSynchronizedTask();
        }

        return new ProcessResult(true);
    }
}
