package net.summerfarm.mapper.manage;


import net.summerfarm.model.DTO.coupon.CouponSenderRuleDTO;
import net.summerfarm.model.domain.CouponSenderRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CouponSenderRuleMapper {

    int deleteByPrimaryKey(Long id);

    int insert(CouponSenderRule record);

    int insertSelective(CouponSenderRule record);

    CouponSenderRule selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CouponSenderRule record);

    int updateByPrimaryKey(CouponSenderRule record);

    int deleteByCouponSenderId(Integer senderSetupId);

    int insertBatch(@Param("list") List<CouponSenderRule> couponSenderRules);

    List<CouponSenderRuleDTO> selectRepeatInfo(@Param("senderType") Integer senderType,
                                               @Param("scopeType") Integer scopeType,
                                               @Param("scopeIds") List<Long> scopeIds);

    List<CouponSenderRule> selectByCouponSenderId(Integer couponSenderId);
}