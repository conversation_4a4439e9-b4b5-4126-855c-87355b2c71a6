package net.summerfarm.task;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.finance.FinanceAccountingPeriodOrderService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/4/24 16:09
 */
@Slf4j
@Component
public class FinanceAccountingPeriodTask extends XianMuJavaProcessor {
    @Resource
    private FinanceAccountingPeriodOrderService periodOrderService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("----------生成账期信息------");
        periodOrderService.generateAccountingPeriodBill();
        log.info("----------生成账期信息结束------");
        return new ProcessResult(true);
    }
}
