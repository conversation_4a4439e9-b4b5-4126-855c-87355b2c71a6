package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.offline.StockDashboardFuture;
import net.summerfarm.model.input.purchase.StockDashboardQueryInput;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StockDashboardFutureMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StockDashboardFuture record);

    int insertSelective(StockDashboardFuture record);

    StockDashboardFuture selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StockDashboardFuture record);

    int updateByPrimaryKey(StockDashboardFuture record);

    List<StockDashboardFuture> selectList(StockDashboardQueryInput stockDashboardQueryInput);

    // todo zjx 确认是否 需要po_on_way_quantity
    List<StockDashboardFuture> selectSumList(StockDashboardQueryInput stockDashboardQueryInput);
}