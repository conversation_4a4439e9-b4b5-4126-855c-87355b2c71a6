package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockWarehouseChangeTask;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/5/25
 */
@Repository
public interface StockWarehouseChangeTaskMapper {
    // 批量添加修改库存仓任务
    void batchInsertTask(@Param("list") List<StockWarehouseChangeTask> insert);

    // 根据id修改状态
    void updateState(@Param("id")Integer taskId, @Param("state")Integer state);

    // 根据id查询任务
    List<StockWarehouseChangeTask> selectTaskByIds(@Param("ids") List<Integer> ids);

    StockWarehouseChangeTask selectTaskById(Integer cutTaskId);
}
