package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.GroupBuyConfig;
import net.summerfarm.model.domain.GroupBuyInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface GroupBuyInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(GroupBuyInfo record);

    int insertSelective(GroupBuyInfo record);

    GroupBuyInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GroupBuyInfo record);

    int updateByPrimaryKey(GroupBuyInfo record);

    /**
     * 查询结束的团购
     * @param finishTime 结束时间
     * @return 团购列表
     */
    List<GroupBuyInfo> selectFinishGroupBuy(@Param("finishTime") LocalDateTime finishTime);

    List<GroupBuyInfo> selectByGroupBuyConfigId(Long groupBuyConfigId);
}