package net.summerfarm.mapper.manage.repository;

import net.summerfarm.facade.auth.AuthUserQueryFacade;
import net.summerfarm.mapper.manage.AdminMapper;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.vo.AdminVO;
import net.summerfarm.model.vo.AdminVos;
import net.summerfarm.model.vo.RoleVO;
import net.summerfarm.model.vo.ValuesVo;
import net.xianmu.authentication.client.provider.AuthBaseUserProvider;
import net.xianmu.authentication.client.resp.AuthUserBaseResp;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class AdminRepository {

    @Resource
    AuthUserQueryFacade authUserQueryFacade;
    @Resource
    AdminMapper adminMapper;

    public List<AdminVO> selectAll(Integer roleType) {
        if (roleType == null) {
            return adminMapper.selectAll(null);
        }
        List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(Collections.singletonList(roleType));
        return adminMapper.selectAll(baseUserIds);
    }

    public List<AdminVos> selectMajor() {
        List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(Collections.singletonList(14));
        return adminMapper.selectMajor(baseUserIds);
    }

    public List<AdminVO> selectByRoleTypes(List<Integer> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(roleIds);
        return adminMapper.selectByRoleTypes(baseUserIds);
    }

    /**
     * 查询非大客户的管理员信息
     *
     * @return
     */
    public List<Admin> selectNotMajorAdmin() {
        List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(Collections.singletonList(14));
        return adminMapper.selectNotMajorAdmin(baseUserIds);
    }


    public List<AdminVO> selectMajorPageInfo(AdminVO selectKeys) {
        List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(Collections.singletonList(14));
        selectKeys.setBaseUserIds(baseUserIds);
        return adminMapper.selectMajorPageInfo(selectKeys);

    }

    public List<Admin> selectByNameRemakes(String nameRemakes) {
        List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(Collections.singletonList(14));
        return adminMapper.selectByNameRemakes(nameRemakes, baseUserIds);
    }


    public List<Integer> selectBD() {
        List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(Collections.singletonList(5));
        return adminMapper.selectBD(baseUserIds);
    }


    public List<ValuesVo> selectByRoleTypesNames(List<Integer> roleTypes, String adminName) {
        if (CollectionUtils.isEmpty(roleTypes)) {
            return new ArrayList<>();
        }
        List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(roleTypes);
        return adminMapper.selectByRoleTypesNames(baseUserIds, adminName);
    }


    public List<Admin> selectKeyAccount(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(Collections.singletonList(14));
        return adminMapper.selectKeyAccount(list, baseUserIds);

    }

    public AdminVO selectWithRoles(Integer adminId) {
        Admin admin = adminMapper.selectByAid(adminId);
        if (admin == null) {
            return null;
        }
        AdminVO adminVO = new AdminVO();
        BeanUtils.copyProperties(admin, adminVO);
        //估计adminId 来源获取admin的信息
        List<RoleVO> roleVOS = authUserQueryFacade.roleVOs(adminVO.getBaseUserId());
        Long authId = authUserQueryFacade.getAuthByBaseUserId(adminVO.getBaseUserId());
        adminVO.setRoleVOs(roleVOS);
        adminVO.setAuthId(authId);
        return adminVO;
    }


}


