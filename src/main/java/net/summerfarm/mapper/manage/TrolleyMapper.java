package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Trolley;
import net.summerfarm.model.vo.OrderItemVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TrolleyMapper {
    void cancelCheckByMid(Long id);

    int cancelCheck(@Param("mId") Long mId, @Param("accountId") Long accountId);

    int merge(Trolley trolley);

    /**
     * 删除购物车数据
     *
     * @param mId 店铺id
     * @return
     */
    int deleteByMid(@Param("mId") Long mId);

    int deleteByMidList(@Param("mIdList") List<Long> mIdList);

    /**
     * 查询具有客户大单的订单
     * @param mId 订单编号
     * @param time 加购时间
     * @return 客户大单的订单详情
     */
    List<OrderItemVO> selectOrderReminder(@Param("mId") Long mId,@Param("time") LocalDateTime time);

    /**
     * 根据biz_id 和 sku 进行删除，限量200
     * @param bizId
     * @param sku
     * @return
     */
    int deleteByBizIdAndSku(Long bizId,String sku);

    /**
     * 根据bizId进行删除，限量200
     * @param bizId
     * @return
     */
    int deleteByBizId(Long bizId);
}