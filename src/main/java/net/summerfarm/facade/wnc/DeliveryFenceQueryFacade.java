package net.summerfarm.facade.wnc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.facade.wnc.converter.ContactConverter;
import net.summerfarm.facade.wnc.dto.AreaQueryRes;
import net.summerfarm.facade.wnc.dto.ContactStoreNoDTO;
import net.summerfarm.facade.wnc.dto.FenceCloseTimeInput;
import net.summerfarm.mapper.manage.ContactMapper;
import net.summerfarm.model.domain.Contact;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseLogisticsQueryProvider;
import net.summerfarm.wnc.client.req.AreaQueryReq;
import net.summerfarm.wnc.client.req.FenceCloseTimeQueryReq;
import net.summerfarm.wnc.client.req.StoreQueryReq;
import net.summerfarm.wnc.client.req.fence.AreaQueryWarehouseNoSkuReq;
import net.summerfarm.wnc.client.req.fence.SkuWarehouseNoQueryAreaReq;
import net.summerfarm.wnc.client.req.warehouse.PopWarehouseLogisticsQueryReq;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.summerfarm.wnc.client.resp.FenceCloseTimeResp;
import net.summerfarm.wnc.client.resp.StoreQueryResp;
import net.summerfarm.wnc.client.resp.WarehousLogisticsCenterResp;
import net.summerfarm.wnc.client.resp.fence.AreaResp;
import net.summerfarm.wnc.client.resp.fence.AreaWarehouseNoSkuResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/4/1 11:03
 */

@Service
@Slf4j
public class DeliveryFenceQueryFacade {
    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;

    @DubboReference
    private WarehouseLogisticsQueryProvider warehouseLogisticsQueryProvider;

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private ContactMapper contactMapper;

    /**
     * 查询区域编号
     * @param warehouseNo
     * @param sku
     * @return
     */
    public List<Integer> queryAreaByWarehouseAndSku(Integer warehouseNo, String sku) {
        SkuWarehouseNoQueryAreaReq req = new SkuWarehouseNoQueryAreaReq();
        req.setSku(sku);
        req.setWarehouseNo(warehouseNo);
        DubboResponse<List<AreaResp>> response = deliveryFenceQueryProvider.queryAreaByWarehouseAndSku(req);
        if (Objects.isNull(response) || !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())){
            log.error("DeliveryFenceQueryFacade[]queryAreaByWarehouseAndSku[]error cause:{}", JSON.toJSONString(response));
            throw new BizException(response.getMsg());
        }
        List<AreaResp> responseData = response.getData();
        return CollUtil.isEmpty(responseData) ? Collections.emptyList() : responseData.stream().map(AreaResp::getAreaNo).collect(Collectors.toList());
    }


    /**
     * 获取pop商城的城配仓编号
     * @return
     */
    public Integer getPopStoreNo(String city, String area, String poi, Long mid){
        PopWarehouseLogisticsQueryReq req = new PopWarehouseLogisticsQueryReq();
        req.setCity(city);
        req.setArea(area);
        req.setPoi(poi);
        req.setMerchantId(mid);
        req.setTenantId(BaseConstant.XIANMU_TENANT_ID);
        DubboResponse<WarehousLogisticsCenterResp> dubboResponse = warehouseLogisticsQueryProvider.queryPopWarehouseLogistics(req);
        if (dubboResponse.isSuccess()) {
            WarehousLogisticsCenterResp data = dubboResponse.getData();
            return data == null ? null : data.getStoreNo();
        }
        throw new BizException(dubboResponse.getMsg());
    }

    /**
     * 获取城配仓编号
     * @return
     */
    public ContactStoreNoDTO queryStoreByAddress(String city, String area){
        if(StringUtils.isEmpty(city)){
           throw new BizException("城市不能为空");
        }
        StoreQueryReq req = new StoreQueryReq();
        req.setCity(city);
        req.setArea(area);
        req.setTenantId(BaseConstant.XIANMU_TENANT_ID);
        DubboResponse<StoreQueryResp> response = deliveryFenceQueryProvider.queryStoreByAddress(req);
        if(response == null){
            throw new BizException("根据省市区查询城配仓异常");
        }
        if (!DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())) {
            log.error("DeliveryFenceQueryFacade[]queryStoreByAddress[]error,input:{}", JSON.toJSONString(req));
            throw new ProviderException(response.getMsg());
        }
        StoreQueryResp storeQueryResp = response.getData();
        if(storeQueryResp == null || storeQueryResp.getStoreNo() == null){
            throw new BizException("此地区不在鲜沐围栏配送范围内");
        }
        ContactStoreNoDTO contactStoreNoDTO = new ContactStoreNoDTO();
        contactStoreNoDTO.setStoreNo(storeQueryResp.getStoreNo());
        contactStoreNoDTO.setStoreName(storeQueryResp.getStoreName());
        return contactStoreNoDTO;
    }

    public AreaQueryRes queryAreaByAddress(String city, String area){
        if(StringUtils.isEmpty(city)){
            throw new BizException("城市不能为空");
        }
        AreaQueryReq areaQueryReq = new AreaQueryReq();
        areaQueryReq.setArea(area);
        areaQueryReq.setCity(city);
        DubboResponse<AreaQueryResp> dubboResponse = deliveryFenceQueryProvider.queryAreaByAddress(areaQueryReq);
        if (Objects.isNull(dubboResponse) || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())){
            log.error("WncDeliveryFenceQueryFacadeImpl[]queryAreaByAddress[]is null!");
            return null;
        }
        return ContactConverter.toAreaQueryRes(dubboResponse.getData());
    }

    /**
     * 查询sku 在仓库下对应的运营区域
     * @param sku
     * @param warehouseNos
     * @return
     */
    public List<Integer> queryAreaByListWarehouseAndSku(String sku,List<Integer> warehouseNos){
        if(sku == null || CollectionUtil.isEmpty(warehouseNos)){
            return Collections.emptyList ();
        }
        AreaQueryWarehouseNoSkuReq areaQueryReq = new AreaQueryWarehouseNoSkuReq();
        areaQueryReq.setSkuWarehouseNoQueryAreaReqList(warehouseNos.stream().map (e->{
            SkuWarehouseNoQueryAreaReq req = new SkuWarehouseNoQueryAreaReq ();
            req.setSku (sku);
            req.setWarehouseNo (e);
            return req;
        }).collect(Collectors.toList()));

        DubboResponse<List<AreaWarehouseNoSkuResp>> dubboResponse = deliveryFenceQueryProvider.queryAreaByListWarehouseAndSku (areaQueryReq);
        if (Objects.isNull(dubboResponse) || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())){
            log.error("WncDeliveryFenceQueryFacadeImpl[]queryAreaByAddress[]is null!");
            return null;
        }
        List<AreaWarehouseNoSkuResp> data = dubboResponse.getData ();
        if(CollectionUtil.isEmpty(data)){
            return Collections.emptyList ();
        }
        return data.stream()
                .flatMap(e -> e.getAreaNos().stream())
                .collect(Collectors.toList());
    }

    public LocalTime queryCloseTime(FenceCloseTimeInput req) {
        log.info("DeliveryFenceQueryFacade[]queryCloseTime[]start req:{}", JSON.toJSONString(req));
        FenceCloseTimeQueryReq closeTimeQueryReq = new FenceCloseTimeQueryReq();
        closeTimeQueryReq.setArea(req.getArea());
        closeTimeQueryReq.setCity(req.getCity());
        if (req.getCity() == null && req.getArea() == null) {
            Contact contact = contactMapper.selectByPrimaryKey(req.getContactId());
            if (contact == null) {
                throw new BizException("当前地址信息为空！");
            }
            closeTimeQueryReq.setArea(contact.getArea());
            closeTimeQueryReq.setCity(contact.getCity());
        }
        closeTimeQueryReq.setSource(req.getSource());
        closeTimeQueryReq.setContactId(req.getContactId());
        closeTimeQueryReq.setTenantId(req.getTenantId());
        DubboResponse<FenceCloseTimeResp> dubboResponse = deliveryFenceQueryProvider.queryCloseTime(closeTimeQueryReq);
        log.info("DeliveryFenceQueryFacade[]queryCloseTime[]dubboResponse:{}", JSON.toJSONString(dubboResponse));
        if (Objects.isNull(dubboResponse) || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus()) ||
                Objects.isNull(dubboResponse.getData())){
            log.error("DeliveryFenceQueryFacade[]queryCloseTime[]is null!");
            return Global.CLOSING_ORDER_TIME;
        }
        return dubboResponse.getData().getCloseTime();
    }

    /**
     * 获取pop商城履约方式，0：POP专配履约，1：POP鲜沐共配履约，为空时默认POP鲜沐共配履约
     * @return
     */
    public Integer getPopFulfillmentway(String city, String area, String poi, Long mid){
        PopWarehouseLogisticsQueryReq req = new PopWarehouseLogisticsQueryReq();
        req.setCity(city);
        req.setArea(area);
        req.setPoi(poi);
        req.setMerchantId(mid);
        req.setTenantId(BaseConstant.XIANMU_TENANT_ID);
        DubboResponse<WarehousLogisticsCenterResp> dubboResponse = warehouseLogisticsQueryProvider.queryPopWarehouseLogistics(req);
        if (dubboResponse.isSuccess()) {
            WarehousLogisticsCenterResp data = dubboResponse.getData();
            return data == null ? null : data.getPopFulfillmentWay();
        }
        throw new BizException(dubboResponse.getMsg());
    }
}
