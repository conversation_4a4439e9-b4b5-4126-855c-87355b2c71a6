package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.BusinessMission;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BusinessMissionMapper {


    int insert(BusinessMission record);


    BusinessMission selectByPrimaryKey(Integer id);


    int updateByPrimaryKey(BusinessMission record);

    @RequiresDataPermission(originalField = "t.area_no")
    List<BusinessMission> select(BusinessMission selectKeys);
}