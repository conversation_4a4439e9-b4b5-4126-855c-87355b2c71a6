package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.OrderItem;
import net.summerfarm.model.input.SalesDataInput;
import net.summerfarm.model.input.TeamDataInput;
import net.summerfarm.model.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface SalesDataMapper {

    /**
     * 拜访数
     * @param salesDataInput
     * @return 拜访数
     */
    Integer selectVisitNum(SalesDataInput salesDataInput);

    /**
     * 新客户下单数
     * @param salesDataInput 查询条件
     * @return 新客户下单数
     */
    Integer selectNewAdminOrderNum(SalesDataInput salesDataInput);

    /**
     * 新客户数
     * @param salesDataInput 查询条件
     * @return 新客户下单数
     */
    Integer selectNewAdmin(SalesDataInput salesDataInput);

    /**
     * 获取bd的客户月活:性能好,但数据只在展示本月时准确,展示过往月数据时,会比实际值大,建议仅使用在展示本月月活处
     * @param salesDataInput 筛选条件
     * @return 客户月活数量
     */
    SalesDataVo selectMonthLiving(SalesDataInput salesDataInput);

    /**
     * 获取bd的客户月活:关联订单表,性能一般,建议时间跨度小时使用
     * @param salesDataInput 筛选条件
     * @return 客户月活数量
     */
    int selectMonthLivingByOrders(SalesDataInput salesDataInput);

    /**
     * 根据当前登录账号拥有的城市权限查询BD名单
     * @param adminId id
     * @param bdName bd姓名
     * @return bd详情
     */
    List<BdVO> selectBDByArea(@Param("adminId") Integer adminId, @Param("bdName") String bdName);

    /**
     * 查询销售业务数据页面
     * @param teamDataInput 查询条件
     * @return 销售业务数据列表
     */
    @RequiresDataPermission(originalField = "m.area_no")
    List<TeamDataVO> salesTeamData(TeamDataInput teamDataInput);

    /**
     * 显示客户数据详情
     * @param mId 商户id
     * @return 客户数据详情
     */
    TeamDataVO selectOrderDetails(Long mId);

    /**
     * 销售客户数
     * @param adminId 销售id
     * @return 单店,大客户,品牌数
     */
    AdminInfoVo selectMerchantNum(Integer adminId);

    /**
     * 销售本月已下单客户数,店铺类型维度
     * @param adminId 销售id
     * @param startTime 本月开始时间
     * @return 单店已下单,大客户已下单数
     */
    AdminInfoVo selectOrderNum(@Param("adminId") Integer adminId ,@Param("startTime") LocalDateTime startTime);

    /**
     * 根据商品名查询其类目及品牌
     * @param name 商品名
     * @return 类目及品牌
     */
    List<SalesDataVo> selectSkuCategory(String name);

    /**
     * 获取本月已下单客户数,公私海维度
     * @param salesDataInput 查询条件
     * @return 本月已下单客户数
     */
    SalesDataVo selectBdMerchantOrderNum(SalesDataInput salesDataInput);

    /**
     * 根据地区及团队查询拜访数,陪访数
     * @param salesDataInput 查询条件
     * @return 拜访/陪访数
     */
    SalesDataVo selectVisitByArea(SalesDataInput salesDataInput);
}
