package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FenceDelivery;

/**
 * 围栏配送mapper
 */
public interface FenceDeliveryMapper {
    /**
     * 根据id删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);
    /**
     * 新增围栏配送
     * @param record 围栏配送
     * @return
     */
    int insert(FenceDelivery record);
    /**
     * 新增围栏配送
     * @param record 围栏配送
     * @return
     */
    int insertSelective(FenceDelivery record);
    /**
     * 根据id查看
     * @param id
     * @return
     */
    FenceDelivery selectByPrimaryKey(Long id);
    /**
     * 更新围栏配送
     * @param record 围栏配送
     * @return
     */
    int updateByPrimaryKeySelective(FenceDelivery record);
    /**
     * 更新围栏配送
     * @param record 围栏配送
     * @return
     */
    int updateByPrimaryKey(FenceDelivery record);

    /**
     * 根据围栏ID取配送数据
     * @param id 围栏id
     * @return 围栏配送数据
     */
    FenceDelivery selectByFenceId(Integer id);

    /**
     * 根据围栏id修改围栏配送信息
     * @param fenceDelivery 围栏信息
     */
    void updateByFenceIdSelective(FenceDelivery fenceDelivery);
}