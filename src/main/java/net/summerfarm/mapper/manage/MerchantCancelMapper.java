package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MerchantCancel;
import net.summerfarm.model.input.MerchantCancelPageQuery;
import net.summerfarm.model.vo.MerchantCancelVO;

import java.util.List;

public interface MerchantCancelMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MerchantCancel record);

    int insertSelective(MerchantCancel record);

    MerchantCancel selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantCancel record);

    int updateByPrimaryKey(MerchantCancel record);

    List<MerchantCancelVO> getPage(MerchantCancelPageQuery merchantCancelPageQuery);

    MerchantCancel selectByEntity(MerchantCancel merchantCancel);

    MerchantCancel selectByPrimaryKeyForMaster(Long id);
}