package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.XianmuCardStatistics;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-11-05
 **/
@Repository
public interface XianmuCardStatisticsMapper {

    /**
     * 根据时间查询
     * @param statDate
     * @return
     */
    XianmuCardStatistics queryByStatDate(String statDate);

    /**
     * 根据时间范围查询
     * @param startTime
     * @param endTime
     * @return
     */
    List<XianmuCardStatistics> queryByStatDates(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
