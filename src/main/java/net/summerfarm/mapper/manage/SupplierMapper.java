package net.summerfarm.mapper.manage;


import net.summerfarm.model.DTO.purchase.SupplierBaseInfoDTO;
import net.summerfarm.model.domain.Supplier;
import net.summerfarm.model.domain.SupplierAccount;
import net.summerfarm.model.input.SaasSupplierInput;
import net.summerfarm.model.input.SaasSupplierReq;
import net.summerfarm.model.input.SupplierReq;
import net.summerfarm.model.input.purchase.SupplierQueryInput;
import net.summerfarm.model.param.SupplierJudgeDateParam;
import net.summerfarm.model.param.SupplierPurchaserParam;
import net.summerfarm.model.vo.InvoiceSupplierVO;
import net.summerfarm.model.vo.SupplierTenantVO;
import net.summerfarm.model.vo.SupplierVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SupplierMapper {

    int insert(Supplier supplier);

    void update(Supplier supplier);

    void updateSelective(Supplier supplier);

    /**
     * 查询供应商账户信息
     * @param supplierId
     * @return
     */
    SupplierAccount selectAccountBySupplierId(Integer supplierId);

    /**
     * 查询供应商账户信息
     * @param supplierId
     * @return
     */
    SupplierAccount selectByAccount(Integer supplierId);

    List<SupplierReq> select(Supplier supplier);

    /**
     * 供应商详情
     * @param id
     * @return
     */
    SupplierReq selectDetail(Integer id);

    List<Supplier> selectAll();

    Supplier selectByName(String name);

    Supplier selectByTaxNumber(@Param("name") String merchantName,@Param("taxNumber") String taxNumber);

    List<Supplier> selectListByName(String name);

    Supplier selectByPrimaryKey(Integer id);

    /**
     * 查询供应商合同信息
     * @param supplierId
     * @return
     */
    SupplierReq selectBill(Integer supplierId);

    /**
     * 查询供货商税号/身份证号是否存在
     * @param taxNumber
     * @return
     */
    int selectTaxNumber(String taxNumber);

    /**
     * 根据id查询供货商管理人信息
     * @param id
     * @return
     */
    Supplier selectManager(Integer id);

    /**
     * 查询供应商信息
     * @return
     */
    List<SupplierVO> selectList();

    /**
     * 发票销售方信息(供应商)
     * @return
     */
    List<InvoiceSupplierVO> selectInvoiceList();

    /**
     * 根据发票销售方名称查询供应商信息
     * @param supplierName
     * @return
     */
    Supplier selectId(String supplierName);

    /**
     * 查询供应商信息
     * @return
     */
    List<SupplierVO> selectSupplierName();

    /**
     * 根据供应商名称查询是否在存在
     * @param name
     * @return
     */
    Supplier selectBySupplierName(String name);

    void enableSupplier(int id);

    List<SupplierReq> selectAuditByType(SupplierReq supplierReq);

    void switchToAuditing(Integer id);

    void switchToRefuse(Integer id);

    void switchToDisable(Integer id);

    List<SupplierJudgeDateParam> getJudgeDateList();

    void batchShutDown(@Param("ids") List<Long> ids);

    List<SupplierPurchaserParam> selectLastPurchaserGroupBySupplierId();

    void batchUpdatePurchaser(List<SupplierPurchaserParam> list);

    List<Supplier> creditCodeCheck(String taxNumber);

    SupplierReq detail(Integer id);

    List<SupplierVO> selectEnabledSupplierName();

    int checkValidContract(Integer id);

    List<SupplierReq> selectEnableButNoContract(SupplierReq supplierReq);

    List<SupplierBaseInfoDTO> selectSupplierBaseInfoByInput(SupplierQueryInput input);

    void updateQrCodeSwitch(SupplierReq supplier);


    List<Supplier> selectByPrimaryKeyList(List<Integer> ids);

    List<SupplierTenantVO> selectWithSaasTenantId(SaasSupplierReq req);

    Integer countNameWithSaas(SaasSupplierInput input);

    Integer countTenantSupplier(SaasSupplierInput input);

    Integer insertWithSaasSource(Supplier supplier);

    Integer updateByIdWithSaas(Supplier supplier);

    List<SupplierTenantVO> allSuppliers(Long tenantId);

    List<SupplierVO> selectSupplierNameWithSource();

    List<SupplierTenantVO> listSupplier(SaasSupplierReq supplierReq);

    List<SupplierReq> selectReqByName(String supplierName);

    Supplier selectByNameWithTenantId(@Param("name") String supplierName, @Param("tenantId") Long tenantId);
}