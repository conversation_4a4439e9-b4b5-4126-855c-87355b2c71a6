package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.BatchLackToCondemnDTO;
import net.summerfarm.model.DTO.LackGoodsApprovedDTO;
import net.summerfarm.model.domain.TmsLackGoodsApproved;
import net.summerfarm.model.vo.LackGoodsApprovedVO;
import net.summerfarm.model.vo.LackGoodsOrderInfoVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TmsLackGoodsApprovedMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TmsLackGoodsApproved record);

    int insertSelective(TmsLackGoodsApproved record);

    TmsLackGoodsApproved selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TmsLackGoodsApproved record);

    int updateByPrimaryKey(TmsLackGoodsApproved record);

    List<LackGoodsOrderInfoVO> selectOrderInfoByPathId(Integer deliveryPathId);

    List<LackGoodsApprovedVO> getDataList(LackGoodsApprovedDTO lackGoodsApprovedDTO);

    void batchUpdate(BatchLackToCondemnDTO batchLackToCondemnDTO);

    List<TmsLackGoodsApproved> selectByDeliveryPathId(Integer deliveryPathId);
}