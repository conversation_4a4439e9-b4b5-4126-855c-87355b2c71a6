package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AfterSaleWoProcess;

import java.util.List;

public interface AfterSaleWoProcessMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AfterSaleWoProcess record);

    int insertSelective(AfterSaleWoProcess record);

    AfterSaleWoProcess selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AfterSaleWoProcess record);

    int updateByPrimaryKey(AfterSaleWoProcess record);

    List<AfterSaleWoProcess> selectByWoId(Integer woId);
}