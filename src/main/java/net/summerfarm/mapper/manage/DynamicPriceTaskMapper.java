package net.summerfarm.mapper.manage;

import java.util.Date;
import java.util.List;
import net.summerfarm.model.DTO.inventory.DynamicPriceTaskQueryDTO;
import net.summerfarm.model.domain.DynamicPriceTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DynamicPriceTaskMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(DynamicPriceTask record);

    
    int insertSelective(DynamicPriceTask record);

    
    DynamicPriceTask selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(DynamicPriceTask record);

    
    int updateByPrimaryKey(DynamicPriceTask record);

    List<DynamicPriceTask> listByQuery(DynamicPriceTaskQueryDTO queryDTO);

    int countByTaskExeTime(@Param("taskExeTime") Date taskExeTime);

    DynamicPriceTask getTodayLast(@Param("categoryType") Integer categoryType);

    int updateStatus(@Param("status") Integer status);
}