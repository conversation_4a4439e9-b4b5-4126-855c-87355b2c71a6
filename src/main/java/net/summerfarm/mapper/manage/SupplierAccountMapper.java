package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SupplierAccount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SupplierAccountMapper {
    int insert(SupplierAccount record);

    int insertSelective(SupplierAccount record);

    int updateSelective(SupplierAccount el);

    List<SupplierAccount> selectSupplierAccount(Integer accountId);

    /**
     * 查询发票销售方名称是否存在
     * @param supplierName
     * @return
     */
    int selectAccountName(String supplierName);

    /**
     * 根据发票销售方名称得到其他信息
     * @param supplierName
     * @return
     */
    SupplierAccount selectName(String supplierName);

    List<SupplierAccount> selectSupplierBySupplierId(Integer supplierId);

    int delete(Integer accountId);

    SupplierAccount selectById(Integer id);

    int deleteBySupplierId(Integer supplierId);
}
