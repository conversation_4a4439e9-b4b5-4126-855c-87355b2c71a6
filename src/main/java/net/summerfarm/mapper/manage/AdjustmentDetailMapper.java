package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AdjustmentDetail;
import net.summerfarm.model.input.AdjustmentInput;
import net.summerfarm.model.vo.AdjustmentDetailVO;
import net.summerfarm.model.vo.finance.AdminAdjustExportVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 调整单明细mapper
 * @date 2021/12/8 17:17
 */
@Repository
public interface AdjustmentDetailMapper {

    /**
     * 查询调整单明细
     * @param adjustNo
     * @return
     */
    List<AdjustmentDetailVO> selectDetail(String adjustNo);

    /**
     * 查询单店调整单明细
     * @param adjustNo
     * @param mId
     * @return
     */
    List<AdjustmentDetailVO> selectDetailByMId(String adjustNo, Long mId);

    /**
     * 指定订单明细
     * @param input
     * @return
     */
    List<AdjustmentDetailVO> selectItem(AdjustmentInput input);

    /**
     * 根据订单明细id查询调整实付成功的总金额（不包括运费调整）
     * @param orderItemId
     * @return
     */
    BigDecimal selectSuccessNumByOrderItem(Long orderItemId);

    /**
     * 根据订单编号查询调整运费成功的总金额
     * @param orderNo
     * @return
     */
    BigDecimal selectSuccessNumByOrderNo(String orderNo);

    /**
     * 批量插入明细
     * @param detailList
     */
    void insertBatch(List<AdjustmentDetail> detailList);

    /**
     * 根据订单明细id查询调整明细
     * @param orderItemIdList
     * @return
     */
    List<AdjustmentDetailVO> selectByOrderItem(@Param("orderItemIdList") List<Long> orderItemIdList);

    /**
     * 根据订单编号和调整单号查询运费调整
     * @param adjustNo
     * @param orderNo
     * @return
     */
    BigDecimal selectDelivery(@Param("adjustNo") String adjustNo, @Param("orderNo") String orderNo);

    /**
     * 查询订单项的调整金额
     *
     * @param orderNos 订单项id
     * @return {@link BigInteger}
     */
    BigDecimal selectAdjustAmountByItemId(@Param("orderNos")List<String> orderNos);

    /**
     * 根据订单号查询调整单
     *
     * @param OrderNos 订单号
     * @return {@link List}<{@link AdminAdjustExportVo}>
     */
    List<AdminAdjustExportVo> selectAdjustByOrderNos(@Param("orderNos")List<String> OrderNos);

    /**
     * 查询门店订单调整金额
     *
     * @param billNo 账单比那好
     * @param mId    m id
     * @return {@link BigDecimal}
     */
    BigDecimal selectAdjustByStoreOrder(@Param("billNo")String billNo,@Param("mId")Integer mId);
}
