package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.domain.market.TagLaunchScopeConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface TagLaunchScopeConfigMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(TagLaunchScopeConfig record);

    
    int insertSelective(TagLaunchScopeConfig record);

    
    TagLaunchScopeConfig selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(TagLaunchScopeConfig record);

    
    int updateByPrimaryKey(TagLaunchScopeConfig record);

    int insertBatch(@Param("list") List<TagLaunchScopeConfig> list);

    int deleteByInfoId(Long infoId);

    int deleteByScopeId(@Param("infoId") Long infoId, @Param("scopeId") Long scopeId);

    List<Long> listByInfoId(@Param("infoId") Long infoId);

}