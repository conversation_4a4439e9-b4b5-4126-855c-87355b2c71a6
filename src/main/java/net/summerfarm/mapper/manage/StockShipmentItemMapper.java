package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AbnormalRecord;
import net.summerfarm.model.domain.StockShipmentItem;
import net.summerfarm.model.domain.StockShipmentItemDetail;
import net.summerfarm.module.pms.model.vo.AllocationOrderItemEntityVO;
import net.summerfarm.model.vo.StockShipmentItemVO;
import net.summerfarm.model.vo.StockTaskProcessVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> xiang
 * create at:  2022-02-24
 */
@Repository
public interface StockShipmentItemMapper {
    /**
     * 批量新增出库条目
     * @param id
     * @param items
     */
    void insertBatch(@Param("stockTaskId") Integer id, @Param("list") List<AllocationOrderItemEntityVO> items, @Param("tenantId") Long tenantId);

    /**
     * 查询出库条目
     * @param taskNo
     * @return
     */
    List<AllocationOrderItemEntityVO> select(String taskNo);

    /**
     * 根据任务编号list查询
     * @param taskNo 任务编号list
     * @return 查询结果
     */
    List<AllocationOrderItemEntityVO> selectByTaskNoList(@Param("list") List<String> taskNo);

    /**
     * 查询出库条目
     * @param taskNo
     * @return
     */
    StockShipmentItem selectBySku(String taskNo,String sku);

    /**
     * 查询出库明细
     * @param param
     * @return
     */
    List<StockShipmentItemDetail> selectBySkuDetail(AbnormalRecord param);

    /**
     * 查询出库单条目
     * @param id
     * @return
     */
    StockShipmentItemVO selectById(Integer id);

    /**
     * 查询出库单条目
     * @param taskNo
     * @return
     */
    List<StockShipmentItem> selectByTaskNo(String taskNo);

    /**
     * 更新出库条目信息
     * @param id
     * @param actualQuantityAdd
     */
    void updateAdd(@Param("id") Integer id, @Param("actualQuantityAdd") Integer actualQuantityAdd);

    /**
     * 查询出库明细
     * @param stockTaskId
     * @return
     */
    List<StockTaskProcessVO> selectByTaskId(Integer stockTaskId);


    BigDecimal countCapacity(@Param("stockTaskIds")List<Integer> stockTaskIds);
}
