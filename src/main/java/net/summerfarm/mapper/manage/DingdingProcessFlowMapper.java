package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.dingding.DingdingProcessFlow;
import org.apache.ibatis.annotations.Param;

/**
 * 钉钉审批-表单数据表Mapper类
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021-12-29
 */
public interface DingdingProcessFlowMapper {

    /**
     * 插入钉钉审批表单数据
     * @param record 表单数据
     * @return 变更记录数
     */
    int insert(DingdingProcessFlow record);

    /**
     * 插入钉钉审批表表单数据-如果字段为空则对应列不插入
     * @param record 表单数据
     * @return 变更记录数
     */
    int insertSelective(DingdingProcessFlow record);

    /**
     * 根据钉钉审批实例id查询审批数据-不带form_data字段
     * @param processInstanceId 审批实例id
     * @return 审批数据
     */
    DingdingProcessFlow selectByProcessInstanceId(@Param("processInstanceId") String processInstanceId);

    /**
     * 审批更新
     * @param flow 审批数据
     * @return 变更记录数
     */
    int updateById(@Param("flow") DingdingProcessFlow flow);

    /**
     * 根据业务数据id以及业务类型查询审批数据-不带form_data字段
     * @param bizId 业务数据id
     * @param bizType 业务数据类型
     * @return 审批单数据
     */
    DingdingProcessFlow selectOneByBizIdAndBizType(@Param("bizId") Long bizId, @Param("bizType") Integer bizType);

    DingdingProcessFlow selectOneByBizIdAndBizTypeOrderById(@Param("bizId") Long bizId, @Param("bizType") Integer bizType);

}