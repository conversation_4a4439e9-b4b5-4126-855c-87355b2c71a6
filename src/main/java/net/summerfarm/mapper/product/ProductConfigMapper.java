package net.summerfarm.mapper.product;

import net.summerfarm.model.ProductConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * @Date 2024/8/5 10:50
 * @<AUTHOR>
 */
@Mapper
public interface ProductConfigMapper {

    int insert(ProductConfig record);

    ProductConfig selectByPrimaryKey(Long id);

    List<ProductConfig> selectListByTypeAndKey(@Param("configType")Integer configType, @Param("configKey")Long configKey);
}
