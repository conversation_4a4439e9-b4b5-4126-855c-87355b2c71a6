package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.domain.market.TagLaunchSkuConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface TagLaunchSkuConfigMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(TagLaunchSkuConfig record);

    
    int insertSelective(TagLaunchSkuConfig record);

    
    TagLaunchSkuConfig selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(TagLaunchSkuConfig record);

    
    int updateByPrimaryKey(TagLaunchSkuConfig record);

    int insertBatch(@Param("list") List<TagLaunchSkuConfig> list);

    int deleteByInfoId(Long infoId);

    int deleteByBizId(@Param("infoId") Long infoId, @Param("bizId") String bizId);

    List<String> listByInfoId(@Param("infoId") Long infoId);

}