package net.summerfarm.mapper.manage;
import java.util.Collection;

import net.summerfarm.model.domain.PriceStrategy;
import net.summerfarm.model.domain.PriceStrategyAuditRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface PriceStrategyMapper {
    int insertSelective(PriceStrategy priceStrategy);

    /**
     * 新增价格调整策略
     * @param priceStrategies 价格策略实体
     */
    void insertBatch(@Param(value = "list") List<PriceStrategy> priceStrategies);

    /**
     * 更新价格调整策略
     * @param priceStrategy
     */
    void updateSelectiveByBusinessId(PriceStrategy priceStrategy);


    /**
     * 根据业务id和业务类型查询调价策略
     * @param businessId 业务id
     * @param type 业务类型
     * @return
     */
    PriceStrategy select(@Param("businessId") Long businessId, @Param("type") Integer type);

    /**
     * 更新价格
     * @param effectivePrice 生效价格
     * @param strategyId     调价id
     */
    void updateEffectivePrice(@Param("effectivePrice") BigDecimal effectivePrice, @Param("strategyId") Integer strategyId);

    /**
     * 查询最新的审核记录
     * @param strategyId strategyId
     * @return PriceStrategyAuditRecord
     */
    PriceStrategyAuditRecord selectNewestRecord(@Param("strategyId") Long strategyId);

    /**
     * @param id id
     * @return PriceStrategy
     */
    PriceStrategy selectById(@Param("id") Integer id);

    List<PriceStrategy> selectByBusinessIdInAndType(@Param("businessIds")Collection<Long> businessIds, @Param("type")Integer type);
}
