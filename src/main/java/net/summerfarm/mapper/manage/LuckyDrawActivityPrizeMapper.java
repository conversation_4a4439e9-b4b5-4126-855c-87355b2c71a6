package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.LuckyDrawActivityPrize;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface LuckyDrawActivityPrizeMapper {

    int deleteByPrimaryKey(Long id);

    int insert(LuckyDrawActivityPrize record);

    int insertSelective(LuckyDrawActivityPrize record);

    LuckyDrawActivityPrize selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LuckyDrawActivityPrize record);

    int updateByPrimaryKey(LuckyDrawActivityPrize record);

    int batchInsert(@Param("list") List<LuckyDrawActivityPrize> activityPrizeList);

    List<LuckyDrawActivityPrize> selectByEquityPackageId(Long equityPackageId);

    int batchDeleteByEqutiyPackageId(@Param("list") List<Long> equityPackageIds);
}