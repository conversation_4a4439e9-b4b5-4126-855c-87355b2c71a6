package net.summerfarm.mapper.manage;

import net.summerfarm.model.RefundSlipVoucher;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> 2021/10/22
 */
@Repository
public interface RefundSlipVoucherMapper {
    int deleteByPrimaryKey(Integer id);

    /**
     * 退款单退款凭证信息
     * @param record
     * @return
     */
    int insert(RefundSlipVoucher record);

    int insertSelective(RefundSlipVoucher record);

    RefundSlipVoucher selectByPrimaryKey(Integer id);

    /**
     * 查询退货单图片地址
     * @param id
     * @return
     */
    List<String> selectVoucherAddress(Integer id);

    int updateByPrimaryKeySelective(RefundSlipVoucher record);

    int updateByPrimaryKey(RefundSlipVoucher record);
}