package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MarketCouponSend;
import net.summerfarm.model.input.MarketCouponSendQuery;
import net.summerfarm.model.vo.MarketCouponSendVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MarketCouponSendMapper {

    /**
     * 新增发放记录
     * @param record 发放记录实体
     * @return 受影响的行数
     */
    int insert(MarketCouponSend record);

    /**
     * 更新发放记录状态
     * @param id 发放记录id
     * @param status 状态
     */
    int updateStatus(Long id, String auditName, Integer status);

    /**
     * 查询发放记录
     * @param sendId 记录id
     * @return 发放记录实体
     */
    MarketCouponSend selectByPrimaryKey(Long sendId);

    /**
     * 查询发放记录列表
     * @param couponSendQuery 查询条件
     * @return 放记录列表
     */
    List<MarketCouponSendVO> select(MarketCouponSendQuery couponSendQuery);

    /**
     * 查询发放记录-强制走主库
     * @param sendId 记录id
     * @return 发放记录实体
     */
    MarketCouponSend selectByPrimaryKeyForceMaster(Long sendId);

    /**
     * 更新发放记录状态
     * @param id 发放记录id
     * @param sendStatus 状态
     */
    int updateSendStatus(@Param("id") Long id, @Param("sendStatus") Integer sendStatus, @Param("originSendStatus") Integer originSendStatus);

    /**
     * 查询发放记录
     * @param marketCouponSend
     */
    List<MarketCouponSend> selectByEntity(MarketCouponSend marketCouponSend);

    /**
     * 更新数据
     * @param marketCouponSend
     */
    int updateByPrimaryKeySelective(MarketCouponSend marketCouponSend);

    /**
     * 更新发放记录状态
     * @param id 发放记录id
     * @param sendStatus 状态
     */
    int updateSendStatusNotOrigin(@Param("id") Long id, @Param("sendStatus") Integer sendStatus);
}