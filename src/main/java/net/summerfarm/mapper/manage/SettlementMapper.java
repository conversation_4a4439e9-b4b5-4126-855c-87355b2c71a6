package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.Settlement;
import net.summerfarm.model.input.SettlementQuery;
import net.summerfarm.model.vo.SettlementDetailVO;
import net.summerfarm.model.vo.SettlementVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface SettlementMapper {
    int insert(Settlement record);

    int insertSelective(Settlement record);

    @RequiresDataPermission(originalField = "storeNo")
    List<SettlementVO> select(SettlementQuery query);

    SettlementVO selectSettlementVO(Integer settlementId);

    int updateSelective(Settlement settlement);

    BigDecimal unFinishAmount();

    /**
     * 现金结付时，处于审核失败/打款取消
     * @return
     */
    BigDecimal unFinishCashAmount();

    /*
     * @Description:查询最后插入的结算单记录
     * @Return:
     * @Date: 2020/11/9 15:29
     * @Author: <EMAIL>
     */
    SettlementVO selectLastRecord();

    /**
     * 根据id查询结算单
     * @param settlementId
     * @return
     */
    Settlement selectById(Integer settlementId);

    /**
     * 根据条件查询结算单详情
     * @param settlementDetailVO
     * @return
     */
    List<SettlementDetailVO> selectConditionDetail(SettlementDetailVO settlementDetailVO);

    /**
     * 根据采购单号查询该采购单的结算情况
     * @param purchaseNo 采购单号
     * @param purchasePlanId 采购计划id
     * @return 采购单的结算情况
     */
    Settlement selectByNo(@Param("purchaseNo") String purchaseNo, @Param("purchasePlanId") Integer purchasePlanId);

    /**
     * 根据供应商查询供应商信息和供应商账户信息
     * @param supplierId
     * @param supplierName
     * @return
     */
    Settlement selectSupplierMessage(Integer supplierId, String supplierName);

    /**
     * 根据采购单号查询付款情况
     * @param purchasesNo
     * @return
     */
    SettlementVO selectByPurchasesNo(String purchasesNo);

    /**
     * 改变结算单退结金额
     * @param refundSettlementAmount
     * @param settlementId
     * @param adminName
     * @return
     */
    int updateRefundSettlementAmount(@Param("refundSettlementAmount") BigDecimal refundSettlementAmount, @Param("settlementId") Integer settlementId, @Param("adminName") String adminName);

    /**
     * 初始化结算单历史退结
     * @param refundSettlementAmount
     * @param settlementId
     * @param adminName
     * @return
     */
    int updateSettlementRefundSettlementAmount(@Param("refundSettlementAmount") BigDecimal refundSettlementAmount, @Param("settlementId") Integer settlementId, @Param("adminName") String adminName);

    /**
     * 查询部分结算状态的结算单
     * @return
     */
    List<Settlement> selectPartSettle();
}
