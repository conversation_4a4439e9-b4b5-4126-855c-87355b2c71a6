package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DeliveryCarWarehouseFee;

/**
 * 前置仓费mapper.
 */
public interface DeliveryCarWarehouseFeeMapper {
    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     * @param record
     * @return
     */
    int insert(DeliveryCarWarehouseFee record);

    /**
     * 新增
     * @param record
     * @return
     */
    int insertSelective(DeliveryCarWarehouseFee record);

    /**
     * 查看
     * @param id
     * @return
     */
    DeliveryCarWarehouseFee selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(DeliveryCarWarehouseFee record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(DeliveryCarWarehouseFee record);

    /**
     * 根据配送司机id查看
     * @param deliveryCarId
     */
    DeliveryCarWarehouseFee selectByDeliveryCarId(Integer deliveryCarId);
}