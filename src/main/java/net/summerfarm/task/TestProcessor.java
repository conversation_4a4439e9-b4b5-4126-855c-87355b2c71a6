package net.summerfarm.task;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class TestProcessor extends XianMuJavaProcessor {
    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("测试定时任务～");
        return new ProcessResult(true);
    }
}
