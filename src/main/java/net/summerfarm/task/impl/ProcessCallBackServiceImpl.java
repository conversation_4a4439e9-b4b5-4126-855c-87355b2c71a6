package net.summerfarm.task.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.RedisKeys;
import net.summerfarm.common.constant.dingding.DingdingEventTypeConstant;
import net.summerfarm.common.util.DingTalkUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.dingding.bo.DingdingCallBackBO;
import net.summerfarm.factory.DingHandleEventFactory;
import net.summerfarm.model.ding.dto.ProcessCallBackFailDTO;
import net.summerfarm.model.ding.dto.ProcessInstance;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.task.ProcessCallBackService;
import net.summerfarm.tms.util.RedisUtil;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProcessCallBackServiceImpl implements ProcessCallBackService {

    public static Long ONE_HOURS = 1L * 60 * 60;

    @Resource
    public RedisUtil redisUtil;

    @Autowired
    MqProducer mqProducer;

    @Override
    public void processCallBackPullTask() {
        log.info("启动拉取钉钉回调失败列表任务");
        try {
            // 副本
            String data = (String) redisUtil.get(RedisKeys.buildDingCallBackInfoKey());
            ProcessCallBackFailDTO processCallBackFailDTO;
            if (StringUtils.isEmpty(data)) {
                processCallBackFailDTO = DingTalkUtils.pullProcessCallBackFailList();
                if (Objects.isNull(processCallBackFailDTO)) {
                    return;
                }
                redisUtil.set(RedisKeys.buildDingCallBackInfoKey(), JSON.toJSONString(processCallBackFailDTO), ONE_HOURS);
            } else {
                log.info("钉钉回调失败信息存在未完成的缓存列表:{}", data);
                processCallBackFailDTO = JSON.parseObject(data, ProcessCallBackFailDTO.class);
            }

            // 处理逻辑
            dealCallBackFail(processCallBackFailDTO);
            // 删除副本
            redisUtil.del(RedisKeys.buildDingCallBackInfoKey());
        } catch (Exception e) {
            log.warn("本次拉取钉钉回调失败消息异常", e);
        }
        log.info("结束拉取钉钉回调失败列表任务");
    }

    private void dealCallBackFail(ProcessCallBackFailDTO processCallBackFailDTO) {
        log.info("开始处理回调失败消息,信息{}", JSON.toJSONString(processCallBackFailDTO));
        // 回调失败列表
        List<ProcessInstance> failList = processCallBackFailDTO.getFailList();
        if (CollectionUtils.isEmpty(failList)) {
            return;
        }

        List<ProcessInstance> needDeal = failList.stream()
                .filter(item -> item.getCallBackTag().equals(DingdingEventTypeConstant.BPMS_INSTANCE_CHANGE) ||
                        item.getCallBackTag().equals(DingdingEventTypeConstant.BPMS_TASK_CHANGE))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needDeal)) {
            nextDeal(processCallBackFailDTO);
            return;
        }
        // send mq
        needDeal.forEach(item -> {
            JSONObject msg = DingHandleEventFactory.buildProcessCallBackFailEvent(item);
            DingdingCallBackBO dingdingCallBackBO = new DingdingCallBackBO();
            dingdingCallBackBO.setDecryptData(msg);

            MQData mqData = new MQData();
            mqData.setType(MType.DINGDING_PROCESS_CALL_BACK.name());
            mqData.setData(dingdingCallBackBO);
            log.info("查询失败列表发送钉钉回调消息数据:{}", JSON.toJSONString(mqData));
            mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
        });
        nextDeal(processCallBackFailDTO);
    }

    private void nextDeal(ProcessCallBackFailDTO processCallBackFailDTO) {
        // 没后续消息直接返回
        if (!Objects.equals(Boolean.TRUE, processCallBackFailDTO.getHasMore())) {
            log.info("已没有后续钉钉回调失败的消息,本次拉取结束:{}", processCallBackFailDTO.getHasMore());
            return;
        }
        // 如果还有未拉取完的消息，则继续拉取
        ProcessCallBackFailDTO processCallBackFailDTO1 = DingTalkUtils.pullProcessCallBackFailList();
        if (Objects.isNull(processCallBackFailDTO1)) {
            return;
        }
        redisUtil.set(RedisKeys.buildDingCallBackInfoKey(), JSON.toJSONString(processCallBackFailDTO), ONE_HOURS);
        dealCallBackFail(processCallBackFailDTO1);
    }
}
