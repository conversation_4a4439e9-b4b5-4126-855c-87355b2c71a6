package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CycleInventoryCost;
import net.summerfarm.model.domain.StoreRecord;
import net.summerfarm.model.vo.StoreRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CycleInventoryCostMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CycleInventoryCost record);

    int insertSelective(CycleInventoryCost record);

    CycleInventoryCost selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CycleInventoryCost record);

    int updateByPrimaryKey(CycleInventoryCost record);

    /**
     * 获取周期库存成本
     * @param sku sku
     * @param warehouseNo 库存仓no
     * @return 周期库存成本
     */
    CycleInventoryCost selectBySku(@Param("sku") String sku, @Param("warehouseNo") Integer warehouseNo);

    void insertInitialization();

    void updateByStoreRecord(@Param("storeRecordList") List<StoreRecord> storeRecordList);

    void updateByStoreSku(@Param("storeRecordVOS") List<StoreRecordVO> storeRecordVOS);

    /**
     * 按照库存记录更新周期成本
     * @param storeRecordVO 库存记录
     */
    void update(StoreRecordVO storeRecordVO);
}