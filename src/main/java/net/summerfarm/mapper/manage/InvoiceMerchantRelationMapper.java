package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.InvoiceConfig;
import net.summerfarm.model.domain.InvoiceMerchantRelation;
import net.summerfarm.model.input.InvoiceMerchantInput;
import net.summerfarm.model.vo.InvoiceMerchantRelationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InvoiceMerchantRelationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(InvoiceMerchantRelation record);

    int insertSelective(InvoiceMerchantRelation record);

    /**
     * 根据MId查询用户发票抬头关联
     * @param mId
     * @return
     */
    List<InvoiceConfig> selectByMId(Long mId);

    int updateByPrimaryKeySelective(InvoiceMerchantRelation record);

    int updateByPrimaryKey(InvoiceMerchantRelation record);

    /**
     * 根据id得到对应的门店关联数
     * @param id
     * @return
     */
    int selectCount(Long id);

    /**
     * 删除 invoiceId 相关联的记录
     * @param invoiceId
     */
    void deleteByInvoiceId(Long invoiceId);

    /**
     * 根据mid删除关联关系
     * @param mId
     */
    void deleteMerchantId(Long mId);

    /**
     * 获取adminId下的所有的门店信息
     * @param invoiceMerchantInput
     * @return
     */
    List<InvoiceMerchantRelationVO> selectAllMerchant(InvoiceMerchantInput invoiceMerchantInput);

    /**
     * 根据adminId获取映射关系
     * @param adminId
     * @return
     */
    List<InvoiceMerchantRelationVO> selectByAdminId(@Param("adminId") Integer adminId);

    /**
     * 将对应的list删除
     * @param deleteList
     */
    void deleteByMerchantIds(List<Long> deleteList);

    /**
     * 根据入参获取
     * @param invoiceMerchantRelationVO
     * @return
     */
    List<InvoiceMerchantRelationVO> selectBySelectKeys(InvoiceMerchantRelationVO invoiceMerchantRelationVO);

    /**
     * 大客户对应的抬头与门店的链接关系
     *
     * @param invoiceId  取自invoice_config表内(admin的抬头)
     * @param merchantId 取自merchant表中m_id
     * @return int
     */
    int insertNew(@Param("invoiceId") Long invoiceId, @Param("merchantId") Long merchantId);

    /**
     * 检测品牌中有多少不重复的发票信息（大客户对应的抬头与门店的链接关系表）
     * @return 品牌对应的抬头与税号
     */
    List<InvoiceMerchantRelationVO> selectHistory();

    /**
     * 检测品牌中有多少的发票信息（大客户对应的抬头与门店的链接关系表）
     * @return
     */
    List<InvoiceMerchantRelationVO> selectAllHistory();

    /**
     * 根据invoice_config的id查询关联门店的信息
     *
     * @param invoiceId
     * @return
     */
    List<InvoiceMerchantRelation> selectAll(@Param("invoiceId") Long invoiceId);

    /**
     * 查询门店大客户关系
     *
     * @param mIdList   门店 Id列表
     * @param invoiceId 发票编号
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> listByMIdAndInvoiceId(@Param("mIdList") List<Integer> mIdList,@Param("invoiceId")Integer invoiceId);

    /**
     * 批量新增
     *
     * @param mIdList   id列表
     * @param invoiceId 发票编号
     */
    void insertBatch(@Param("mIdList") List<Integer> mIdList,@Param("invoiceId")Integer invoiceId);

    /**
     * 批量删除
     *
     * @param mIdList   id列表
     * @param invoiceId 发票编号
     */
    void delBatch(@Param("mIdList") List<Integer> mIdList,@Param("invoiceId")Integer invoiceId);

}