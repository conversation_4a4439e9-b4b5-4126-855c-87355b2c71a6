package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.domain.market.ActivitySkuDetail;
import net.summerfarm.model.vo.ActivitySkuDetailValueObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivitySkuDetailMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insertSelective(ActivitySkuDetail record);

    
    ActivitySkuDetail selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(ActivitySkuDetail record);


    int insertBatch(@Param("list") List<ActivitySkuDetail> list, @Param("itemConfigId") Long itemConfigId);

    ActivitySkuDetail selectBySku(@Param("itemConfigId") Long itemConfigId, @Param("sku") String sku);

    List<ActivitySkuDetail> selectByItemConfig(@Param("itemConfigId") Long itemConfigId);

    int countByItemConfig(@Param("itemConfigId") Long itemConfigId);

    int updateDelFlag(@Param("itemConfigId") Long itemConfigId, @Param("sku") String sku);

    List<ActivitySkuDetail> listByItemConfigs(@Param("list") List<Long> list, @Param("sku") String sku);

    List<ActivitySkuDetailValueObject> listDetailByItemConfigs(@Param("list") List<Long> list, @Param("sku") String sku);

    List<ActivitySkuDetail> listByItemConfigsSkus(@Param("configList") List<Long> list, @Param("skus") List<String> skus);

    int updateDelFlagBatch(@Param("ids")List<Long> ids);

    int deleteSkuByConfigId(@Param("itemConfigId") Long itemConfigId, @Param("sku") String sku);

    List<ActivitySkuDetail> listByBasicInfoIds(@Param("list") List<Long> basicInfoIds, @Param("sku") String sku);


    List<ActivitySkuDetail> listAllByBasicInfoIds(@Param("list") List<Long> basicInfoIds);

    int updateBatch(@Param("list") List<ActivitySkuDetail> activitySkuDetails);

    List<String> listSkuByItemConfigIdStrings(@Param("list") List<Long> itemConfigIds);
}