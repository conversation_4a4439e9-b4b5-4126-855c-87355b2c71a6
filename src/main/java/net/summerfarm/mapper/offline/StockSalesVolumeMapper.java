package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.StockSkuStatistics;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/2/16
 */
@Repository
public interface StockSalesVolumeMapper {

   // 查询库存仓销量
   List<StockSkuStatistics> select(@Param("warehouseNo") Integer warehouseNo, @Param("startTime") Integer startTime, @Param("endTime") Integer endTime);

   // 查询售罄率，周转率
   List<StockSkuStatistics> selectByDateAndSku(@Param("warehouseNo") Integer warehouseNo, @Param("skus")List<String> skus);

   // 查询sku销量
   StockSkuStatistics selectBySku(@Param("warehouseNo") Integer warehouseNo, @Param("startTime") Integer startTime, @Param("endTime") Integer endTime, @Param("sku") String sku);

   // 查询sku销量
   StockSkuStatistics selectBySkuAndWerhouseNo(@Param("warehouseNo") Integer warehouseNo, @Param("sku") String sku);
}
