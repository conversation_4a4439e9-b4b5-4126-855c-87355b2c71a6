package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.crm.CrmBdOrg;
import org.apache.ibatis.annotations.Param;

public interface CrmBdOrgMapper {

    CrmBdOrg selectById(@Param("id") Long id);

    /**
     * 根据城市和区域获取销售rank职级的adminId
     *
     * @param city
     * @param area
     * @return
     */
    Long getAdminIdByCityAndAreaAndRank(@Param("city") String city, @Param("area") String area, @Param("rank") Integer rank);


    /**
     * 查询指定级别的销售
     *
     * @param bdId 销售id
     * @param rank 级别
     * @return {@link CrmBdOrg}
     */
    CrmBdOrg selectOneByBdIdAndRank(@Param("bdId")Long bdId,@Param("rank")Integer rank);




}