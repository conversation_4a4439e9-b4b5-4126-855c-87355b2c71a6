package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.wms.StockTaskWaveSkuOccupyDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * @Date 2023/5/22 10:09
 * @<AUTHOR>
 */
public interface StockTaskWaveSkuOccupyMapper {

    int insert(StockTaskWaveSkuOccupyDO record);

    void insertList(@Param("recordList") List<StockTaskWaveSkuOccupyDO> recordList);

    List<StockTaskWaveSkuOccupyDO> selectListByStockTaskId(@Param("stockTaskId") Integer stockTaskId,
                                                               @Param("skuList") List<String> skuList);

    int updatePickChange(@Param("id") Long id,
                         @Param("remainOccupyQuantityReduce") Integer remainOccupyQuantityReduce,
                         @Param("remainNotOccupyQuantityReduce") Integer remainNotOccupyQuantityReduce);

}
