package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SupplierContract;
import net.summerfarm.model.input.SupplierContractReq;
import net.summerfarm.model.input.SupplierReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SupplierContractMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SupplierContract record);

    int insertSelective(SupplierContract record);

    SupplierContract selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SupplierContract record);

    int updateByPrimaryKey(SupplierContract record);

    List<SupplierContractReq> selectAuditByType(SupplierReq supplierReq);

    void expireAllCurrentContract(Integer supplierId);

    void batchInvalid(@Param("list") List<Long> list);

    void expireContract();
}