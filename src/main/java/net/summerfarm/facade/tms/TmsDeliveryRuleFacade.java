package net.summerfarm.facade.tms;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.facade.tms.converter.DeliveryRuleConverter;
import net.summerfarm.facade.tms.input.DeliveryRuleQueryInput;
import net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider;
import net.summerfarm.wnc.client.resp.DeliveryRuleResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/10 10:42<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class TmsDeliveryRuleFacade {

    @DubboReference
    DeliveryRuleQueryProvider deliveryRuleQueryProvider;

    /**
     * 查询可配送的日期
     * @param deliveryRuleQueryInput 条件
     * @return 结果
     */
    public LocalDate queryCloudDeliveryDate(DeliveryRuleQueryInput deliveryRuleQueryInput){
        log.info("请求tms获取配送日期参数:{}",JSON.toJSONString(deliveryRuleQueryInput));
        DubboResponse<DeliveryRuleResp> resp = deliveryRuleQueryProvider
                .queryDeliveryDateInfo(DeliveryRuleConverter.input2Req(deliveryRuleQueryInput));
        if(null == resp || !DubboResponse.COMMON_SUCCESS_CODE.equals(resp.getCode())){
            throw new ProviderException(resp == null ? ProviderErrorCode.DEFAULT_CODE : resp.getMsg());
        }
        if(resp.getData() == null ||CollectionUtils.isEmpty(resp.getData().getDeliveryTimes())){
            throw new BizException("没有合适的配送日期");
        }
        return resp.getData().getDeliveryTimes().get(0);
    }

    /**
     * 查询可配送的日期
     * @param deliveryRuleQueryInput 条件
     * @return 结果
     */
    public List<LocalDate> queryBetweenCloudDeliveryDate(DeliveryRuleQueryInput deliveryRuleQueryInput){
        DubboResponse<DeliveryRuleResp> resp = deliveryRuleQueryProvider
                .queryDeliveryDateInfo(DeliveryRuleConverter.input2Req(deliveryRuleQueryInput));
        if(null == resp || !DubboResponse.COMMON_SUCCESS_CODE.equals(resp.getCode())){
            throw new ProviderException(resp == null ? ProviderErrorCode.DEFAULT_CODE : resp.getMsg());
        }
        if(resp.getData() == null || CollectionUtils.isEmpty(resp.getData().getDeliveryTimes())){
            throw new BizException("没有合适的配送日期");
        }
        return resp.getData().getDeliveryTimes();
    }
}
