package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CityGroup;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CityGroupMapper {

    /**
     * 城市组列表
     * @return 列表
     */
    List<CityGroup> list();

    /**
     * 保存城市组
     * @param cityGroup 城市组对象
     */
    void insert(CityGroup cityGroup);

    /**
     * 更新城市组
     * @param cityGroup 城市组对象
     */
    void update(CityGroup cityGroup);

    /**
     * 删除城市组
     * @param id id
     */
    void deleteById(Integer id);
}
