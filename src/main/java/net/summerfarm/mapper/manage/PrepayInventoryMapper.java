package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PrepayInventory;
import net.summerfarm.model.vo.PrepayInventoryVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface PrepayInventoryMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PrepayInventory record);

    int insertSelective(PrepayInventory record);

    PrepayInventory selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PrepayInventory record);

    int updateByPrimaryKey(PrepayInventory record);

    int updateByIdWithDeliveryCycle(PrepayInventory record);

    List<PrepayInventoryVO> selectByAdminId(@Param("adminId") Integer adminId, @Param("status") Integer status, @Param("sku") String sku, @Param("realname") String realname, @Param("statusArr") Integer[] statusArr);

    List<PrepayInventory> unUseQuantity();

    Integer unUseSkuQuantity(String sku);

    /**
     * 查询指定日期过期的预付
     * @param calcDate 到期日期
     * @return
     */
    List<PrepayInventory> selectExpireAtDate(LocalDate calcDate);

    /**
     * 查询生效中的商品
     * @param adminId
     * @param sku
     * @return
     */
    List<PrepayInventory> selectInEffectList(@Param("adminId") Integer adminId, @Param("sku") String sku);

    /**
     * 更新已用总量
     * @param record
     * @param amount
     * @return
     */
    int updateUsedAmount(@Param("record")PrepayInventory record,@Param("amount")Integer amount);

    /**
     * 根据ID批量查询
     * @param list
     * @return
     */
    List<PrepayInventory> selectPrepayInventoryListById(@Param("list") List<Integer> list);

}