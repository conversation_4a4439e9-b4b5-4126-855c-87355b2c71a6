<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.ContactMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.Contact">
        <id column="contact_id" property="contactId" jdbcType="BIGINT"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="contact" property="contact" jdbcType="VARCHAR"/>
        <result column="position" property="position" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="BIT"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="weixincode" property="weixincode" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="delivery_car" property="deliveryCar" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="is_default" property="isDefault" jdbcType="INTEGER"/>
        <result column="poi_note" property="poiNote" jdbcType="VARCHAR" />
        <result column="distance" property="distance" jdbcType="DECIMAL" />
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="house_number" property="houseNumber"/>
        <result column="store_no" property="storeNo"/>
        <result column="delivery_frequent" property="deliveryFrequent"/>
        <result column="delivery_rule" property="deliveryRule" jdbcType="VARCHAR"/>
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL"/>
        <result column="address_remark" property="addressRemark" jdbcType="VARCHAR"/>

    </resultMap>

    <resultMap id="EsResultMap" type="net.summerfarm.common.util.es.dto.EsContactDTO">
        <id column="contact_id" property="contactId" jdbcType="BIGINT"/>
        <result column="contact" property="contact" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="poi_note" property="poiNote" jdbcType="VARCHAR" />
        <result column="house_number" property="houseNumber"/>
    </resultMap>


    <sql id="Base_Column_List">
    contact_id, m_id, contact, position, gender, phone,
     email, weixincode,province,city,area,address,status,remark,is_default,delivery_car,poi_note,distance,path,house_number,store_no,
    delivery_frequent, delivery_rule, delivery_fee,address_remark
  </sql>

    <select id="select" resultMap="BaseResultMap" parameterType="net.summerfarm.model.domain.Contact">
        select
        <include refid="Base_Column_List"/>
        from contact
        <where>
            <if test="mId !=null">
                AND m_id = #{mId,jdbcType=BIGINT}
            </if>

            <if test="status !=null">
                AND status = #{status}
            </if>
            <if test="storeNo != null">
                AND store_no = #{storeNo}
            </if>
        </where>
    </select>


    <insert id="insertSelective" parameterType="net.summerfarm.model.domain.Contact" keyColumn="contact_id" keyProperty="contactId" useGeneratedKeys="true">
        insert into contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                m_id,
            </if>
            <if test="contact != null">
                contact,
            </if>
            <if test="position != null">
                position,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="weixincode != null">
                weixincode,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="isDefault != null">
                is_default,
            </if>
            <if test="poiNote != null">
                poi_note,
            </if>
            <if test="distance != null">
                distance,
            </if>
            <if test="houseNumber != null">
                house_number ,
            </if>
            <if test="storeNo != null">
                store_no ,
            </if>
            <if test="deliveryFrequent != null">
                delivery_frequent,
            </if>
            <if test="deliveryRule != null">
                delivery_rule,
            </if>
            <if test="deliveryFee != null">
                delivery_fee,
            </if>
            <if test="addressRemark != null">
                address_remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="mId != null">
                #{mId,jdbcType=BIGINT},
            </if>
            <if test="contact != null">
                #{contact,jdbcType=VARCHAR},
            </if>
            <if test="position != null">
                #{position,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=BIT},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="weixincode != null">
                #{weixincode,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province},
            </if>
            <if test="city != null">
                #{city},
            </if>
            <if test="area != null">
                #{area},
            </if>
            <if test="address != null">
                #{address},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="isDefault != null">
                #{isDefault},
            </if>
            <if test="poiNote != null">
                #{poiNote},
            </if>
            <if test="distance != null">
                #{distance},
            </if>
            <if test="houseNumber != null">
                #{houseNumber},
            </if>
            <if test="storeNo != null">
                #{storeNo} ,
            </if>
            <if test="deliveryFrequent != null">
                #{deliveryFrequent},
            </if>
            <if test="deliveryRule != null">
                #{deliveryRule},
            </if>
            <if test="deliveryFee != null">
                #{deliveryFee},
            </if>
            <if test="addressRemark != null">
                #{addressRemark},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.Contact">
        update contact
        <set>
            <if test="contact != null">
                contact = #{contact,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                province=#{province},
            </if>
            <if test="city != null">
                city=#{city},
            </if>
            <if test="area != null">
                area=#{area},
            </if>
            <if test="address != null">
                address=#{address},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
            <if test="remark != null">
                remark=#{remark},
            </if>
            <if test="deliveryCar != null">
                delivery_car=#{deliveryCar},
            </if>
            <if test="isDefault != null">
                is_default=#{isDefault},
            </if>
            <if test="poiNote != null">
                poi_note = #{poiNote},
            </if>
            <if test="distance != null">
                distance = #{distance},
            </if>
            <if test="path != null">
                path = #{path},
            </if>
            <if test="houseNumber != null">
                house_number =#{houseNumber} ,
            </if>
            <if test="storeNo != null">
                store_no =#{storeNo} ,
            </if>
            <if test="deliveryFrequent !=null">
                delivery_frequent = #{deliveryFrequent},
            </if>
            <if test="deliveryRule != null">
                delivery_rule = #{deliveryRule},
            </if>
            <if test="deliveryFee != null">
                delivery_fee = #{deliveryFee},
            </if>
            <if test="addressRemark != null">
                address_remark = #{addressRemark},
            </if>
            <if test="appointStoreNoFlag != null">
                appoint_store_no_flag = #{appointStoreNoFlag},
            </if>
        </set>
        where contact_id = #{contactId,jdbcType=BIGINT}
    </update>

    <update id="updatePath">
        UPDATE contact
        SET path = NULL
        WHERE contact_id = #{contactId,jdbcType=BIGINT}
    </update>

    <select id="selectVOList" resultType="net.summerfarm.model.vo.ContactVO">
        SELECT c.contact_id contactId,m.mname,c.contact,c.phone,CONCAT(c.province,c.city,c.area,c.address) address,
        c.path,COUNT(1) times,c.store_no storeNo,address_remark addressRemark
        FROM merchant m
        INNER JOIN orders o ON o.m_id=m.m_id
        INNER JOIN delivery_plan dp ON o.order_no=dp.order_no
        INNER JOIN (
            SELECT dp.id
            FROM delivery_plan dp
            WHERE dp.`status` IN (2,3,6)
            <if test="deliveryStartDate != null">
                AND dp.delivery_time <![CDATA[>=]]> #{deliveryStartDate}
            </if>
            <if test="deliveryEndDate != null">
                AND dp.delivery_time <![CDATA[<=]]> #{deliveryEndDate}
            </if>
            <if test="storeNo != null">
                AND dp.order_store_no = #{storeNo}
            </if>
            GROUP BY dp.delivery_time,dp.contact_id
        ) t ON dp.id=t.id
        INNER JOIN contact c ON dp.contact_id=c.contact_id
        GROUP BY c.contact_id
        <if test="times != null">
            HAVING times >= #{times}
        </if>
        ORDER BY times DESC
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="net.summerfarm.model.domain.Contact">
        SELECT contact_id contactId,m_id mId,contact,position,gender,phone,email,weixincode,province,city,area,address,delivery_car deliveryCar,status,
              remark,is_default isDefault,poi_note poiNote,distance,path,store_no storeNo,address_remark addressRemark
        FROM contact
        WHERE contact_id = #{contactId,jdbcType=BIGINT}
    </select>
    <update id="changeContactMerchant">
        update contact set m_id = #{newMid},is_default = 0 where m_id = #{oldMid}
    </update>
    <select id="selectEsContactByMId" resultMap="EsResultMap">
        select
            contact_id, contact, phone,province,city,area,address,status,poi_note,house_number
        from contact where m_id = #{mId} and status = 1
    </select>

    <select id="selectAllByMId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from contact where m_id = #{mId}
    </select>


    <select id="selectByMid"  resultType="net.summerfarm.model.domain.Contact" >
        SELECT c.contact_id contactId, c.contact, c.phone,c.province,c.city,c.area,c.address,c.status,
        c.is_default isDefault,c.remark,c.store_no storeNo,address_remark addressRemark
        FROM contact c
        WHERE c.m_id = #{mId,jdbcType=BIGINT}
        <if test="status !=null">
            and c.status=#{status}
        </if>
    </select>
    <select id="selectSimilar" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from contact where contact_id &lt;&gt; #{contactId}
        and city = #{city} and area = #{area} and delivery_frequent &lt;&gt; #{deliveryFrequent}
        and status = 1
    </select>
    <select id="selectByAreaDelivery" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from contact where
        status = 1 and area = #{area} and city = #{city}
        and
        <foreach collection="notExist" separator="or" item="item" open="(" close=")">
            delivery_frequent like  CONCAT('%',#{item},'%')
        </foreach>

    </select>

    <update id="updateContact" parameterType="net.summerfarm.model.input.FenceInput">
        update contact set store_no = #{changeToStoreNo} , acm_id = #{acmId}
        where  city = #{city}
        <if test ="area != null">
            and  area =#{area}
        </if>
    </update>

    <select id="selectContacts" parameterType="java.lang.Long" resultType="net.summerfarm.model.domain.Contact">
        SELECT contact_id contactId,m_id mId,store_no storeNo,contact,phone,province,city,area,address,
        house_number houseNumber,address_remark addressRemark
        FROM contact
        WHERE status = 1 AND m_id IN
        <foreach collection="midSet" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectById" resultType="net.summerfarm.model.vo.ContactVO">
        SELECT contact_id contactId,m_id mId,contact,position,gender,phone,email,weixincode,province,city,area,address,delivery_car deliveryCar,status,
               remark,is_default isDefault,poi_note poiNote,distance,path,store_no storeNo,delivery_frequent deliveryFrequent,delivery_rule deliveryRule,
               delivery_fee deliveryFee,address_remark addressRemark
        FROM contact
        WHERE contact_id = #{contactId,jdbcType=BIGINT}
    </select>

    <select id="selectContactName" parameterType="long" resultType="net.summerfarm.model.DTO.ContactNameDTO">
        SELECT contact_id contactId, c.phone, m.mname mName,c.poi_note poiNote,c.store_no storeNo, m.area_no areaNo,address_remark addressRemark
        FROM contact c
        LEFT JOIN merchant m ON c.m_id = m.m_id
        WHERE c.contact_id = #{contactId}
    </select>

    <select id="selectDeliveryFrequent" resultType="net.summerfarm.model.vo.ContactVO">
        SELECT distinct
        c.m_id mId,
        m.mname,
        c.phone
        FROM
        wnc_contact_delivery_rule w
        INNER JOIN contact c ON w.out_business_no = c.contact_id
        INNER JOIN merchant m ON c.m_id = m.m_id
        where w.system_source=0 and c.`status`=1
        <if test="provinceList !=null and provinceList.size>0">
            and c.province in
            <foreach collection="provinceList" open="(" close=")" separator="," item="province">
                #{province}
            </foreach>
        </if>
        <if test="cityList !=null and cityList.size>0">
            and c.city in
            <foreach collection="cityList" open="(" close=")" separator="," item="city">
                #{city}
            </foreach>
        </if>
        <if test="areaList !=null and areaList.size>0">
            and c.area in
            <foreach collection="areaList" open="(" close=")" separator="," item="area">
                #{area}
            </foreach>
        </if>
    </select>
    <update id="updateContactStoreNo" >
        update contact c set c.store_no = #{newStoreNo}
        where c.city = #{city}
        and  c.area =#{area}
        and exists (select m.m_id from merchant m where m.m_id = c.m_id and m.business_line = #{businessLine})

    </update>

    <select id="selectAllDeLiveryFee" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM `contact` WHERE  `delivery_fee` IS NOT NULL and `status` = 1;
    </select>

    <select id="selectIsDefaultByMid" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from contact where status = 1  and m_id = #{mId} order by  is_default desc  limit 1
    </select>
</mapper>