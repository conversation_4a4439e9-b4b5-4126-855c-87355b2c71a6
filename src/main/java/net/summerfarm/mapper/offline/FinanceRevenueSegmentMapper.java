package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.FinanceRevenueSegment;
import net.summerfarm.model.vo.FinanceRevenueSegmentVO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface FinanceRevenueSegmentMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinanceRevenueSegment record);

    int insertSelective(FinanceRevenueSegment record);

    /**
     * 查询账期预估收入
     * @param endTime
     * @return
     */
    FinanceRevenueSegment selectByEndTime(LocalDate endTime);

    int updateByPrimaryKeySelective(FinanceRevenueSegment record);

    int updateByPrimaryKey(FinanceRevenueSegment record);

    /**
     * 查看收入模块信息
     * @param startTime
     * @param endTime
     * @return
     */
    FinanceRevenueSegment selectAll(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 收入确认额趋势
     * @param startTime
     * @param endTime
     * @return
     */
    List<FinanceRevenueSegmentVO> select(LocalDateTime startTime, LocalDateTime endTime);

}