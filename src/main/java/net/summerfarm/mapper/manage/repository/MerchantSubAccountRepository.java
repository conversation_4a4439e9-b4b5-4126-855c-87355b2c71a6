package net.summerfarm.mapper.manage.repository;

import net.summerfarm.facade.auth.AuthUserQueryFacade;
import net.summerfarm.mapper.manage.MerchantSubAccountMapper;
import net.summerfarm.model.vo.MerchantMergeVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
public class MerchantSubAccountRepository {
    @Resource
    MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    AuthUserQueryFacade authUserQueryFacade;

    public List<MerchantMergeVO> selectMerchantMerge(String mname, String phone, Integer bdId) {
        List<Long> userBaseIdBySourceRoleIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(Collections.singletonList(5));
        return merchantSubAccountMapper.selectMerchantMerge(mname, phone, bdId, userBaseIdBySourceRoleIds);
    }

    public List<MerchantMergeVO> selectMergeDetail(Long mId) {
        List<Long> userBaseIdBySourceRoleIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(Collections.singletonList(5));
        return merchantSubAccountMapper.selectMergeDetail(mId, userBaseIdBySourceRoleIds);
    }

    /**
     * 查询待选商户信息
     *
     * @param vo vo
     * @return 商户信息
     */
    public List<MerchantMergeVO> selectMerchantInfo(MerchantMergeVO vo) {
        List<Long> userBaseIdBySourceRoleIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(Collections.singletonList(5));
        vo.setBaseUserIds(userBaseIdBySourceRoleIds);
        return merchantSubAccountMapper.selectMerchantInfo(vo);
    }


}
