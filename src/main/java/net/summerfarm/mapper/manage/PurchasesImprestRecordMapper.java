package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PurchasesImprest;
import net.summerfarm.model.domain.PurchasesImprestRecord;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PurchasesImprestRecordMapper {

    /**
     * 新建备用金额修改
     * @param purchasesImprestRecord
     * @return
    */
    int createPurchasesRecord(PurchasesImprestRecord purchasesImprestRecord);


    /**
    * 查询审核单
     * @return
    */
    List<PurchasesImprestRecord> queryRecord();

    /**
    * 根据id查询
     * @param id
     * @return
    */
    PurchasesImprestRecord queryById(Integer id);

    /**
     * 更新审核单
     * @param purchasesImprestRecord
     * @return
    */
    int updateRecord(PurchasesImprestRecord purchasesImprestRecord);

    /**
    * 查询审核单
     * @param purchasesImprestRecord
     * @return
    */
    List<PurchasesImprestRecord> queryByRecord(PurchasesImprestRecord purchasesImprestRecord);


}
