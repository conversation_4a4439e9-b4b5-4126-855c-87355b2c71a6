package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockAllocationConfigRecord;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2022/2/22
 */
@Repository
public interface StockAllocationConfigRecordMapper {

    /**
     * 新增调拨单配置
     * @param record
     */
    void insert(StockAllocationConfigRecord record);

    /**
     * 查询调拨配置信息
     * @param taskNo
     * @return
     */
    StockAllocationConfigRecord selectOne(String taskNo);
}
