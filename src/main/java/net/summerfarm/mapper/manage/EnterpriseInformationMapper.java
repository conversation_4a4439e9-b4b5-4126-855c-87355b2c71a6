package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.EnterpriseInformation;
import net.summerfarm.model.input.EnterpriseInput;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> 2021/11/17
 */
@Repository
public interface EnterpriseInformationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(EnterpriseInformation record);

    /**
     * 插入企业信息
     * @param record
     * @return
     */
    int insertSelective(EnterpriseInformation record);

    /**
     * 根据税号查询信息
     * @param id
     * @return
     */
    EnterpriseInformation selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(EnterpriseInformation record);

    int updateByPrimaryKey(EnterpriseInformation record);

    /**
     * 根据发票抬头，搜索数据库中企业的工商信息
     *
     * @param name 发票抬头
     * @return 企业信息
     */
    List<EnterpriseInformation> selectAll(String name);

    /**
     * 据发票抬头，搜索数据库中企业的工商信息
     * @param name
     * @return
     */
    List<EnterpriseInformation> selectName(String name);

    /**
     * 公司信息表查询
     *
     * @param name       工商名称
     * @param creditCode 统一社会信用代码
     * @return 公司信息
     */
    EnterpriseInformation select(@Param("name") String name, @Param("creditCode") String creditCode);

    List<EnterpriseInformation> selectByPage(EnterpriseInput selectKeys);

    int countByCreditCode(String creditCode);

    EnterpriseInformation selectByTaxNum(String taxNum);

}