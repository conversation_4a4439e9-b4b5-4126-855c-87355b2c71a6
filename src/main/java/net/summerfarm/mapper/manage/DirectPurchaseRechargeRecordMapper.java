package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DirectPurchaseRechargeRecord;
import net.summerfarm.model.input.ReceivableMerchantInput;
import net.summerfarm.model.vo.DirectPurchaseMerchantVO;
import net.summerfarm.model.vo.DirectPurchaseRechargeRecordVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Date: 2020/11/17 10:29
 * @Author: <EMAIL>
 */
@Repository
public interface DirectPurchaseRechargeRecordMapper {

    int insert(DirectPurchaseRechargeRecord record);

    int insertSelective(DirectPurchaseRechargeRecord record);

    DirectPurchaseRechargeRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DirectPurchaseRechargeRecord record);

    int updateByPrimaryKey(DirectPurchaseRechargeRecord record);

    /**
     * @Description: 查询某次充值对应的扣款记录（不包括已取消订单的扣款)
     * @Param: rechargeId, recordType
     * @Return: java.util.List<net.summerfarm.model.vo.DirectPurchaseRechargeRecordVO>
     * @Date: 2020/11/18 11:08
     * @Author: <EMAIL>
     */
    List<DirectPurchaseRechargeRecordVO> selectByKeys(DirectPurchaseRechargeRecord record);

    /**
     * 根据输入的list<Long>Id,查询出完整的记录
     * @param idList
     * @return
     */
    List<DirectPurchaseRechargeRecord> selectByIds(List<Long> idList);

    /**
     * 根据条件筛选出客户信息
     * @param selectKeys
     * @return
     */
    List<DirectPurchaseMerchantVO> selectAllMerchant(ReceivableMerchantInput selectKeys);
}