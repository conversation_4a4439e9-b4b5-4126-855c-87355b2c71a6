package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MerchantLabel;
import net.summerfarm.model.domain.MerchantLabelCorrelaion;
import net.summerfarm.model.input.MerchantLabelReq;
import net.summerfarm.model.vo.MerchantLabelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MerchantLabelMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MerchantLabel record);

    int insertSelective(MerchantLabel record);

    MerchantLabel selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantLabel record);

    int updateByPrimaryKey(MerchantLabel record);

    List<MerchantLabel> selectListByEntity(MerchantLabel merchantLabel);

    List<MerchantLabel> batchInfoById(@Param("ids") List<Long> labelIdList);

    List<MerchantLabelVO> getListVO(MerchantLabelReq merchantLabelReq);

    /**
     * 查询门店绑定标签
     *
     * @param mId m id
     * @param name
     * @return {@link List}<{@link MerchantLabel}>
     */
    List<String> selectNameByMid(@Param("mId")Long mId);
}