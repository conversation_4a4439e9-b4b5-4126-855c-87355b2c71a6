package net.summerfarm.mapper.offline;

import java.util.List;
import net.summerfarm.model.DTO.DynamicPriceStatisticsQueryDTO;
import net.summerfarm.model.domain.offline.DynamicPriceStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DynamicPriceStatisticsMapper {
    
    int deleteByPrimaryKey(Long id);

    int insert(DynamicPriceStatistics record);
    
    int insertSelective(DynamicPriceStatistics record);

    DynamicPriceStatistics selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DynamicPriceStatistics record);
    
    int updateByPrimaryKey(DynamicPriceStatistics record);

    List<DynamicPriceStatistics> selectByQuery(DynamicPriceStatisticsQueryDTO queryDTO);


}