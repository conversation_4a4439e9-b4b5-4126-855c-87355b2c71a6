package net.summerfarm.data.dto;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TransferSyncDTO implements Serializable {

    private static final long serialVersionUID = -6406599498281848801L;

    List<Long> ids;

    Long id;

    Long beginId;

    Long maxId;

    Integer type;
}
