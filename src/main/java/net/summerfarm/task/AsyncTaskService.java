package net.summerfarm.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.javaparser.utils.Log;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.HttpUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.*;
import net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.OuterPlatformRes;
import net.summerfarm.model.vo.*;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.mq.ofc.input.ShortOutItemEntityInput;
import net.summerfarm.mq.ofc.input.ShortOutOrderEntityInput;
import net.summerfarm.service.*;
import net.summerfarm.task.quartz.JobManage;
import net.summerfarm.tms.message.DeliveryItemMessage;
import net.summerfarm.tms.message.DeliveryOrderMessage;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Package: net.summerfarm.task
 * @Description: 异步任务
 * @author: <EMAIL>
 * @Date: 2017/5/17
 */
@Component
@Async("asycExecutor")
public class AsyncTaskService {

    @Resource
    private AreaMapper areaMapper;
    @Lazy
    @Resource
    private PurchasesConfigService purchasesConfigService;
    @Resource
    private MerchantMapper merchantMapper;
    @Lazy
    @Resource
    private MerchantService merchantService;
    @Lazy
    @Resource
    private RamUserService ramUserService;
    @Resource
    private CrmManageBdMapper crmManageBdMapper;
    @Resource
    private CrmManageAreaMapper crmManageAreaMapper;
    @Resource
    private DingTalkService dingTalkService;
    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    private CrmNewsService crmNewsService;
    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private MsgAdminService msgAdminService;
    @Resource
    private MerchantOuterMapper merchantOuterMapper;

    @Resource
    private OuterPlatformMapper outerPlatformMapper;
    @Resource
    private OrderOuterInfoMapper orderOuterInfoMapper;
    @Resource
    private JobManage jobManage;
    @Resource
    private OuterPlatformPushRecordMapper outerPlatformPushRecordMapper;

    @Autowired
    private DeliveryPlanMapper deliveryPlanMapper;

    @Autowired
    MqProducer mqProducer;
    /**
    * 省心送消息提醒
    */
    private static String MERCHANT_ONE_DATE = "感谢您的海涵，您原计划明天配送的，%s件%s库存不足，我们已为您尝试延期到至%s月%s日配送，您也可以尝试修改配送日期，或联系业务代表>\n";

    private static String BD_ONE_DATE = "您的客户【%s】，原计划%s月%s日配送的，%s件%s库存不足，系统已为客户延期至%s月%s日配送，建议立即跟进客情，也可在鲜沐后台，订单管理处，帮助客户修改配送日期。\n";

    private static String MERCHANT_TWO_DATE = "感谢您的海涵，您原计划%s月%s日配送的，%s件%s可能库存不够，明日我们将会继续为您努力备货，您也可以尝试修改配送日期，或联系业务代表>\n";

    private static String BD_TWO_DATE = "您的客户【%s】，原计划%s月%s日配送的，%s件%s可能库存不够，建议立即跟进客情，也可在鲜沐后台，订单管理处，帮助客户修改配送日期";

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 审核消息
     *
     * @throws IOException
     */
    public void sendMessage(Long mId) throws IOException {
        MQData mqData = new MQData(MType.registerapproved.name());
        mqData.setData(mId);
        mqProducer.send(RocketMqMessageConstant.MANAGE_LIST,null,JSON.toJSONString(mqData));
    }

    /**
     * 发送优惠券发放消息
     *
     * @param merchant
     * @param type
     * @param areaNo
     * @throws IOException
     */
    public void sendCouponAndPushMsg(Merchant merchant, String type, String money,
                                     LocalDateTime triggerTime, CouponSenderSetupSenderTypeEnum couponSenderSetupSenderTypeEnum, Integer areaNo) throws IOException {
        MQData mqData = new MQData(MType.coupon.name());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("mId", merchant.getmId());
        jsonObject.put("type", type);
        jsonObject.put("money",money);
        jsonObject.put("triggerTime", DateUtils.localDateTimeToString(triggerTime));
        jsonObject.put("senderType",  couponSenderSetupSenderTypeEnum.getType());
        jsonObject.put("areaNo", areaNo);
        jsonObject.put("size", merchant.getSize());
        jsonObject.put("adminId", merchant.getAdminId());
        mqData.setData(jsonObject);
        mqProducer.send(RocketMqMessageConstant.MANAGE_LIST,null,JSON.toJSONString(mqData));
    }

    /**
     * 刷新内存中品类数据
     */
    public void flushCategoryTree() {
        MQData mqData = new MQData(MType.flush.name());
        mqProducer.send(RocketMqMessageConstant.MANAGE_LIST,null,JSON.toJSONString(mqData));
    }

    /**
     * 省心送加冻结 (第二日配送且未加冻结的配送计划)
     */
    public void timingOrderLock(String sku, Integer storeNo){
        AreaStore areaStore = new AreaStore();
        areaStore.setSku(sku);
        areaStore.setAreaNo(storeNo);
        MQData mqData = new MQData(MType.TIMING_ORDER_LOCK_TWO.name());
        mqData.setData(areaStore);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
    }

    /**
     * 省心送加冻结
     * @param sku    sku
     * @param storeNo 需要进行操作的store
     * @param oldDate 之前的时间
     * @param newDate 新的时间
     *
     */
    public void switchTimingOrderLock(String sku, Integer storeNo, LocalDate oldDate ,LocalDate newDate,String type){
        MQData mqData = new MQData(MType.TIMING_ORDER_LOCK.name());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sku",sku);
        jsonObject.put("storeNo",storeNo);
        jsonObject.put("oldDate",oldDate);
        jsonObject.put("newDate",newDate);
        jsonObject.put("type",type);
        mqData.setData(jsonObject);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));

    }


    public void timingOrder(DeliveryPlanVO deliveryPlanVO,String name){
        MQData mqData = new MQData(name);
        mqData.setData(deliveryPlanVO);
        mqProducer.send(RocketMqMessageConstant.MANAGE_LIST,null,JSON.toJSONString(mqData));
    }

    public void updateAreaStatusMsg(Integer areaNo,String notifyTitle,String notifyContent,String notifyRemake){
        MQData mqData = new MQData(MType.UPDATE_AREA_STATUS.name());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("areaNo", areaNo);
        jsonObject.put("notifyTitle", notifyTitle);
        jsonObject.put("notifyContent",notifyContent);
        if(!StringUtils.isEmpty(notifyRemake)){
            jsonObject.put("notifyRemake",notifyRemake);
        }
        mqData.setData(jsonObject);
        mqProducer.send(RocketMqMessageConstant.MANAGE_LIST,null,JSON.toJSONString(mqData));
    }

    public void purchaserMsg(PurchasesConfig purchasesConfig){
        try {
            Thread.sleep(5000); //暂停5秒,等待商城事物提交
        } catch (InterruptedException e) {
            Log.error("采购预警消息发送失败，线程sleep异常");
        }
        if (!CollectionUtils.isEmpty(purchasesConfig.getSkus())) {
            Integer storeNo = purchasesConfig.getAreaNo();
            List<String> skus = purchasesConfig.getSkus().stream().distinct().collect(Collectors.toList());
            for (String sku : skus) {
                purchasesConfigService.msgArrival(storeNo, sku);
            }
        }
    }



    /**
     * 发送钉钉消息提醒 和用户推送
     * @param deliveryPlanVOList
     * @param type
     */
    public void sendTimingMsg(List<DeliveryPlanVO> deliveryPlanVOList,Integer type){

        LocalDate date = LocalDate.now().plusDays(1);
        int dateMonthValue = date.getMonthValue();
        int dateDayOfMonth = date.getDayOfMonth();
        //为空不处理
        if(CollectionUtils.isEmpty(deliveryPlanVOList)){
            return;
        }
        for (DeliveryPlanVO deliveryPlanVO : deliveryPlanVOList) {
            DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(deliveryPlanVO.getId());
            String merchantMsg;
            String bdMsg;
            String pdName = deliveryPlanVO.getPdName();
            Integer mId = deliveryPlanVO.getmId();
            Integer quantity = deliveryPlanVO.getQuantity();
            String mname = deliveryPlanVO.getMname();
            LocalDate oldDeliveryTime = deliveryPlanVO.getDeliveryTime();
            LocalDate newDeliveryTime = deliveryPlan.getDeliveryTime();
            logger.info("省心送订单定时任务发送消息信息 id >>> {}, orderNo >>> {}, oldDeliveryTime >>> {}, newDeliveryTime >>> {}",
                    deliveryPlanVO.getId(), deliveryPlanVO.getOrderNo(), oldDeliveryTime, newDeliveryTime);
            String mType;
            //获取发送消息信息
            if(Objects.equals(type,0)){
                merchantMsg = String.format(MERCHANT_ONE_DATE, quantity, pdName, newDeliveryTime.getMonthValue(), newDeliveryTime.getDayOfMonth());
                bdMsg = String.format(BD_ONE_DATE,mname,dateMonthValue,dateDayOfMonth,quantity,pdName, newDeliveryTime.getMonthValue(), newDeliveryTime.getDayOfMonth());
                mType = MType.TIMING_DELAY_TIME.name();
            } else {
                merchantMsg = String.format(MERCHANT_TWO_DATE, dateMonthValue, dateDayOfMonth, quantity, pdName);
                bdMsg = String.format(BD_TWO_DATE, mname, dateMonthValue, dateDayOfMonth, quantity, pdName);
                mType = MType.TIMING_LOCK_QUANTITY.name();
            }
            //获取区域负责人
            Integer adminId = deliveryPlanVO.getAdminId();
            CrmManageArea crmManageArea = crmManageAreaMapper.selectByAreaNO(deliveryPlanVO.getAreaNo());
            CrmManageBd crmManageBd = crmManageBdMapper.selectByPrimaryKey(crmManageArea.getManageAdminId());
            //发送给城市销售主管
            if(Objects.nonNull(crmManageBd) && !Objects.equals(crmManageBd.getManageAdminId(),adminId) && Objects.nonNull(adminId)){
                handleDeliveryVO(type,bdMsg,crmManageBd.getManageAdminId());
            }
            adminId = Objects.isNull(adminId) && Objects.nonNull(crmManageBd) ? crmManageBd.getManageAdminId() : adminId;


            //获取销售信息
            DingTalkMsgReceiverIdBO dingTalkMsgBO = new DingTalkMsgReceiverIdBO();
            dingTalkMsgBO.setReceiverIdList(Collections.singletonList(Long.valueOf(adminId)));
            dingTalkMsgBO.setContent(bdMsg);
            dingTalkMsgBO.setMsgType(DingTalkMsgTypeEnum.TXT.getType());
            dingTalkMsgSender.sendMessageWithFeiShu(dingTalkMsgBO);

            String time = newDeliveryTime.format(DateTimeFormatter.ofPattern("MM月dd日"));
            String orderTime = DateUtils.localDateTimeToString(deliveryPlanVO.getOrderTime());
            //发送消息
            MQData mqData = new MQData(mType);
            JSONObject js = new JSONObject();
            js.put("msg",merchantMsg);
            js.put("mId",mId);
            js.put("orderNo",deliveryPlanVO.getOrderNo());
            js.put("orderTime",orderTime);
            js.put("deliveryTime",time);
            mqData.setData(js);
            logger.info("merchantMsg:" + merchantMsg);
            mqProducer.send(RocketMqMessageConstant.MANAGE_LIST,null,JSON.toJSONString(mqData));
            handleDeliveryVO(type,bdMsg,adminId);
        }
        return;

    }

    /**
     * 插入CRM消息
     * @param type
     * @param msg
     */
    private void handleDeliveryVO(Integer type,String msg,Integer adminId){

        if(Objects.isNull(adminId)){
            return;
        }
        CrmNews crmNews = new CrmNews();
        String title = Objects.equals(type,0) ? "省心送延期配送通知" : "省心送冻结失败预警";
        Integer warnIngType = Objects.equals(type,0) ? CrmNewsWarningTypeEnum.VERY_URGENT.getType() : CrmNewsWarningTypeEnum.GENERAL_EMERGENCY.getType();
        crmNews.setContent(msg);
        crmNews.setTitle(title);
        crmNews.setType(1);
        crmNews.setStatus(0);
        crmNews.setWarningType(warnIngType);
        crmNews.setAdminId(adminId);
        crmNews.setCreator(adminId);
        crmNews.setUpdater(adminId);
        crmNewsService.insert(crmNews);
        return;
    }

    /**
     * 发送售罄消息
     * @param url
     * @param txt
     */
    public void sendDingTalkMsg(String url, String txt) {
        JSONObject jsonObject = JSONObject.parseObject(txt);
        String title = jsonObject.getString("title");
        String text = jsonObject.getString("text");
        HashMap<String, String> msgMap = new HashMap<>(2);
        msgMap.put("title", title);
        msgMap.put("text", text);
        DingTalkRobotUtil.sendMarkDownMsg(url, () -> msgMap, null);
    }

    public void sendDingTalkMsgToAdmin(Integer[] adminIdArr, String msg) {
        List<Long> dingUserIdList = new ArrayList<>();
        Arrays.stream(adminIdArr).forEach(adminId -> dingUserIdList.add(adminId.longValue()));
        if (CollectionUtils.isEmpty(dingUserIdList)) {
            logger.info("钉钉私信发送失败，用户不存在");
            return;
        }
        DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.TXT.getType(), null, msg);
        DingTalkMsgReceiverIdBO dingTalkMsgReceiverIdBO = new  DingTalkMsgReceiverIdBO(dingTalkMsgBO);
        dingTalkMsgReceiverIdBO.setReceiverIdList(dingUserIdList);
        dingTalkMsgSender.sendMessageWithFeiShu(dingTalkMsgReceiverIdBO);
    }

    /**
     *批量修改配送日期消息发送
     * @param isPlanDeliveryDateList
     * @param notPlanDeliveryDateList
     * @param cancelHeartList
     */
    @Async("asycExecutor")
    public void sendBatchUpdateDeliveryDateMesaageAndWeChat(ArrayList<BatchUpdateDeliveryDateVo> isPlanDeliveryDateList,
                                     ArrayList<BatchUpdateDeliveryDateVo> notPlanDeliveryDateList, ArrayList<DeliveryPlanHeartVo> cancelHeartList){
        if(!CollectionUtils.isEmpty(notPlanDeliveryDateList)){
            isPlanDeliveryDateList.addAll(notPlanDeliveryDateList);
        }
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("MM/dd");
        DateTimeFormatter wcDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        if (!CollectionUtils.isEmpty(isPlanDeliveryDateList)){
            for (BatchUpdateDeliveryDateVo batchUpdateDeliveryDateVo : isPlanDeliveryDateList) {
                //短信-订单延期
                String oldDeliveryTime = batchUpdateDeliveryDateVo.getDeliveryTime().format(dtf);
                String newDeliveryTime = batchUpdateDeliveryDateVo.getNewDeliveryTime().format(dtf);
                String managePhone = batchUpdateDeliveryDateVo.getPhone();
                try {
                    msgAdminService.sms(13L, Arrays.asList(oldDeliveryTime, newDeliveryTime,managePhone == null ? " ": managePhone), batchUpdateDeliveryDateVo.getSendPhone(), SMSType.NOTIFY);
                } catch (Exception e) {
                    logger.error(batchUpdateDeliveryDateVo.getOrderNo()+"订单延期短信发送失败",e);
                }
                //微信公众号推送-订单延期
                DeliveryPlanChangeWeChatVo deliveryPlanChangeWeChatVo = new DeliveryPlanChangeWeChatVo();
                deliveryPlanChangeWeChatVo
                        .setOpenid(batchUpdateDeliveryDateVo.getOpenid())
                        .setOrderNo(batchUpdateDeliveryDateVo.getOrderNo())
                        .setOldDliveryDates(batchUpdateDeliveryDateVo.getDeliveryTime().format(wcDtf))
                        .setNewDeliveryDate(batchUpdateDeliveryDateVo.getNewDeliveryTime().format(wcDtf));

                try {
                    sendWeChat(MType.BATCH_UPDATE_DEVLIVERY_DATE.name(),deliveryPlanChangeWeChatVo);
                } catch (Exception e) {
                    logger.error(batchUpdateDeliveryDateVo.getOrderNo()+"订单延期微信公众号发送失败",e);
                }
            }
        }

        if (!CollectionUtils.isEmpty(cancelHeartList)){
            for (DeliveryPlanHeartVo deliveryPlanHeartVo : cancelHeartList) {
                //短信-取消订单
                String deliveryTime = deliveryPlanHeartVo.getDeliveryTime().format(dtf);
                String managePhone = deliveryPlanHeartVo.getPhone();
                try {
                    msgAdminService.sms(14L, Arrays.asList(deliveryTime,managePhone == null ? " ": managePhone), deliveryPlanHeartVo.getSendPhone(), SMSType.NOTIFY);
                } catch (Exception e) {
                    logger.error(deliveryPlanHeartVo.getOrderNo()+"省心送订单取消短信发送失败",e);
                }
                //微信公众号推送-省心送订单取消
                DeliveryPlanCancelWeChatVo deliveryPlanCancelWeChatVo = new DeliveryPlanCancelWeChatVo();
                deliveryPlanCancelWeChatVo
                        .setReason("交通受阻")
                        .setOpenid(deliveryPlanHeartVo.getOpenid())
                        .setOrderNo(deliveryPlanHeartVo.getOrderNo())
                        .setCancelDate(LocalDate.now().format(wcDtf));
                try {
                    sendWeChat(MType.CANCEL_HEART_ORDER.name(),deliveryPlanCancelWeChatVo);
                } catch (Exception e) {
                    logger.error(deliveryPlanHeartVo.getOrderNo()+"省心送订单取消微信公众号发送失败",e);
                }
            }
        }

    }

    public void sendWeChat(String typeName,Object object){
        MQData mqData = new MQData(typeName);
        mqData.setData(object);
        mqProducer.send(RocketMqMessageConstant.MANAGE_LIST,null,JSON.toJSONString(mqData));
    }

    /**
     * 公众号缺货退款通知
     * ordersRevokeWeChatVo
     */
    @Async("asycExecutor")
    public void sendOutOfStockRefundWeChat(OrdersRevokeWeChatVo ordersRevokeWeChatVo) {
        if (StringUtils.isEmpty(ordersRevokeWeChatVo.getOrderTime()) || StringUtils.isEmpty(ordersRevokeWeChatVo.getPdName())) {
            logger.error("异常订单售后公众号缺货退款通知数据异常...");
            return;
        }
        //微信公众号推送-省心送订单取消
        try {
            sendWeChat(MType.ORDER_ABNORMAL_HANDLE.name(),ordersRevokeWeChatVo);
        } catch (Exception e) {
            logger.error(ordersRevokeWeChatVo.getOrderNo()+"公众号缺货退款通知发送失败",e);
        }
    }

    /**
     * 商城缺货微信推送
     */
    public  void sendShortWeChatMessage(String orderNo, Integer mId, AutoAfterSaleVO autoAfterSaleVO){
        MQData mqData = new MQData();
        mqData.setType(MType.DELIVERY_WECHANT_MSG.name());

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderNo",orderNo);
        jsonObject.put("shortSku",autoAfterSaleVO.getShortSkuMsg());
        jsonObject.put("mId",mId);
        jsonObject.put("shortSkuMsg",autoAfterSaleVO.getShortSkuMsgDetail());
        jsonObject.put("orderTime", DateUtils.localDateTimeToString(autoAfterSaleVO.getOrderTime()));
        mqData.setData(jsonObject);
        logger.info("商城缺货微信推送shortSku: {}",autoAfterSaleVO.getShortSkuMsg());
        mqProducer.send("manage-list",null,JSON.toJSONString(mqData));
    }

    /**
     * 商城 配送到货提醒（未缺货）
     */
    public  void sendWechantMessage(String orderNo,String signForMsg,Integer mId){
        MQData mqData = new MQData();
        mqData.setType(MType.DELIVERY_WECHANT_MSG.name());

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderNo",orderNo);
        jsonObject.put("msg",signForMsg);
        jsonObject.put("mId",mId);
        mqData.setData(jsonObject);
        logger.info("商城 配送到货提醒（未缺货）: {}",signForMsg);
        mqProducer.send("manage-list",null,JSON.toJSONString(mqData));
    }

    /**
     * 后台发送 配送到货提醒
     */
    public void sendDingTalkMsg(JSONObject jsonObject){
        MQData mqData = new MQData();
        mqData.setType(MType.DELIVERY_DINGTALK_MSG.name());
        mqData.setData(jsonObject);
        mqProducer.send("mall-list",null, JSON.toJSONString(mqData));
    }

    /**
     * 配送单缺货sku消息提醒
     *
     * @param text
     */
    public void sendShortSkuMsg(String text){
        logger.info("缺货sku提醒：{}", text);
        MQData mqData = new MQData();
        mqData.setType(MType.DELIVERY_SHORT_SKU.name());
        mqData.setData(text);
        mqProducer.send("mall-list",null,JSON.toJSONString(mqData));
    }

    /**
     * 外部对接推送订单收货通知
     *
     * @param deliveryOrderItemList 发货明细
     * @param shortSkuList          短缺sku
     */
    public void outerPlatformPushReceiptNotice(Integer mId, List<OrderItemVO> deliveryOrderItemList, List<DeliveryPathShortSku> shortSkuList) {
        // 根据鲜沐门店mId查询该门店是否需要向外部对接推送发货通知
        Integer countReceiptNotice = merchantOuterMapper.selectByMidReceiptNotice(mId);
        if (countReceiptNotice > 0) {
            JSONObject json = new JSONObject();
            json.put("orderItemList", deliveryOrderItemList);
            json.put("shortSkuList", shortSkuList);
            String param = json.toJSONString();
            MQData mqData = new MQData();
            mqData.setType(MType.PUSH_RECEIPT_NOTICE.name());
            mqData.setData(param);
            logger.info("外部对接推送订单收货通知: {}",JSON.toJSONString(mqData));
            mqProducer.send("mall-list",null,JSON.toJSONString(mqData));
        }
    }

    /**
     * 外部对接推送订单收货通知-ofc发送
     *
     * @param mqData 发货明细
     */
    public void outerPlatformPushMessage(MQData mqData) {
        // 根据鲜沐门店mId查询该门店是否需要向外部对接推送发货通知
        JSONObject jsonObject = JSONObject.parseObject((String) mqData.getData());
        String orderMessages = jsonObject.getString("deliveryOrderMessages");
        Integer mId = jsonObject.getInteger("mId");
        String shortOutOrderEntities = jsonObject.getString("shortOutOrderEntities");
        Integer pushTimes = jsonObject.getInteger("pushTimes");
        List<DeliveryOrderMessage> deliveryOrderList = JSON.parseArray(orderMessages, DeliveryOrderMessage.class);
        List<ShortOutOrderEntityInput> shortOutOrderList = JSON.parseArray(shortOutOrderEntities, ShortOutOrderEntityInput.class);
        Integer countReceiptNotice = merchantOuterMapper.selectByMidReceiptNotice(mId);
        if (countReceiptNotice > 0) {
            this.pushReceiptNotice(deliveryOrderList,shortOutOrderList,pushTimes);
        }
    }

    private void pushReceiptNotice(List<DeliveryOrderMessage> deliveryOrderList, List<ShortOutOrderEntityInput> shortOutOrderList,Integer pushTimes){
        if (pushTimes == null) {
            pushTimes = 0;
        }
        Integer pushTimesMax = 3;
        List<DeliveryItemMessage> itemMessageList = new ArrayList<>();
        deliveryOrderList.forEach(e -> {itemMessageList.addAll(e.getDeliveryItemMessages());});
        if(StringUtils.isBlank(itemMessageList)){
            return;
        }
        List<DeliveryItemMessage> orderItemList = itemMessageList.stream().filter(el -> Objects.isNull(el.getOutOrderId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemList)) {
            return;
        }

        // 订单发货明细根据sku分组，每个sku的应发货数量
        List<OrderItemVO> orderItemSkuList = new ArrayList<>();
        orderItemList.stream().collect(Collectors.groupingBy(DeliveryItemMessage::getOutItemId)).forEach((sku, item) -> {
            int sum = item.stream().mapToInt(DeliveryItemMessage::getPlanReceiptCount).sum();
            OrderItemVO orderItemVO = new OrderItemVO();
            orderItemVO.setSku(sku);
            orderItemVO.setAmount(sum);
            orderItemSkuList.add(orderItemVO);

        });

        List<ShortOutItemEntityInput> shortSkuList = new ArrayList<>();
        shortOutOrderList.forEach(e -> {shortSkuList.addAll(e.getOrderItemList());});
        // 根据缺货sku计算每个sku实发货数量
        for (OrderItemVO orderItem : orderItemSkuList) {
            for (ShortOutItemEntityInput shortSku : shortSkuList) {
                if (!orderItem.getSku().equals(shortSku.getOutItemId())) {
                    continue;
                }
                orderItem.setAmount(orderItem.getAmount() - (shortSku.getShortCount() == null ? 0: shortSku.getShortCount()));
            }
        }

        // 订单发货明细根据订单号分组,根据订单维度向外部对接平台推送订单收货通知
        List<OrderItemVO> orderList = new ArrayList<>();
        orderItemList.stream().collect(Collectors.groupingBy(DeliveryItemMessage::getOutOrderId)).forEach((orderNo, item) -> {
            OrderItemVO orderItemVO = new OrderItemVO();
            orderItemVO.setOrderNo(orderNo);
            orderList.add(orderItemVO);
        });

        for (OrderItemVO order : orderList) {
            // 根据鲜沐订单号查询外部订单信息
            OrderOuterInfo orderOuterInfo = orderOuterInfoMapper.queryInfoByXmOrderNo(order.getOrderNo());
            if (orderOuterInfo == null) {
                return;
            }
            // 根据外部平台id查询外部平台信息
            OuterPlatform outerPlatform = outerPlatformMapper.selectOuterPlatformById(orderOuterInfo.getOuterPlatformId());
            // 判断外部平台是否要推送订单发货通知
            if (outerPlatform.getPushOrderSwitch() == PushSwitchEnum.CLOSE.getSwitchStatus().intValue()) {
                return;
            }

            // 根据外部订单号、外部平台id查询外部订单明细信息
            List<OrderOuterItem> orderOuterItemList = orderOuterInfoMapper.selectOrderOuterItem(orderOuterInfo.getOrderNo(), outerPlatform.getOuterPlatformId());
            List<Map> signedOrderItems = new ArrayList<>();
            for (DeliveryItemMessage orderItem : orderItemList) {
                if (!order.getOrderNo().equals(orderItem.getOutOrderId())) {
                    continue;
                }
                // 明细签收数量
                Integer signedInquantity = 0;
                for (OrderItemVO orderItemsSku : orderItemSkuList) {
                    if (orderItemsSku.getSku().equals(orderItem.getOutItemId())) {
                        if (orderItemsSku.getAmount() > 0) {
                            Integer orderItemsSkuAmount = orderItemsSku.getAmount();
                            // sku实际发货数量-订单明细应发货数量，大于等于0签收数量=应发货数量，否则签收数量=实际发货数量
                            orderItemsSku.setAmount(orderItemsSku.getAmount() - orderItem.getPlanReceiptCount());
                            if (orderItemsSku.getAmount() >= 0) {
                                signedInquantity = orderItem.getPlanReceiptCount();
                            } else {
                                signedInquantity = orderItemsSkuAmount;
                            }
                        }
                    }
                }
                // 明细签收数量大于0 才推送明细签收信息
                if (signedInquantity > 0) {
                    Integer itemId = null;
                    String outerSku = "";
                    for (OrderOuterItem orderOuterItem : orderOuterItemList) {
                        if (orderItem.getOutItemId().equals(orderOuterItem.getXmSku())) {
                            itemId = orderOuterItem.getItemId();
                            outerSku = orderOuterItem.getSku();
                        }
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("oi_id", itemId);
                    map.put("sn", outerSku);
                    map.put("nums", signedInquantity);
                    signedOrderItems.add(map);
                }
            }
            JSONObject json = new JSONObject();
            json.put("method", "orderconfirm");
            json.put("token", outerPlatform.getToken());
            json.put("order_id", orderOuterInfo.getOrderNo());
            json.put("memo", "");
            json.put("items", JSON.parseArray(JSON.toJSONString(signedOrderItems)));
            String param = json.toJSONString();
            logger.info("外部对接-推送门店确认签收通知,请求数据：{}", param);
            Integer pushStatus = PushRrecordStatusEnum.FAIL.getPushStatus();
            String result = "";
            try {
                result = sendHttp(outerPlatform.getCallUrl(), param);
                logger.info("外部对接-推送门店确认签收通知,返回数据：{}", result);
                OuterPlatformRes outerPlatformRes = JSON.parseObject(result, OuterPlatformRes.class);
                if (outerPlatformRes != null && OuterPlatformResEnum.SUCCESS.getRecode().equals(outerPlatformRes.getRecode())) {
                    pushStatus = PushRrecordStatusEnum.SUCCESS.getPushStatus();
                    pushTimes = pushTimes + 1;
                } else {
                    if (pushTimes <= pushTimesMax) {
                        pushTimes = pushTimes + 1;
                        /*ScheduleJob job = new ScheduleJob(itemMessageList + "_" + itemMessageList + "_" + pushTimes, JobGroupType.REPUSH_DELIVERY_NOTICE);
                        LocalDateTime repushTime = LocalDateTime.now().plusMinutes(5);
                        job.setCronExpression(repushTime.format(DateTimeFormatter.ofPattern(DateUtils.QUARTZ_DATE_FORMAT)));
                        jobManage.addJob(job);*/
                    }
                }
            } catch (Exception e) {
                logger.error("外部对接-推送订单发货通知异常", e);
            }
            OuterPlatformPushRecord pushRecord = outerPlatformPushRecordMapper.selectRecordByXmOrderNo(orderOuterInfo.getXmOrderNo(), PushRrecordTypeEnum.RECEIPT_NOTICE.getPushRrecordType());
            if (pushRecord == null) {
                OuterPlatformPushRecord outerPlatformPushRecord = new OuterPlatformPushRecord();
                outerPlatformPushRecord.setType(PushRrecordTypeEnum.RECEIPT_NOTICE.getPushRrecordType());
                outerPlatformPushRecord.setXmOrderNo(orderOuterInfo.getXmOrderNo());
                outerPlatformPushRecord.setPushStatus(pushStatus);
                outerPlatformPushRecord.setPushTimes(pushTimes);
                outerPlatformPushRecord.setReqContent(param);
                outerPlatformPushRecord.setResContent(result);
                outerPlatformPushRecordMapper.insertSelective(outerPlatformPushRecord);
            } else {
                pushRecord.setPushStatus(pushStatus);
                pushRecord.setPushTimes(pushTimes);
                outerPlatformPushRecordMapper.updateByPrimaryKeySelective(pushRecord);
            }
        }
    }

    /**
     * 发送http请求
     * @param urlStr 地址
     * @param param 请求内容
     * @return
     */
    public String sendHttp(String urlStr, String param) {
        String contentType = "application/json";
        return HttpUtil.sendHttp(urlStr, RequestMethod.POST, param, contentType);
    }
}
