package net.summerfarm.mapper.manage;

import com.sun.org.apache.xpath.internal.operations.Bool;
import net.summerfarm.model.domain.FinanceSettlement;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> 2021-12-08
 */
@Repository
public interface FinanceSettlementMapper {
    int deleteByPrimaryKey(Long id);

    /**
     * 品牌结算方式
     * @param financeSettlement
     * @return
     */
    int insert(FinanceSettlement financeSettlement);

    int insertSelective(FinanceSettlement record);

    /**
     * 查询品牌的结算方式
     * @param adminId 品牌id
     * @return FinanceSettlement
     */
    List<FinanceSettlement> selectByPrimaryKey(Integer adminId);

    /**
     * 查询品牌的结算方式
     * @param adminId 品牌id
     * @return FinanceSettlement
     */
    FinanceSettlement selectByAdminAndType(@Param("adminId") Integer adminId,@Param("type") Integer type);

    /**
     * 按admin id和admin类型删除
     *
     * @param adminId 管理员id
     * @param type    类型
     */
    void deleteByAdminIdAndType(@Param("adminId") Integer adminId,@Param("type") List<Integer> type);

    /**
     * 查询今天生成账单的品牌
     * @param settlementMethod
     * @return
     */
    List<FinanceSettlement> selectPaidOnceMonth(Integer settlementMethod);

    /**
     * 根据出账日查询账期信息
     *
     * @param settlementMethod 结算方式:0:半月结;月结:每月X号
     * @param isLastDayOfMonth 是否是当月最后一天
     * @param adminId          大客户品牌Id
     * @return
     */
    List<FinanceSettlement> selectByDayAndAdminId(@Param("settlementMethod") Integer settlementMethod, @Param("isLastDayOfMonth")Boolean isLastDayOfMonth,@Param("adminId")Integer adminId);

    int updateByPrimaryKeySelective(FinanceSettlement record);

    int updateByPrimaryKey(FinanceSettlement record);
}