package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.Banner;
import net.summerfarm.model.input.market.resource.PageQueryInput;
import net.summerfarm.model.vo.BannerVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface BannerMapper {

    int insertSelective(Banner record);

    int insertBatch(@Param("banner") Banner banner, @Param("areaNos") List<Integer> areaNos);

    int updateByPrimaryKeySelective(Banner record);

    @RequiresDataPermission(originalField = "b.area_no")
    Banner selectUnSave(String type);

    @RequiresDataPermission(originalField = "b.area_no")
    Banner selectOne(Banner selectKeys);

    @RequiresDataPermission(originalField = "b.area_no")
    List<BannerVO> select(Banner selectKeys);

    /**
     * 首页查询
     * @param selectKeys
     * @return
     */
    @RequiresDataPermission(originalField = "b.area_no")
    List<BannerVO> selectByStatus(BannerVO selectKeys);

    @RequiresDataPermission(originalField = "a.area_no")
    List<Banner> selectByGroupId(Integer groupId);

    int deleteByGroupId(Integer groupId);

    Integer countMaxId();

    List<BannerVO> selectSame(@Param("id") Integer id, @Param("ruleId") Integer ruleId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("type") Integer type, @Param("bannerType") String bannerType);

    int selectByName(@Param("name") String name, @Param("id") Integer id);

    /**
     * 条件查询
     * @param queryInput
     * @return
     */
    List<BannerVO> listByQuery(PageQueryInput queryInput);

    List<BannerVO> listAreaByGroupIds(@Param("list") List<Integer> groupIds);
}