package net.summerfarm.mapper.manage;


import net.summerfarm.model.domain.LuckyDrawActivityRecord;

public interface LuckyDrawActivityRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(LuckyDrawActivityRecord record);

    int insertSelective(LuckyDrawActivityRecord record);

    LuckyDrawActivityRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LuckyDrawActivityRecord record);

    int updateByPrimaryKey(LuckyDrawActivityRecord record);
}