package net.summerfarm.dao.stockTransfer.dataobject;

import lombok.*;
import lombok.experimental.FieldDefaults;


import java.util.Date;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockTransferItemDO {

    /**
     * 主键id
     */
    Long id;

    /**
     * 任务id
     */
    Long stockTransferId;

    /**
     * 转入sku
     */
    String transferInSku;

    /**
     * 预转入数量
     */
    Long preTransferInNum;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;
}
