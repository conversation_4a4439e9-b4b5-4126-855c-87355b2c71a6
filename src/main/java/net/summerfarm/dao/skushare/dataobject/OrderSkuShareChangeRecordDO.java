package net.summerfarm.dao.skushare.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderSkuShareChangeRecordDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 转出sku编码
     */
    private String transferOutSku;

    /**
     * 转入sku编码
     */
    private String transferInSku;

    /**
     * 单号
     */
    private String outOrderNo;

    /**
     * 新应转出数量
     */
    private Integer newShouldTransferOutQuantity;

    /**
     * 原应转出数量
     */
    private Integer oldShouldTransferOutQuantity;

    /**
     * 新已转出数量
     */
    private Integer newActualTransferOutQuantity;

    /**
     * 原已转出数量
     */
    private Integer oldActualTransferOutQuantity;

    /**
     * 新待转出数量
     */
    private Integer newRemainingTransferOutQuantity;

    /**
     * 原待转出数量
     */
    private Integer oldRemainingTransferOutQuantity;

    /**
     * 新应转入数量
     */
    private Integer newShouldTransferInQuantity;

    /**
     * 原应转入数量
     */
    private Integer oldShouldTransferInQuantity;

    /**
     * 新已转入数量
     */
    private Integer newActualTransferInQuantity;

    /**
     * 原已转入数量
     */
    private Integer oldActualTransferInQuantity;

    /**
     * 新待转入数量
     */
    private Integer newRemainingTransferInQuantity;

    /**
     * 原待转入数量
     */
    private Integer oldRemainingTransferInQuantity;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 库存变动类型
     */
    private Integer recordType;

    /**
     * 库存变动类型描述
     */
    private String recordTypeDesc;

}