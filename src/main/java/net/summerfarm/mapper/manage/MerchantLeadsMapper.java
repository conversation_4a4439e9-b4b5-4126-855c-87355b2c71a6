package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MerchantLeads;
import net.summerfarm.model.vo.MerchantLeadsVO;

import java.util.List;

public interface MerchantLeadsMapper {
    int insertSelective(MerchantLeads record);

    int updateByPrimaryKeySelective(MerchantLeads record);

    int updateByPrimaryKey(MerchantLeads record);

    List<MerchantLeadsVO> select(MerchantLeads selectKeys);

    MerchantLeads selectByMname(String mname);

    MerchantLeads selectById(Integer id);
}