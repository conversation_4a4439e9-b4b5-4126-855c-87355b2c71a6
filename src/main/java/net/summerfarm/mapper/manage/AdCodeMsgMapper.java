package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AdCodeMsg;
import net.summerfarm.model.vo.CityStoreNoVO;
import net.summerfarm.model.vo.ProvinceCityAreaVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> ct
 * create at:  2021/8/11  11:01
 */
@Repository
public interface AdCodeMsgMapper {

    int insertBatchAdCode(List<AdCodeMsg> list);

    List<AdCodeMsg> selectByFenceId(@Param("fenceId") Integer fenceId, @Param("status") Integer status);


    int deleteAdCode(Integer id);


    int deleteBatch(List<AdCodeMsg> list);


    List<AdCodeMsg> selectAdCodeMsg(AdCodeMsg adCodeMsg);

    List<AdCodeMsg> selectCityAdCodeMsg(AdCodeMsg adCodeMsg);

    /**
    * 查询 信息 非同一个fenceId
    */
    List<AdCodeMsg> selectByCity(AdCodeMsg adCodeMsg);

    /**
    * 更改状态
    */
    int updateStatusByFenceId(@Param("fenceId") Integer fenceId, @Param("status") Integer status, @Param("originStatus") Integer originStatus);

    /**
    * 根据ID查询
     *
    */
    List<AdCodeMsg> selectByIds(List<String> list);

    /**
    * 更新区域对应的围栏信息
     * @param id
     * @param fenceId
     * @return
    */
    int updateFenceIdById(@Param("id") Integer id, @Param("fenceId") Integer fenceId);


    /**
     * 查询 信息 非同一个fenceId 包含暂停
     */
    List<AdCodeMsg> selectAndStopByCity(AdCodeMsg adCodeMsg);

    /**
     * 查询
     * @param province
     * @param city
     * @param area
     * @return
     */
    List<AdCodeMsg> selectByProviceAreaCity(@Param("province") String province, @Param("city") String city, @Param("area") String area);

    /**
     * 根据城市名称查询
     * @param list 城市名称
     * @return 对应行政城市信息
     */
    List<AdCodeMsg> selectByCityList(@Param("list") List<String> list);


    /**
     * 获取蔚蓝信息0，3
     * @param fenceId
     * @return
     */
    List<AdCodeMsg> selectAdCodeMsgByFenceId(@Param("fenceId") Integer fenceId);

    /**
     * 获取code和城市的对应
     * @return
     */
    CityStoreNoVO selectCodeByAdCode(@Param("adCode")String adCode);

    List<ProvinceCityAreaVO> getCity(@Param("name")String name,@Param("storeNo")Integer storeNo);

    Integer selectByStoreNoAndAdCode(@Param("storeNo")Integer storeNo,@Param("adCode") String adCode);


    /**
     * 根据ID查询
     *
     */
    List<AdCodeMsg> selectByFenceIds(@Param("list") List<Integer> list, @Param("status") Integer status);
}
