package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.common.util.es.dto.EsMerchantIndexDTO;
import net.summerfarm.model.DTO.market.circle.EsMerchantTagDTO;
import net.summerfarm.model.domain.Merchant;
import net.summerfarm.model.input.GradeReq;
import net.summerfarm.model.input.MajorMerchantQuery;
import net.summerfarm.model.input.MerchantReq;
import net.summerfarm.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Repository
public interface MerchantMapper {

    int insertSelective(Merchant record);

    @RequiresDataPermission(originalField = "m.area_no")
    Merchant selectByPrimaryKey(Long mId);

    /**
     * 重置为待审核状态
     * @param record
     * @return
     */
    int rebackReview(Merchant record);

    /**
     * 重置为待审核状态
     * @param record
     * @return
     */
    int rebackReviewNew(Merchant record);

    int updateByPrimaryKeySelective(Merchant record);

    @RequiresDataPermission(originalField = "m.area_no")
    List<MerchantVO> select(MerchantVO selectKeys);

    List<Merchant> selectIssueUser(String phone);

    List<MerchantVO> selectMerchantList(MerchantVO selectKeys);

    List<GradeReq> getGrade(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo);

    int updateInfo(Merchant record);

    int updateStatus(Merchant merchant);

    /**
     * 查询当日新增用户数
     *
     * @param startTime
     * @param endTime
     * @return
     */
    int countNewMerchants(@Param(value = "startTime") Date startTime, @Param(value = "endTime") Date endTime);

    //    @RequiresDataPermission(fieldAlias = "areaNo")
    Integer countMerchantsToGmv(@Param("areaNo") Integer areaNo, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("dataPermission") Set<Integer> dataPermission);

    List<DataVO> merchatSizeData(@Param(value = "date") LocalDateTime date);

    List<DataVO> merchatTypeData(@Param(value = "date") LocalDateTime date);

    Merchant selectOne(Map selectKeys);

    int updateInviter2Null(long mId);

    List<MerchantVO> selectMerchantByAdminId(HashMap map);


    void updateByAdminId(Integer adminId);

    void updateSkuShowByAdminId(@Param(value = "direct") Integer direct, @Param(value = "skuShow") Integer skuShow, @Param(value = "adminId") Integer adminId);

    void updateSkuShowByDirect(@Param(value = "direct") Integer direct, @Param(value = "skuShow") Integer skuShow, @Param(value = "adminId") Integer adminId);

    /**
     * 查询大客户关联门店信息
     * @param majorMerchantQuery 查询条件
     * @return 门店信息
     */
    List<MerchantVO> selectMajorMerchantDetail(MajorMerchantQuery majorMerchantQuery);

    @RequiresDataPermission(originalField = "m.area_no")
    List<MerchantVO> selectOpenSea(@Param(value = "start") LocalDateTime start, @Param(value = "reason") String reason, @Param(value = "mname") String mname, @Param(value = "mId") Long mId, @Param(value = "followType") Integer followType, @Param(value = "area") String area, @Param(value = "city") String city, @Param(value = "province") String province, @Param(value = "address") String address,@Param(value = "timingFollowType")Integer timingFollowType,@Param(value = "whiteListType")Integer whiteListType);

    @Deprecated
    List<MerchantVO> selectPrivateSea(@Param(value = "start") LocalDateTime start, @Param(value = "reason") String reason, @Param(value = "mname") String mname, @Param("adminId") Integer adminId, @Param("address") String address);

    List<Merchant> selectMerchants(Merchant merchant);

    /**
     * 获取筛选条件下的用户
     * @param select 筛选条件
     * @return 用户集合
     */
    List<Merchant> selectSomeMerchants(MerchantReq select);

    //修改用户余额
    int updateRechargeAmount(@Param("amount") BigDecimal amount, @Param("mId") Long mId);

    /**
     * 退还余额
     * @param amount
     * @param mId
     * @return
     */
    int subtractRechargeAmount(@Param("amount") BigDecimal amount, @Param("mId") Long mId);

    int updateGrade(@Param("grade") Integer grade, @Param("mId") Long mId);

    @RequiresDataPermission(originalField = "m.area_no")
    List<MerchantVO> selectMerchantListBD(MerchantVO record);

    /**
     * 根据id查询客户信息详情
     * @param mId 客户id
     * @return 客户信息详情
     */
    MerchantVO selectMerchantByMid(@Param("mId")Long mId);

    /**
     * 根据查询条件获取商户信息列表
     * @param merchantVO 查询条件：mId、mname（商户名）、grade（等级）、adminId（销售id）、areaNo、address(商户地址)、size（商户类型）
     * @return 商户信息列表
     */
    @RequiresDataPermission(originalField = "m.area_no")
    List<MerchantVO> queryAllMerchantVO(MerchantVO merchantVO);

    List<MerchantVO> queryPrivateSea();

    @RequiresDataPermission(originalField = "m.area_no")
    List<MerchantVO> queryBDPrivateSea(MerchantVO merchantVO);


    /**
     * 是否存在相同邀请码
     * @param channelCode 邀请码
     * @return t、存在 f、不存在
     */
    boolean existChannelCode(String channelCode);

    /**
     * 添加渠道码
     * @param mId 店铺好
     * @param channelCode 邀请码
     * @return 影响行数
     */
    int updateChannelCode(@Param("mId") Long mId, @Param("channelCode") String channelCode);

    Merchant selectByMId(Long mId);

    /**
     * 删除子账号
     * @param mId
     * @return
     */
    int delete(Long mId);

    /**
     * 查询公海客户信息
     * @param select 查询条件
     * @return 公海客户信息
     */
    @RequiresDataPermission(originalField = "m.area_no")
    List<MerchantVO> selectOpenSeas(MerchantVO select);


    Integer countAllMerchantsToGmv(@Param("areaNo") Integer areaNo, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("dataPermission") Set<Integer> dataPermission);

    /**
     * 分页获取客户信息
     * @param mId 客户id
     * @param startRow 位置
     * @param endRow 数量
     * @return 客户信息
     */
    List<EsMerchantIndexDTO> queryMerchantWithContact(@Param("mId") Long mId, @Param("startRow") int startRow, @Param("endRow") int endRow);

    /**
     * 分页获取客户信息
     * @param mId 客户id
     * @param startRow 位置
     * @param endRow 数量
     * @return 客户信息
     */
    List<EsMerchantIndexDTO> queryMerchantWithContactToEs(@Param("mId") Long mId, @Param("startRow") int startRow, @Param("endRow") int endRow);

    /**
     * 用户名查询
     * @param mname
     * @return
    */
    Merchant selectIsUserName(@Param("mname") String mname, @Param("phone") String phone);

    /**
    * 更改大客户为单店
     * @param mid
     * @return
    */
    int updateMerchantSize(Long mid);

    List<MerchantVO> selectRegisterPrivateSea(@Param("mname") String mname, @Param("registerDate") LocalDate registerDate, @Param("bdAdminId") Integer bdAdminId);

    Merchant selectByMname(String mname);

    List<Merchant> selectByNameList(@Param("nameList") Collection<String> nameList);


    /**
     * 根据merchant内的phone参数来获取对应的Merchant集合
     * @param merchant
     * @return
     */
    List<Merchant> selectMerchantsByParam(Merchant merchant);

    /**
     *
     * 更新客户经营类型
    */
    int updateMerchantType(@Param("mId") Long mId,@Param("type") String type);

    /**
     * 根据某些条件查询出目前可用的门店
     * @param query
     * @return
     */
    List<MerchantVO> selectMerchantsBySelectKeys(MerchantVO query);

    int updateMerchantAreaNo(MerchantVO vo);

    List<Long> selectMerchantByMsg(Merchant merchant);

    /**
     * 查询审核通过店铺数量
     * @param localDateTime
     * @return
     */
    int selectStoreNum(LocalDateTime localDateTime);
    List<DataVO> merchatEnterpriseScaleData(@Param("date") LocalDateTime date);

    /**
     * 批量调整用户归属城市
     * @param merchant
     * @return
     */
    int updateAreaNoByCity(Merchant merchant);

    /**
     * 获取商户从某个时间至今购买的订单类目top10
     * @param mId 商户id
     * @param startTime 开始时间
     * @return 商户从某个时间至今购买的订单类目top10商品名称及数量
     */
    List<CustomerAnalysisVO> merchantCategoryTOP(@Param("mId") Long mId,@Param("startTime") LocalDateTime startTime);

    /**
     * 同区域同行业购买二级类目下单商户数top10
     * @param mId 商户id
     * @param startTime 开始时间
     * @return 同区域同行业购买二级类目下单商户数top10商品名称及商户数
     */
    List<CustomerAnalysisVO> peerCategoryTOP(@Param("mId") Long mId,@Param("startTime") LocalDateTime startTime);

    /**
     * 判断当前商户是否购买过 同区域同行业购买二级类目下单商户数top10 上的商品
     * @param mId 商户id
     * @param startTime 开始时间
     * @param categoryId 品类id
     * @return 结果>0为购买过，反之为未购买
     */
    Integer whetherNotToBuy(@Param("mId") Long mId,@Param("startTime") LocalDateTime startTime,@Param("categoryId") Integer categoryId);

    /**
     * 获取商户从某个时间至今购买的SPU_top10及数量
     * @param mId 商户id
     * @param startTime 开始时间
     * @return 商户从某个时间至今购买的SPU_top10商品名称及数量
     */
    List<CustomerAnalysisVO> merchantSPUTOP(@Param("mId") Long mId,@Param("startTime") LocalDateTime startTime);

    /**
     * 同区域同行业购买SPU下单商户数top10
     * @param mId 商户id
     * @param startTime 开始时间
     * @return 同区域同行业购买SPU下单商户数top10及商户数
     */
    List<CustomerAnalysisVO> peerSPUTOP(@Param("mId") Long mId,@Param("startTime") LocalDateTime startTime);

    /**
     * 商户订单spu数量top10在同区域行业内单个客户下单最大量
     * @param mId 商户id
     * @param startTime 开始时间
     * @return 商户订单spu数量top10在同区域行业内单个客户下单最大量
     */
    CustomerAnalysisVO peerSPUMax(@Param("mId") Long mId,@Param("startTime") LocalDateTime startTime,@Param("pdId") Integer pdId);

    /**
     * 判断当前商户是否购买过 同区域同行业购买二级类目下单商户数top10 上的商品
     * @param mId 商户id
     * @param startTime 开始时间
     * @param pdId spuId
     * @return 结果>0为购买过，反之为未购买
     */
    Integer whetherNotToBuySPU(@Param("mId") Long mId,@Param("startTime") LocalDateTime startTime,@Param("pdId") Integer pdId);

    /**
     * 查询一天内的待审核店铺根据城市分组
     * @return 城市和待审核店铺数据对象
     */
    List<NotExamineStoreVO> selectNotExamineMerchantInOneDayList(LocalDateTime oneDayBefore);

    /**
     * 查询超过24小时的待审核店铺根据城市分组
     * @return 城市和待审核店铺数据对象
     */
    List<NotExamineStoreVO> selectNotExamineMerchantMoreThanOneDayList(LocalDateTime oneDayBefore);

    /**
     * 查询所有待审核店铺数量根据城市分组
     * @return 所有待审核店铺数量根据城市分组
     */
    List<NotExamineStoreVO> selectNotExamineMerchantList();

    /**
     * 查询加扩展表
     * @param id 客户id
     * @return 客户表加扩展表数据
     */
    @RequiresDataPermission(originalField = "m.area_no")
    Merchant selectExtByPrimaryKey(@Param("mId") Long id);

    /**
     * 查找在指定时间之前注册的所有用户
     * @param dayEnd 指定时间
     * @return
     */
    List<MerchantExtVo> selectAllByTime(@Param("dayEnd") LocalDateTime dayEnd);

    /**
     * 根据名称模糊查询
     * @param name
     * @return
     */
    List<Merchant> fuzzySelectByName(String name);

    /**
     * 根据品牌id查询到客户
     * @param adminId
     * @return
     */
    List<Merchant> selectMerchantAdminId(@Param("adminId") Integer adminId);

    List<Merchant> selectNotExistUser(@Param("list") List<String> phoneList);

    /**
     * 根据门店名称模糊查询门店名称、门店BD
     * @param mname
     * @return
     */
    List<MerchantVO> selectByMnameSaler(String mname);

    /**
     * 查看详情
     * @param selectKeys
     * @return
     */
    MerchantVO selectDetail(MerchantVO selectKeys);

    BatchUpdateDeliveryDateVo getMerchantInfoByCId(@Param("contactId") String contactId);

    List<Merchant> listByMIds(@Param("list") List<Long> mIds);

    Long getLastMId();

    /**
     * 查询MId区间内的商家
     * @param beginMId
     * @param endMId
     * @return
     */
    List<EsMerchantTagDTO> listByRangeId(@Param("beginMId") Long beginMId, @Param("endMId") Long endMId);

    List<EsMerchantTagDTO> listEsMerchantByMIds(@Param("list") List<Long> mIds);

    /**
     * 根据adminId查询联系人id
     * @param adminId admin
     * @return 联系人id集合
     */
    List<Long> queryContactByAdminId(@Param("adminId")Integer adminId);

    /**
     * 根据手机号查询对应商户id
     * @param phone 手机号
     * @return 商户id集合
     */
    List<Long> selectByPhone(@Param("phone") String phone,@Param("islock") Integer islock);

    /**
     * 根据门店名称模糊匹配信息 -- 默认返回500条
     * @param mName 门店名称
     * @return 门店名称和ID
     */
    List<Merchant> getListByName(@Param("mName") String mName, @Param("size") String size);

    /**
     * 根据门店id查询门店详情信息
     * @param mIds 门店id
     * @return
     */
    List<Merchant> listByAllMIds(@Param("list") List<Long> mIds);

    /**
     * 更新门店运营服务区
     * @param city 城市
     * @param area 区域
     * @param newAreaNo 新运营服务区
     */
    void updateMerchantArea(@Param("province") String province, @Param("city") String city, @Param("area") String area, @Param("newAreaNo") Integer newAreaNo, @Param("businessLine") Integer businessLine);

    /**
     * 根据运营服务区域查询审核通过的门店
     * @param areaNos 运营服务区域
     * @return Merchant
     */
    List<Merchant> selectByAreaNos(@Param("list") List<Integer> areaNos);

    /**
     * 查询大客户门店
     *
     * @param adminId 管理员id
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> listByAdminId(@Param("adminId")Integer adminId);
}
