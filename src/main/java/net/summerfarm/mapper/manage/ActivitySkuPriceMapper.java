package net.summerfarm.mapper.manage;

import java.util.List;

import net.summerfarm.model.domain.market.ActivitySkuPrice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivitySkuPriceMapper {

    int deleteByPrimaryKey(Long id);


    int insert(ActivitySkuPrice record);


    int insertSelective(ActivitySkuPrice record);


    ActivitySkuPrice selectByPrimaryKey(Long id);


    int updateByPrimaryKeySelective(ActivitySkuPrice record);


    int updateByPrimaryKey(ActivitySkuPrice record);

    int updatePrice(ActivitySkuPrice activitySkuPrice);

    int updatePriceSelective(ActivitySkuPrice activitySkuPrice);

    int insertBatch(@Param("list") List<ActivitySkuPrice> list);

    int deleteByBasicInfoId(@Param("basicInfoId") Long basicInfoId);

    int deleteSkuByInfoId(@Param("basicInfoId") Long basicInfoId, @Param("sku") String sku, @Param("areaNos") List<Integer> areaNos);

    int deleteByAreaNo(@Param("basicInfoId") Long basicInfoId);

    ActivitySkuPrice selectByDetailId(@Param("skuDetailId") Long skuDetailId, @Param("sku") String sku, @Param("areaNo") Integer areaNo);

    List<ActivitySkuPrice> selectBySkuAndBasicInfoId(@Param("basicInfoId") Long basicInfoId, @Param("sku") String sku);

    List<ActivitySkuPrice> selectByBasicIds(@Param("list") List<Long> list);

    int updateLadderPriceBatch(@Param("list") List<ActivitySkuPrice> activitySkuPrices);
}
