package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.TrustChangeRecord;

import java.util.List;

public interface TrustChangeRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TrustChangeRecord record);

    int insertSelective(TrustChangeRecord record);

    TrustChangeRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TrustChangeRecord record);

    int updateByPrimaryKey(TrustChangeRecord record);

    // 记录开始id
    Integer selectCount();

    // 查询这次失败记录
    List<TrustChangeRecord> selectByMaxId(Integer id, Integer storeNo);
}