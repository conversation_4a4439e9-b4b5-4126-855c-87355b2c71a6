package net.summerfarm.mapper.manage;


import net.summerfarm.model.domain.LuckyDrawActivity;
import net.summerfarm.model.input.LuckDrawActivityPageQuery;
import net.summerfarm.model.vo.LuckDrawActivityVO;

import java.util.List;

public interface LuckyDrawActivityMapper {

    int deleteByPrimaryKey(Long id);

    int insert(LuckyDrawActivity record);

    int insertSelective(LuckyDrawActivity record);

    LuckyDrawActivity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LuckyDrawActivity record);

    int updateByPrimaryKey(LuckyDrawActivity record);

    List<LuckDrawActivityVO> getPage(LuckDrawActivityPageQuery luckDrawActivityPageQuery);

    int getCount(LuckyDrawActivity newLuckyDrawActivity);
}