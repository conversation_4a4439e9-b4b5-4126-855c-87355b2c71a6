package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.crm.MerchantAccountTransfer;
import net.summerfarm.model.input.merchant.TransferMerchantQueryInput;

import java.util.List;

public interface MerchantAccountTransferMapper {
    int deleteByPrimaryKey(Long id);


    int insertSelective(MerchantAccountTransfer record);

    MerchantAccountTransfer selectByPrimaryKey(Long id);


    List<MerchantAccountTransfer> select(TransferMerchantQueryInput queryInput);
}