package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Area;
import net.summerfarm.model.domain.BDExt;
import net.summerfarm.model.domain.CrmManageBd;
import net.summerfarm.model.input.SaveManageAreaInput;
import net.summerfarm.model.vo.AreaResultVo;
import net.summerfarm.model.vo.ZoneNameListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmManageBdMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CrmManageBd record);

    int insertSelective(CrmManageBd record);

    CrmManageBd selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CrmManageBd record);

    int updateByPrimaryKey(CrmManageBd record);

    int existZoneName(SaveManageAreaInput saveManageAreaInput);

    List<String> queryZoneName(@Param("zoneName") String zoneName);

    int existManageAdmin(SaveManageAreaInput saveManageAreaInput);

    /**
     * 根据登录人员权限获取区域列表,为空获取所有区域
     * @param adminId 登录人id
     * @return 区域列表
     */
    List<ZoneNameListVo> selectZoneNameByAdminId(@Param("adminId") Integer adminId);

    /**
     * 获取用户管理区域内的城市ids
     * @param adminId 登录用户id
     * @return 城市ids
     */
    List<Integer> getAreaNoByAdmin(@Param("adminId") Integer adminId);

    /**
     * 根据城市编号获取该城市对应城市负责人
     * @param areaNo
     * @return
     */
    BDExt selectRealName(@Param("areaNo") Integer areaNo);

    /**
     * 获取固定奖励sku
     * @param adminId 销售id
     * @return sku集合
     */
    List<String> getSkuCommission(Integer adminId);

    List<AreaResultVo> queryExistArea(Area query);

    List<Integer> queryExistAreaInt();

    /**
     * 根据bdID获取当前直属主管M1以及M2
     * @param adminId 销售id
     * @return
     */
    CrmManageBd getManageBdByAdminId(Integer adminId);

    /**
     * 根据城市获取当前区域的主管M1以及M2
     * @param city 注册城市
     * @return
     */
    CrmManageBd getManageBdByCity(String city);
}