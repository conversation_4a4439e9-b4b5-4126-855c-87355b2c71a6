package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AdminAuthExtend;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author:ct
 * @Date:15:11 2020/2/10
 */
@Repository
public interface AdminAuthExtendMapper {



    /**
     * 根据userid查询绑定信息
     * @param type 认证类型：0、钉钉 1、CRM小程序
     * @param approverAdmin id
     * @return 绑定信息
     */
    List<AdminAuthExtend> getListByAdminId(@Param("type") int type, @Param("approverAdmin") List<Integer> approverAdmin);


    List<AdminAuthExtend> selectAll();
    List<Long> selectAdminByWarehouseNo(String warehouseNo,  List<Long> baseUserIds);




}
