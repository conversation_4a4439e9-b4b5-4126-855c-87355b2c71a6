package net.summerfarm.dao.impl;

import net.summerfarm.dao.StoreGoodsTaskRepository;
import net.summerfarm.mapper.StoreGoodsTaskMapper;
import net.summerfarm.model.DTO.GoodsCheckTaskDO;
import net.summerfarm.model.domain.StoreGoodsTaskPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * @desc
 * <AUTHOR>
 * @Date 2022/11/7 15:50
 **/
@Repository
public class StoreGoodsTaskRepositoryImpl implements StoreGoodsTaskRepository {

    @Resource
    private StoreGoodsTaskMapper storeGoodsTaskMapper;

    @Override
    public Long createGoodsCheckTask(GoodsCheckTaskDO createDO) {
        StoreGoodsTaskPO taskPO = StoreGoodsTaskPO.builder()
                .taskSource(createDO.getTaskSource())
                .taskStatus(createDO.getTaskStatus())
                .warehouseNo(createDO.getWarehouseNo())
                .sku(createDO.getSku())
                .skuName(createDO.getSkuName())
                .weight(createDO.getWeight())
                .storageLocation(createDO.getStorageLocation())
                .volume(createDO.getVolume())
                .weightNum(createDO.getWeightNum())
                .creator(createDO.getCreator())
                .operator(createDO.getOperator())
                .gmtCreated(System.currentTimeMillis())
                .gmtModified(System.currentTimeMillis())
                .build();
        return storeGoodsTaskMapper.insert(taskPO);
    }
}
