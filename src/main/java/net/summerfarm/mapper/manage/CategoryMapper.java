package net.summerfarm.mapper.manage;

import net.summerfarm.manage.client.products.dto.res.CategoryResDTO;
import net.summerfarm.model.domain.Category;
import net.summerfarm.model.domain.CategoryAllPathEntity;
import net.summerfarm.model.vo.CategorySkuVO;
import net.summerfarm.model.vo.CategoryVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

@Repository
public interface CategoryMapper {

    int insertSelective(Category record);

    int updateByPrimaryKeySelective(Category record);

    List<CategoryVO> selectTreeNodes();

    List<Category> selectTypeByIds(@Param("ids") List<Integer> ids);

    Set<Integer> selectDairy();

    Set<Integer> selectOldDairy();

    Category selectByPrimaryKey(Integer cId);

    boolean hasEqualsName(@Param("parentId") Integer parentId, @Param("id") Integer id, @Param("category") String category);

    /**
     * 根据前台映射类目查询后台类目
     *
     * @param frontCategoryId
     * @return
     */
    List<Category> selectByFrontCategoryId(Integer frontCategoryId);

    /**
     * 根据parentId查询
     *
     * @param parentId
     * @return
     */
    List<Category> selectByParentId(Integer parentId);

    /**
     * 根据类目名称查询
     *
     * @param gName 一级类目
     * @param pName 二级类目
     * @param cName 三级类目
     * @return
     */
    Category selectByName(@Param("gName") String gName, @Param("pName") String pName, @Param("cName") String cName);

    /**
     * 根据id查询三个级的类目名称
     *
     * @param id
     * @return
     */
    HashMap<String, String> selectThreeLevelCategory(@Param("id") Integer id);

    /**
     * 水果三级类目
     * @return
     */
    List<Category> selectFruits();

    /**
     * 乳制品三级类目
     * @return
     */
    List<Category> selectByDairy();

    /**
     * 根据sku获得类目
     * @param sku
     * @return
     */
    Category selectBySku(@Param("sku") String sku);

    /**
     * 查询类型详情
     * @param id 类目id
     * @return 类目详情数据
     */
    CategoryVO selectDetail(Integer id);

    /**
     * 逻辑删除分类
     * @param categoryIds 分类ids
     */
    void delete(@Param("list") Set<Integer> categoryIds);

    /**
     * 根据后台类目查询对应的sku
     * @param categoryIds 后台类目ids
     * @param skuList skuList
     * @param pdName 商品名称
     * @return 后台类目商品列表
     */
    List<CategorySkuVO> selectByCategoryId(@Param("categoryIds") List<Integer> categoryIds,@Param("skuList") List<String> skuList,@Param("pdName") String pdName);

    /**
     * 分页查询类目数据
     * @param categoryIds
     * @return
     */
    List<CategoryResDTO> selectPageByCategoryIds(@Param("ids") List<Integer> categoryIds);

    /**
     * 分页查询类目数据
     * @param categoryId
     * @return
     */
    List<Category> selectChildrenId(Integer categoryId);

    /**
     * 分页查询类目数据
     * @param childrenIds
     * @return
     */
    List<Category> selectChildrenIdByList(@Param("list") List<Integer> childrenIds);

    /**
     * 根据type查询一级类目
     */
    List<Category> selectFirstByType(@Param("type")Integer type);

    /**
     * 查询类目全路径信息
     * @param categoryId
     * @return
     */
    CategoryAllPathEntity selectCategoryAllPath(@Param("categoryId") Long categoryId);
}
