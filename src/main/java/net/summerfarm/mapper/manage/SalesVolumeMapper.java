package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SalesVolume;
import net.summerfarm.model.vo.PCDayOfWeekQuantityVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;

@Repository
public interface SalesVolumeMapper {

    int insertList(List<SalesVolume> list);

    int insert(SalesVolume salesVolume);

    int updateSaleVolumes(SalesVolume salesVolume);

    List<PCDayOfWeekQuantityVO> selectAll(@Param("storeNo") Integer storeNo, @Param("sku") String sku);

    /**
     * 查询区间内实际销量
     */
    @MapKey("sku")
    HashMap<String,SalesVolume> selectSumTrueQuantity(@Param("storeNo") Integer storeNo, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 查询区间内预计销量
     */
    @MapKey("sku")
    HashMap<String,SalesVolume> selectSumPreQuantity(@Param("storeNo") Integer storeNo, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 查询区间内超出大单值数量
     */
    @MapKey("sku")
    HashMap<String,SalesVolume> selectSumBigDealOrderCnt(@Param("storeNo") Integer storeNo, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);


    /**
    * 查询大单值
    */
    @MapKey("sku")
    HashMap<String,SalesVolume> selectMaxThreshold(@Param("storeNo") Integer storeNo, @Param("startDate") LocalDate startDate);

    Integer selectAmountPerHour(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("startDate") LocalDate date);
    /**
    * 查询数据
    */
    List<SalesVolume> selectBySkuAndTime(SalesVolume salesVolume);

}
