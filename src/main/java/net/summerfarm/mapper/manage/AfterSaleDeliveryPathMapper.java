package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.tms.OldDistOrder;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.domain.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Repository
public interface AfterSaleDeliveryPathMapper {


    List<ClosingOrder> closingOrderByAfterSale(@Param("deliveryTime") LocalDate deliveryTime, @Param("areaNo") Integer areaNo, @Param("parentNo") Integer parentNo);

    List<ClosingOrder> closingOrderByAfterSaleNew(@Param("deliveryTime") LocalDate deliveryTime,
                                               @Param("areaNo") Integer areaNo, @Param("parentNo") Integer parentNo, @Param("interceptFlag") Integer interceptFlag);

    List<OrderItem> afterSaleTask(@Param("deliveryTime") LocalDate deliveryTime, @Param("storeNo") Integer storeNo, @Param("outStoreNo") Integer outStoreNo);

    List<OrderItem> afterSaleTasking(@Param("deliveryTime") LocalDate deliveryTime, @Param("storeNo") Integer storeNo, @Param("outStoreNo") Integer outStoreNo);

    List<OrderItem> afterSaleTaskingForCross(@Param("deliveryTime") LocalDate deliveryTime, @Param("storeNo") Integer storeNo, @Param("outStoreNo") Integer outStoreNo);

    List<OrderItem> selectAfterSaleTask(@Param("deliveryTime") LocalDate deliveryTime, @Param("storeNo") Integer storeNo, @Param("outStoreNo") Integer outStoreNo, @Param("status") Integer status);

    List<OrderItemVO>  selectAfterDetail(@Param("deliveryTime") LocalDate deliveryTime, @Param("contactId") Long contactId);

    List<OrderItem> afterSaleTaskIn(@Param("deliveryTime") LocalDate deliveryTime, @Param("storeNo") Integer storeNo, @Param("outStoreNo") Integer outStoreNo);

    List<DeliveryPathShortSku> afterSaleByNo(@Param("afterSaleNo") String afterSaleNo, @Param("type") Integer type);

    List<ExchangeGoods> selectExchangeGoods(String afterSaleNo);

    AfterSaleDeliveryPath selectPathByNo(String afterSaleNo);

    int updateDeliveryPath(@Param("afterSaleNo") String afterSaleNo, @Param("status") Integer status);

    /**
    * 更新状态，取消的售后不更新状态
    */
    int updateDeliveryPathByPath(AfterSaleDeliveryPath afterSaleDeliveryPath);

    /**
     * 更新状态通过完成排线MQ
     */
    int updateDeliveryPathStatus(@Param("deliveryTime") LocalDate deliveryTime,@Param("contactId") Integer contactId);

    AfterSaleDeliveryDetail selectDetail(Integer id);

    /**
    * 大客户提前截单数据查询
    */
    List<StockTaskPick> afterSalePick(@Param("deliveryTime") LocalDate deliveryTime,
                                      @Param("storeNo") Integer storeNo, @Param("outStoreNo") Integer outStoreNo, @Param("closeTime") String closeTime);


    List<ClosingOrder> closingOrderBySplit(MerchantVO vo);

    int updateAfterPathStoreNo(@Param("idList") List<Long> idList, @Param("storeNo") Integer storeNo);

    /**
     * 获取当前时间之后的补货冻结数量
     * @param warehouseNo
     * @param sku
     * @param deliveryDate
     * @return
     */
    Integer selectAfterSaleQuantity(Integer warehouseNo, String sku, LocalDate deliveryDate);

    /**
     * 获取当前时间之后的补货冻结数量
     * @param storeNo
     * @param sku
     * @param deliveryDate
     * @return
     */
    Integer selectAfterSaleQuantityByStoreNo(Integer storeNo, String sku, LocalDate deliveryDate);

    /**
     * 获取当前时间之后的补货冻结订单
     * @param warehouseNo
     * @param sku
     * @param deliveryDate
     * @return
     */
    List<OrderItemVO> selectAfterSaleOrders(Integer warehouseNo, String sku, LocalDate deliveryDate);

    /**
     * 根据城配仓获取当前时间之后的补货冻结订单
     * @param storeNo
     * @param sku
     * @param deliveryDate
     * @return
     */
    List<OrderItemVO> selectAfterSaleOrdersByStoreNo(Integer storeNo, String sku, LocalDate deliveryDate);


    /**
    *  合并店铺更新补发订单数据
     * @param newMid
     * @param oldMid
     * @return
    */
    Integer updateMerchantId(@Param("newMid") Long newMid, @Param("oldMid") Long oldMid);

    /**
     * 获取退货入库售后相关订单信息
     * @param gmtCreate
     */
    List<ReturnSaleNeedStockVo> getAfterSaleOrderInfo(@Param("gmtCreate") LocalDateTime gmtCreate);

    /**
     * 根据配送路线id查询补发单
     * @param deliveryPathId
     * @return
     */
    List<LackGoodsOrderInfoVO> selectReissueByPathId(Integer deliveryPathId);

    /**
     * 根据配送时间和联系人id查询售后单id集合
     * @param deliveryTime
     * @param contactId
     * @return
     */
    List<GetAfterDataVO> getAfterDataByDTAndCId(@Param("deliveryTime") LocalDate deliveryTime, @Param("contactId") Integer contactId);


    List<ClosingOrder> closingOrderByAfterSaleByMid(Date startDate, Date endDate, Integer mId);

    /**
     * 查询售后配送计划详情
     * @param afterSaleOrderNo
     * @return
     */
    List<AfterSaleDeliveryPathVO> selectByAfterNo(String afterSaleOrderNo);

    /**
     * 查询详情
     * @param afterSaleDeliveryPath 配送时间
     * @return 详情
     *
     */
    List<OrderItemVO> selectAfterDetailNew(AfterSaleDeliveryPath afterSaleDeliveryPath);

    /**
     * 查询详情
     * @param afterSaleDeliveryPath 配送时间
     * @return 详情
     *
     */
    List<OrderItemVO> selectAfterSaleDetail(AfterSaleDeliveryPath afterSaleDeliveryPath);


    /**
     * 查询回收商品信息
     * @param afterSaleOrderNo
     * @return
     */
    List<AfterSaleDeliveryPathVO> selectByAfterNoHandle(List<String> afterSaleOrderNo);

    /**
     * 查询TMS的售后单
     * @param deliveryTime 配送时间
     * @param storeNo 城配仓编号
     * @return 结果
     */
    List<ClosingOrder> tmsClosingOrderByAfterSale(@Param("deliveryTime") Date deliveryTime,@Param("storeNo") Integer storeNo);

    /**
     * 查询指定配送日期之后的售后配送计划
     * @param storeNo 城配仓编号
     * @param deliveryDate 配送日期
     * @return 老模型委托单DTO集合
     */
    List<OldDistOrder> selectAfterSaleDeliveryPlanByStoreNo(@Param("storeNo") Integer storeNo, @Param("deliveryDate") LocalDate deliveryDate);

    /**
     * 更新售后配送计划城配仓编号
     * @param afterSaleDeliveryPlanIds 售后配送计划ID
     * @param oldStoreNo 原城配仓编号
     * @param newStoreNo 新城配仓编号
     * @return 更新条数
     */
    int updateAfterSaleDeliveryPlanStoreNo(@Param("afterSaleDeliveryPlanIds") List<Integer> afterSaleDeliveryPlanIds, @Param("oldStoreNo") Integer oldStoreNo, @Param("newStoreNo") Integer newStoreNo);
}
