package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MchEnterpriseAddressRelation;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface MchEnterpriseAddressRelationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MchEnterpriseAddressRelation record);

    int insertSelective(MchEnterpriseAddressRelation record);

    MchEnterpriseAddressRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MchEnterpriseAddressRelation record);

    int updateByPrimaryKey(MchEnterpriseAddressRelation record);

    /**
     * 查询该地址是否已经被匹配
     * @param contactId
     * @return
     */
    MchEnterpriseAddressRelation selectById(Long contactId);

    /**
     * 删除关联
     * @param id
     * @return
     */
    int updateByKey(Long id);

}