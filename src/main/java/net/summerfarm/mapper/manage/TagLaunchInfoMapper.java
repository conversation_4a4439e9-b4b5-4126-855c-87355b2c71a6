package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.DTO.market.malltag.TagLaunchQueryDTO;
import net.summerfarm.model.domain.market.TagLaunchInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 */
@Mapper
public interface TagLaunchInfoMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(TagLaunchInfo record);

    
    int insertSelective(TagLaunchInfo record);

    
    TagLaunchInfo selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(TagLaunchInfo record);

    
    int updateByPrimaryKey(TagLaunchInfo record);

    List<TagLaunchInfo> listByQuery(TagLaunchQueryDTO queryDTO);
}