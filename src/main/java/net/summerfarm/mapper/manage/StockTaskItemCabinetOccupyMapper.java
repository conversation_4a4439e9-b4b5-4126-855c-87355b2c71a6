package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.wms.StockTaskItemCabinetOccupyDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * @Date 2023/5/22 10:09
 * @<AUTHOR>
 */
public interface StockTaskItemCabinetOccupyMapper {

    int insert(StockTaskItemCabinetOccupyDO record);

    List<StockTaskItemCabinetOccupyDO> selectListByStockTaskIdOccupyed(@Param("stockTaskId") Integer stockTaskId,
                                                                       @Param("skuList") List<String> skuList);

    void softDeleteCabinetInventory(@Param("stockTaskId")Integer stockTaskId, @Param("sku") String sku, @Param("cabinetCode") String cabinetCode);

    int updatePickChange(@Param("id") Long id, @Param("pickChangeQuantity") Integer pickChangeQuantity);

    int updateReleaseChange(@Param("id") Long id, @Param("releaseChangeQuantity") Integer releaseChangeQuantity);
}
