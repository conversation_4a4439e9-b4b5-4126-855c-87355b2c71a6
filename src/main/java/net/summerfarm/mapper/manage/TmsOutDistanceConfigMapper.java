package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.TmsOutDistanceConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TmsOutDistanceConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TmsOutDistanceConfig record);

    int insertSelective(TmsOutDistanceConfig record);

    TmsOutDistanceConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TmsOutDistanceConfig record);

    int updateByPrimaryKey(TmsOutDistanceConfig record);

    List<TmsOutDistanceConfig> selectByTmsOutDistanceConfig(@Param("storeNo") Integer storeNo,@Param("state") Integer state);
}