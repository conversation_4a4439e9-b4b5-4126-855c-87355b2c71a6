package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.offline.StockDashboardHistory;
import net.summerfarm.model.input.purchase.StockDashboardQueryInput;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StockDashboardHistoryMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StockDashboardHistory record);

    int insertSelective(StockDashboardHistory record);

    StockDashboardHistory selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StockDashboardHistory record);

    int updateByPrimaryKey(StockDashboardHistory record);

    List<StockDashboardHistory> selectList(StockDashboardQueryInput queryInput);

    // 查询历史总销量
    List<StockDashboardHistory> selectHistorySaleQuantity(StockDashboardQueryInput queryInput);
}