package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.FrontCategoryToArea;

import java.util.List;

public interface FrontCategoryToAreaMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(FrontCategoryToArea record);

    int insertSelective(FrontCategoryToArea record);

    FrontCategoryToArea selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(FrontCategoryToArea record);

    int updateByPrimaryKey(FrontCategoryToArea record);

    /**
     * 查询前台类目城市信息
     * @param frontCategoryId
     * @return
     */
    List<FrontCategoryToArea> selectByFrontCategoryId(Integer frontCategoryId);

    List<FrontCategoryToArea> selectByAreaNo(Integer areaNo);
}