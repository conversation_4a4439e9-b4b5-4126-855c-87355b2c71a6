package net.summerfarm.mapper.manage;

import net.summerfarm.biz.finance.dto.FinancialInvoiceUserDTO;
import net.summerfarm.biz.finance.input.FinancialFailedInvoiceQuery;
import net.summerfarm.model.DTO.FinancialInvoiceImportDTO;
import net.summerfarm.model.domain.FinancialInvoice;
import net.summerfarm.model.input.FinancialInvoiceExportQuery;
import net.summerfarm.model.input.FinancialInvoiceQuery;
import net.summerfarm.model.vo.FinanceInvoiceExportVo;
import net.summerfarm.model.vo.FinancialInvoiceVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

public interface FinancialInvoiceMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FinancialInvoice record);

    int insertSelective(FinancialInvoice record);

    /**
     * 获取发票信息
     * @param id 发票id
     * @return 发票信息
     */
    FinancialInvoice selectByPrimaryKey(Long id);

    /**
     * 查询发票
     * @param financeOrderId
     * @return
     */
    List<FinancialInvoice> selectByFinanceOrderId(Long financeOrderId);

    /**
     * 根据订单号查询作废发票信息
     * @param query 查询条件
     * @return
     */
    List<Long> selectIdByQuery(FinancialInvoiceQuery query);

    /**
     * 更新发票信息
     * @param record 发票信息
     * @return ok
     */
    int updateByPrimaryKeySelective(FinancialInvoice record);

    /**
     * 账期审核未通过,状态更改为删除,此时票据未开出来
     * @param financialInvoiceVO 发票信息
     * @return ok
     */
    int updateByCancel(FinancialInvoiceVO financialInvoiceVO);

    /**
     * 根据条件查询
     * @param query 查询条件
     * @return 发票信息列表
     */
    List<FinancialInvoiceVO> selectByKey(FinancialInvoiceQuery query);

    /**
     * 更新发票的物流单号
     * @param id 票据id
     * @param express 快递单号
     */
    int updateExpress(@Param("id") Long id, @Param("express") String express);

    /**
     * 根据订单号查询关联的发票信息
     * @param financialInvoiceUserDTO 订单信息
     * @return 开票记录与订单的关系信息
     */
    List<FinancialInvoiceVO> selectFinancialInvoiceByOrder(FinancialInvoiceUserDTO financialInvoiceUserDTO);

    /**
     * 查询发票信息
     * @param financialInvoiceQuery 查询条件
     * @return 发票信息
     */
    List<FinancialInvoice> selectFinancialInvoiceByQuery(FinancialInvoiceQuery financialInvoiceQuery);

    /**
     * 查询发票信息
     * @param invoiceIds 根据ids查询信息
     * @return 发票信息
     */
    List<FinancialInvoice> selectByIds(@Param("invoiceIds")List<Long> invoiceIds);

    /**
     * 导出发票列表
     *
     * @param query 查询
     * @return {@link List}<{@link FinanceInvoiceExportVo}>
     */
    List<FinanceInvoiceExportVo> selectExportInvoice(FinancialInvoiceExportQuery query);

    /**
     * 查询快递单号为空的列表
     *
     * @param ids id
     * @return {@link List}<{@link Integer}>
     */
    List<Long> selectEmptyExpressByIds(@Param("ids")List<Long> ids);

    /**
     * 批量更新发票信息
     *
     * @param list
     */
    void updateBatchById(@Param("list") List<FinancialInvoiceImportDTO> list);

    /**
     * 查询需要重开的发票
     * @return
     */
    List<FinancialInvoice> selectRedoInvoices(FinancialFailedInvoiceQuery query);

    /**
     * 根据账单查询发票
     * @param billNo
     * @return
     */
    FinancialInvoice selectByBillNo(String billNo);
}