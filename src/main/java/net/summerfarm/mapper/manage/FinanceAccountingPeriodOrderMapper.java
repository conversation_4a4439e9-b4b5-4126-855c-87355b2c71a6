package net.summerfarm.mapper.manage;

import com.sun.org.apache.xpath.internal.operations.Bool;
import net.summerfarm.model.domain.FinanceAccountingPeriodOrder;
import net.summerfarm.model.domain.offline.FinanceBillAfterSaleDetails;
import net.summerfarm.model.domain.offline.FinanceBillRevenueDetails;
import net.summerfarm.model.input.AccountingPeriodOrderQuery;
import net.summerfarm.model.input.finance.AccountingPeriodOrderInput;
import net.summerfarm.model.vo.BillInfoVo;
import net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO;
import net.summerfarm.model.vo.finance.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 2021-12-08
 */
@Repository
public interface FinanceAccountingPeriodOrderMapper {

    /**
     * 根据id查询账单信息
     *
     * @param id
     * @return
     */
    FinanceAccountingPeriodOrder selectByPrimaryKey(Long id);

    /**
     * 根据id查询账单信息
     *
     * @param id
     * @return
     */
    FinanceAccountingPeriodOrderVO selectById(@Param("id") Long id);

    /**
     * 查询账单信息
     *
     * @param id
     * @param type
     * @return
     */
    FinanceAccountingPeriodOrderVO selectByDownload(@Param("id") Long id, @Param("type") Integer type);

    /**
     * 删除没有订单的账单
     *
     * @return
     */
    int deleteZeroOrders();

    /**
     * 删除指定账单
     *
     * @param id id
     * @return int
     */
    int deleteById(Long id);

    int insert(FinanceAccountingPeriodOrder record);

    /**
     * 生成账期账单
     *
     * @param record
     * @return
     */
    int insertSelective(FinanceAccountingPeriodOrder record);

    /**
     * 查询企业工商名称
     *
     * @param adminId
     * @return
     */
    List<String> selectByAdmin(@Param("adminId") Long adminId);

    /**
     * 查询所属销售
     *
     * @param adminId
     * @return
     */
    List<String> selectBySaleName(@Param("adminId") Long adminId);

    /**
     * 查询账期订单列表
     *
     * @param accountingPeriodOrderQuery
     * @return
     */
    List<FinanceAccountingPeriodOrderVO> selectList(AccountingPeriodOrderQuery accountingPeriodOrderQuery);

    /**
     * 今日出账信息
     *
     * @param localDateTime 今天零点
     * @return 出账信息
     */
    FinanceAccountingPeriodOrderVO selectToday(@Param("localDateTime") LocalDateTime localDateTime);

    /**
     * 应收账单超时未确认
     *
     * @param localDateTime 逾期时间
     * @return 预期信息
     */
    FinanceAccountingPeriodOrderVO selectOverdue(@Param("localDateTime") LocalDateTime localDateTime);

    /**
     * 更新信息
     *
     * @param financeAccountingPeriodOrder
     * @return
     */
    int updateByPrimaryKeySelective(FinanceAccountingPeriodOrder financeAccountingPeriodOrder);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(FinanceAccountingPeriodOrder record);

    /**
     * 更新账单门店数量
     *
     * @param id
     * @return
     */
    int updateByQuantity(Long id);

    /**
     * 查询账单
     *
     * @param query
     * @return
     */
    FinanceAccountingPeriodOrder selectOne(FinanceAccountingPeriodOrder query);

    /**
     * 根据账单编号查询今天有几个同款账单
     *
     * @param billNumber 账单编号
     * @return 帐单数
     */
    int selectBillNumber(String billNumber);

    /**
     * 根据账单编号查询账单编号位数
     *
     * @param billNumber 账单编号
     * @return 帐单数
     */
    Integer selectBillNo(String billNumber);

    /**
     * 查询账单的金额数据
     *
     * @param id
     * @return
     */
    FinanceAccountingPeriodOrder selectMoney(@Param("id") Long id);


    /**
     * 根据账单编号查询
     *
     * @param billNumber
     * @return
     */
    FinanceAccountingPeriodOrderVO selectByBillNumber(String billNumber);

    /**
     * 根据账单编号查询其中的订单编号
     *
     * @param billNumber
     * @return
     */
    List<String> selectOrderNoList(String billNumber);

    /**
     * 查询已经确认的账单
     *
     * @return
     */
    List<FinanceAccountingPeriodOrderVO> selectConfirm();

    /**
     * 根据adminId、invoiceId查询账单编号
     *
     * @param adminId   品牌id
     * @param invoiceId 发票抬头id
     * @return 返回多条账单编号数据
     */
    List<FinanceAccountingPeriodOrderVO> selectBillNumberList(Long adminId, Long invoiceId);

    /**
     * 查询账期订单列表
     *
     * @param accountingPeriodOrderQuery
     * @return
     */
    List<FinanceAccountingPeriodOrderVO> selectBySalerIdList(AccountingPeriodOrderQuery accountingPeriodOrderQuery);

    /**
     * 根据品牌id或者门店id、发票抬头id查询账期订单账单
     *
     * @param accountingPeriodOrderQuery
     * @return
     */
    List<FinanceAccountingPeriodOrderVO> selectByInvoiceBillList(AccountingPeriodOrderQuery accountingPeriodOrderQuery);

    /**
     * 根据id查询账期订单账单
     *
     * @param id
     * @return
     */
    FinanceAccountingPeriodOrderVO selectByIdInfo(Long id);

    /**
     * 根据当天时间查询账期订单账单
     *
     * @param currentDay
     * @return
     */
    List<FinanceAccountingPeriodOrderVO> selectCurrentDayBillList(LocalDate currentDay);


    /**
     * 根据品牌名称模糊搜索账期订单品牌
     *
     * @param nameRemakes
     * @return
     */
    List<FinanceAccountingPeriodOrderVO> selectByPeriodOrderNameRemakes(@Param("nameRemakes") String nameRemakes);

    /**
     * 根据门店名称模糊搜索账期订单品牌
     *
     * @param mname
     * @return
     */
    List<FinanceAccountingPeriodOrderVO> selectByPeriodOrderMname(String mname);

    /**
     * 根据品牌id、门店id查询账期订单品牌的发票信息
     *
     * @param accountingPeriodOrderQuery
     * @return
     */
    List<FinanceAccountingPeriodOrderVO> selectByPeriodOrderInvoice(AccountingPeriodOrderQuery accountingPeriodOrderQuery);





    /**
     * 获取门店账单生成日期
     *
     * @param mId
     * @return {@link Date}
     */
    LocalDateTime selectBillGenerationTimeByMId(@Param("mId") Integer mId);


    /**
     * 账期订单列表
     *
     * @param input 查询条件
     * @return {@link List}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    List<FinanceAccountingPeriodOrderSummary> allList(AccountingPeriodOrderInput input);

    /**
     * 账期信息列表
     *
     * @param input
     * @return {@link List}<{@link FinanceAccountingPeriodOrderResult}>
     */
    List<FinanceAccountingPeriodOrderResult> listPeriod(AccountingPeriodOrderInput input);

    /**
     * 账单明细excel导出 明细概要
     *
     * @param billNo 账单编号
     * @return {@link FinancePeriodOverviewExportVO}
     */
    FinancePeriodOverviewExportVO selectOverview(@Param("billNo") String billNo);

    /**
     * 账单明细excel导出 订单明细
     *
     * @param orderNo 订单号
     * @return {@link List}<{@link FinancePeriodOrderItemExportVO}>
     */
    List<FinancePeriodOrderItemExportVO> selectPeriodDetail(@Param("orderNo") String orderNo);

    /**
     * 获取指定门店时间范围内的售后单
     *
     * @param mId           mid
     * @param startDate     开始时间
     * @param endDate       结束时间
     * @param isAfterUpdate 是否是更新后 true 是
     * @return {@link List}<{@link FinancePeriodAfterSaleItemExportVO}>
     */
    List<FinancePeriodAfterSaleItemExportVO> selectAfterSaleByMIdAndDate(@Param("mId") Long mId, @Param("dateStart") LocalDateTime startDate, @Param("dateEnd") LocalDateTime endDate, @Param("isAfterUpdate")boolean isAfterUpdate);
    
    /**
     * 账单明细excel导出 调整单明细
     *
     * @param billNo 账单编号
     * @return {@link List}<{@link FinancePeriodAdjustmentItemExportVO}>
     */
    List<FinancePeriodAdjustmentItemExportVO> selectPeriodAdjustmentItem(@Param("billNo") String billNo);

    /**
     * 账单概览excel
     *
     * @param billNos      账单号
     * @param paymentCycle 付款周期
     * @return {@link List}<{@link FinanceReceivableOverviewExportVO}>
     */
    List<FinanceReceivableOverviewExportVO> selectReceivableOverview(@Param("billNos") List<String> billNos, @Param("paymentCycle") Integer paymentCycle);

    /**
     * 逾期详情
     *
     * @param input 输入
     * @return {@link List}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    List<FinanceAccountingPeriodOrderSummary>  overdueDetails(AccountingPeriodOrderInput input);

    /**
     * 根据品牌名称搜索账期订单品牌
     *
     * @param nameRemakes
     * @return
     */
    List<FinanceAccountingPeriodOrderVO> selectByNameRemakes(@Param("nameRemakes") String nameRemakes);

    /**
     * 查询账单详情
     *
     * @param billNos 账单号
     * @return {@link List}<{@link BillInfoVo}>
     */
    List<BillInfoVo> selectByBillNos(@Param("billNos") List<String> billNos);

    /**
     * 查询大客户未开票金额
     *
     * @param nameRemakes 名字重塑
     * @return {@link List}<{@link BigDecimal}>
     */
    List<BigDecimal> selectNotInvoicedAmount(@Param("nameRemakes") String nameRemakes);


    /**
     * 查询账单门店金额信息
     *
     * @param billNo  账单编号
     * @param mname   门店名称
     * @param mId     m id
     * @param salerName 销售
     * @return {@link List}<{@link FinanceStoreDetailVo}>
     */
    List<FinanceStoreDetailVo> selectStoreDetailByBillNo(@Param("billNo") String billNo,@Param("mname")String mname,@Param("mId")Integer mId,@Param("salerName")String salerName);

    /**
     * 查询门店的指定时间内的跨期售后
     *
     * @param mId       m id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param billNo    账单编号
     * @return {@link List}<{@link FinancePeriodOrderItemExportVO}>
     */
    List<FinancePeriodOrderItemExportVO> selectInterTemporalAfterSale(@Param("mId") Long mId, @Param("startDateTime") LocalDateTime startTime,
                                                                      @Param("endDateTime") LocalDateTime endTime, @Param("billNo") String billNo);
}
