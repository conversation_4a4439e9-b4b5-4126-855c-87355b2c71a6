package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.ScheduleTask;
import net.summerfarm.model.vo.ScheduleTaskVO;

import java.util.List;

public interface ScheduleTaskMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ScheduleTask record);

    int insertSelective(ScheduleTask record);

    ScheduleTask selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ScheduleTask record);

    int updateByPrimaryKey(ScheduleTask record);

    /**
     * 查询任务
     * @param query 默认查未删除数据（delete_flag 为false）
     * @return
     */
    List<ScheduleTaskVO> list(ScheduleTask query);

    /**
     * 准备执行任务，将待执行的更新为执行中
     * @param id id
     * @return
     */
    int readyToExec(Integer id);

    /**
    * 查询信息
    */
    ScheduleTask selectOneMsg(ScheduleTask scheduleTask);
}