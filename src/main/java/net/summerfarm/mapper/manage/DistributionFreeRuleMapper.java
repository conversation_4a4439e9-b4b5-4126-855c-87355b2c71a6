package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DistributionFreeRule;
import net.summerfarm.model.vo.DistributionFreeRuleVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DistributionFreeRuleMapper {


    List<DistributionFreeRule> queryById(Integer id);

    Integer insertFreeRule(List<DistributionFreeRule> distributionFreeRules);

    Integer updateFreeRule(DistributionFreeRule distributionFreeRule);

    Integer insertFreeRuleVO(List<DistributionFreeRuleVO> distributionFreeRuleVO);
}
