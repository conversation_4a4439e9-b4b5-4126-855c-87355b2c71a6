package net.summerfarm.mapper.manage;


import net.summerfarm.model.domain.DistributionRule;
import net.summerfarm.model.vo.DistributionRuleVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DistributionRuleMapper {

    List<DistributionRule> selectByAdminId(Integer id);


    Integer insertBathRule(List<DistributionRule> list);

    Integer updateRule(DistributionRule distributionRule);

    Integer insertBathRuleVO(List<DistributionRuleVO> list);

    List<DistributionRuleVO> queryListRule(Integer id);

    List<DistributionRuleVO> queryRuleList(@Param("ids") List<Integer> ids);


    DistributionRule selectByAdminIdArea(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo);

}
