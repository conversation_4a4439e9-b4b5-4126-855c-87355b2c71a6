package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MerchantOrderRecord;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface MerchantOrderRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(MerchantOrderRecord record);

    int insertSelective(MerchantOrderRecord record);

    MerchantOrderRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(MerchantOrderRecord record);

    int updateByPrimaryKey(MerchantOrderRecord record);

    List<String> selectNotExistSku(@Param("skus") List<String> skus);

    void insertBySkus(@Param("notExistSkus") List<String> notExistSkus, @Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

    void deleteByAll();

}