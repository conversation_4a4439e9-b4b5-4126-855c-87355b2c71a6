package net.summerfarm.task;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.metadata.RowData;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.StringUtils;

import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.client.provider.DownloadCenterProvider;
import net.summerfarm.common.client.req.DownloadCenterUploadReq;
import net.summerfarm.common.excel.LargeDataSetExporter;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.enums.periodOrderTypeEnum;
import net.summerfarm.enums.StockDamageTaskEnum.status;
import net.summerfarm.enums.finance.FinanceConstants;
import net.summerfarm.mapper.manage.AfterSaleOrderMapper;
import net.summerfarm.model.input.AfterSaleProofExportInput;
import net.summerfarm.model.vo.AfterSaleOrderVO;
import net.summerfarm.service.AfterSaleOrderService;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;

import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static jdk.nashorn.internal.runtime.regexp.joni.Config.log;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Component
public class AsyncOrderTaskService {

    private static final Logger logger = LoggerFactory.getLogger(AfterSaleOrderService.class);

    @DubboReference
    private DownloadCenterProvider downloadCenterProvider;

    @Resource
    private RedisTemplate<String,String> redisTemplate;

    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;


    public String asyncGenerateProofExcel(AfterSaleProofExportInput input, Long resId,String finalName) {
        HtmlDataExporter handler = new HtmlDataExporter(finalName, input.getDomain());
        afterSaleOrderMapper.selectAfterSaleProofForExport(input, handler);
        handler.clearData();

        OssUploadResult upload = null;
        File file = new File(handler.getLocalFilePath());
        logger.info("开始上传文件到OSS:{}", file.getAbsolutePath());

        // 获取filepath
        upload = OssUploadUtil.upload(finalName, file, OSSExpiredLabelEnum.THREE_DAY);

        // 上传文件到下载中心
        DownloadCenterUploadReq downloadCenterUploadReq = new DownloadCenterUploadReq();
        downloadCenterUploadReq.setResId(resId);
        downloadCenterUploadReq.setFileName(finalName);
        downloadCenterUploadReq.setBizStatus(DownloadCenterEnum.BizStatusEnum.SUCCESS);
        downloadCenterUploadReq.setFilePath(upload.getObjectOssKey());
        DubboResponse<Boolean> uploadResult = downloadCenterProvider.uploadFile(downloadCenterUploadReq);
        logger.info("上传文件:{} 到OSS的结果:{}", handler.getLocalFilePath(), JSON.toJSONString(uploadResult));
        return upload.getObjectOssKey();
    }


    public static class HtmlDataExporter extends LargeDataSetExporter<AfterSaleOrderVO, List<AfterSaleOrderVO>> {

        private BufferedWriter writer;
        private final String localFilePath;
        private final String domainName;

        public String getLocalFilePath() {
            return this.localFilePath;
        }

        public HtmlDataExporter(String fileName, String domainName) {
            this.domainName = domainName;
            try {
                localFilePath = File.createTempFile(fileName, ".html").getAbsolutePath();
                logger.info("创建本地HTML文件开始...:{}", localFilePath);
                writer = new BufferedWriter(new FileWriter(localFilePath));

                // 写入HTML头部
                writer.write("<!DOCTYPE html>\n");
                writer.write("<html lang=\"en\">\n");
                writer.write("<head>\n");
                writer.write("<meta charset=\"UTF-8\">\n");
                writer.write("<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
                writer.write(String.format("<title>%s</title>\n", fileName));
                writer.write("<style>\n");
                writer.write("table { width: 100%; border-collapse: collapse; margin: 20px 0; }\n");
                writer.write("th, td { border: 1px solid #ccc; padding: 10px; text-align: left; word-wrap: break-word; }\n"); // Added word-wrap
                writer.write("td:nth-child(1), td:nth-child(2), td:nth-child(3), td:nth-child(5), td:nth-child(6), td:nth-child(7), td:nth-child(9), td:nth-child(10) { min-width: 100px; }"); // Min widths for certain columns
                writer.write("td:nth-child(4) { min-width: 150px; }"); // Min width for 商品名称
                writer.write("td:nth-child(8) { min-width: 200px; }"); // Min width for 售后备注
                writer.write("td:nth-child(11) { min-width: 250px; }"); // Min width for 售后凭证
                writer.write(".image-gallery { display: flex; flex-wrap: wrap; gap: 5px; }\n");
                writer.write(".image-container { position: relative; }\n");
                writer.write(".thumbnail { width: 100px; height: 100px; object-fit: contain; cursor: pointer; border: 1px solid #eee; }\n"); // Added border
                writer.write(".modal { display: none; position: fixed; z-index: 1; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.9); }\n");
                writer.write(".modal-content { margin: auto; display: block; width: auto; height: auto; max-width: 90%; max-height: 90vh; }\n"); // Adjusted modal image size
                writer.write(".close { position: absolute; top: 15px; right: 35px; color: #f1f1f1; font-size: 40px; font-weight: bold; transition: 0.3s; }\n");
                writer.write(".close:hover, .close:focus { color: #bbb; text-decoration: none; cursor: pointer; }\n");
                writer.write("</style>\n");
                writer.write("</head>\n");
                writer.write("<body>\n");
                writer.write(String.format("<h1>%s</h1>\n", fileName));

                // 写入表格头部
                writer.write("<table>\n<thead>\n<tr>\n");
                writer.write("<th>门店ID</th>\n<th>门店名</th>\n<th>订单号</th>\n<th>售后单号</th>\n<th>商品名称</th>\n");
                writer.write("<th>sku</th>\n<th>售后数量</th>\n<th>售后单位</th>\n<th>售后备注</th>\n"); // Added 售后数量, 售后单位, 售后备注
                writer.write("<th>售后金额</th>\n<th>售后单创建时间</th>\n<th>售后凭证</th>\n");
                writer.write("</tr>\n</thead>\n<tbody>\n");

                // 添加模态框
                writer.write("<div id=\"imageModal\" class=\"modal\">\n");
                writer.write("  <span class=\"close\">&times;</span>\n");
                writer.write("  <img class=\"modal-content\" id=\"modalImage\">\n");
                writer.write("</div>\n");
            } catch (IOException e) {
                throw new RuntimeException("创建本地HTML文件失败", e);
            }
        }

        @Override
        protected List<List<AfterSaleOrderVO>> convert(AfterSaleOrderVO data) throws MalformedURLException {
            throw new UnsupportedOperationException("未实现的方法 'convert'");
        }

        @Override
        protected List<List<AfterSaleOrderVO>> convertBatch(Collection<AfterSaleOrderVO> dataList) {
            if (CollectionUtils.isEmpty(dataList)) {
                return Collections.emptyList();
            }
            logger.info("convertBatch....size:{}", dataList.size());
            List<List<AfterSaleOrderVO>> result = new ArrayList<>(1);
            result.add(new ArrayList<>(dataList));
            return result;
        }

        @Override
        public void clearData() {
            try {
                super.clearData();
                // 写入表格和HTML尾部
                writer.write("</tbody>\n</table>\n");

                // 添加JavaScript代码
                writer.write("<script>\n");
                writer.write("var modal = document.getElementById('imageModal');\n");
                writer.write("var modalImg = document.getElementById('modalImage');\n");
                writer.write("var span = document.getElementsByClassName('close')[0];\n");
                writer.write("function openModal(imgSrc) {\n");
                writer.write("  modal.style.display = 'block';\n");
                writer.write("  modalImg.src = imgSrc;\n");
                writer.write("}\n");
                writer.write("span.onclick = function() {\n");
                writer.write("  modal.style.display = 'none';\n");
                writer.write("}\n");
                // Close modal on click outside image
                writer.write("modal.onclick = function(event) {\n");
                writer.write("  if (event.target == modal) {\n");
                writer.write("    modal.style.display = 'none';\n");
                writer.write("  }\n");
                writer.write("}\n");
                writer.write("</script>\n");

                writer.write("</body>\n</html>\n");
                logger.info("写入HTML文件完成:{}", localFilePath);
                writer.close();
            } catch (IOException e) {
                throw new RuntimeException("写入HTML内容失败", e);
            }
        }

        @Override
        protected void flushData(List<List<AfterSaleOrderVO>> dataList) {
            if (CollectionUtils.isEmpty(dataList)) {
                return;
            }
            try {
                for (List<AfterSaleOrderVO> tableData : dataList) {
                    logger.info("写入数据到HTML文件...size:{}", tableData.size());
                    for (AfterSaleOrderVO row : tableData) {
                        writer.write("<tr>\n");
                        writer.write("<td>" + (row.getmId() != null ? row.getmId() : "") + "</td>\n");
                        writer.write("<td>" + (row.getMname() != null ? row.getMname() : "") + "</td>\n");
                        writer.write("<td>" + (row.getOrderNo() != null ? row.getOrderNo() : "") + "</td>\n");
                        writer.write("<td>" + (row.getAfterSaleOrderNo() != null ? row.getAfterSaleOrderNo() : "") + "</td>\n");
                        writer.write("<td>" + (row.getPdName() != null ? row.getPdName() : "") + "</td>\n");
                        writer.write("<td>" + (row.getSku() != null ? row.getSku() : "") + "</td>\n");
                        // --- New Columns Start ---
                        writer.write("<td>" + (row.getQuantity() != null ? row.getQuantity() : "") + "</td>\n");
                        writer.write("<td>" + (row.getAfterSaleUnit() != null ? row.getAfterSaleUnit() : "") + "</td>\n");
                        writer.write("<td>" + (row.getApplyRemark() != null ? row.getApplyRemark() : "") + "</td>\n");
                        // --- New Columns End ---
                        writer.write("<td>" + (row.getHandleNum() != null ? row.getHandleNum() : "") + "</td>\n");
                        writer.write("<td>" + row.getAddTime() + "</td>\n"); // Assuming getAddTime returns Date

                        writer.write("<td>\n<div class=\"image-gallery\">\n");
                        if (StringUtils.isNotBlank(row.getProofPic())) { // Use isNotBlank
                            AtomicInteger imageCounter = new AtomicInteger(1);
                            // Handle potential full URLs or relative paths
                            String[] imageUrls = row.getProofPic().split(";");
                            for (String imageUrl : imageUrls) {
                                if (StringUtils.isBlank(imageUrl)) {
                                    continue;
                                } // Skip empty splits
                                int index = imageCounter.getAndIncrement();
                                String imgSrc;
                                // Check if imageUrl is already a full URL
                                if (imageUrl.toLowerCase().startsWith("http://") || imageUrl.toLowerCase().startsWith("https://")) {
                                    imgSrc = imageUrl;
                                } else {
                                    imgSrc = this.domainName + imageUrl; // Prepend domain only if it's a relative path
                                }
                                writer.write("<div class=\"image-container\">\n");
                                writer.write("  <img src=\"" + imgSrc + "\" alt=\"凭证" + index
                                        + "\" class=\"thumbnail\" onclick=\"openModal('" + imgSrc + "')\" onerror=\"this.style.display='none'\" />\n"); // Added onerror
                                writer.write("</div>\n");
                            }
                        } else {
                            writer.write("无"); // Indicate no images
                        }
                        writer.write("</div>\n</td>\n");
                        writer.write("</tr>\n");
                    }
                }
                writer.flush();
            } catch (IOException e) {
                throw new RuntimeException("写入HTML内容失败", e);
            }
        }
    }

}
