package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MatchPurchasesPlan;
import net.summerfarm.model.domain.PurchasesPlan;
import net.summerfarm.model.vo.MatchPurchaseOrderVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface MatchPurchasesPlanMapper {

    /**
     * 将采购计划删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Integer id);

    int insert(MatchPurchasesPlan record);

    /**
     * 插入采购计划信息，等待采购发票分配
     * @param record
     * @return
     */
    int insertSelective(MatchPurchasesPlan record);

    /**
     * 根据采购计划id查询信息
     * @param id
     * @return
     */
    MatchPurchasesPlan selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(MatchPurchasesPlan record);

    /**
     * 更新采购计划匹配进度 待匹配金额
     * @param record
     * @return
     */
    int updateByPrimaryKey(MatchPurchasesPlan record);

    /**
     * 采购计划更改时，同步更改数据
     * @param record
     * @return
     */
    int update(PurchasesPlan record);

    /**
     * 采购单sku匹配日志
     * @param id
     * @return
     */
    List<MatchPurchaseOrderVO> selectSku(Integer id);

}