package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockTaskItemDetail;
import net.summerfarm.model.domain.WmsDamageStockItem;
import net.summerfarm.model.vo.StockTaskItemDetailVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

@Repository
public interface StockTaskItemDetailMapper {

    int insert(StockTaskItemDetail stockTaskItemDetail);

    StockTaskItemDetail selectOne(StockTaskItemDetail stockTaskItemDetail);

    List<StockTaskItemDetail> selectOneNew(StockTaskItemDetail stockTaskItemDetail);

    int update(StockTaskItemDetail stockTaskItemDetail);

    List<StockTaskItemDetailVO> selectByItemId(@Param("stockTaskItemId") Integer stockTaskItemId);

    int merge(StockTaskItemDetail stockTaskItemDetail);

    List<StockTaskItemDetailVO> selectList(StockTaskItemDetail stockTaskItemDetail);

    boolean hasUnFinishDamageTask(@Param("storeNo") Integer areaNo, @Param("sku") String sku, @Param("batch") String listNo, @Param("qualityDate") LocalDate qualityDate);

    /**
     * 根据任务编号类型查询出入库单
     * @param listNo
     * @param type
     * @return
     */
    List<StockTaskItemDetail> selectByTaskNoAndType(@Param("taskNo") String listNo, @Param("type") Integer type);

    List<StockTaskItemDetail> selectByTaskIdsAndTypeAndSku(@Param("taskNos") List<String> taskNos, @Param("type") Integer type, @Param("sku") String sku);
}
