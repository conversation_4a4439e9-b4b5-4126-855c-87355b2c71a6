package net.summerfarm.dao.roadsale;

import net.summerfarm.dao.roadsale.dataobject.RoadSaleDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator 2023/10/12
 */
public interface RoadSaleDAO {
    int deleteByPrimaryKey(Long id);

    int insert(RoadSaleDO record);

    int insertSelective(RoadSaleDO record);

    RoadSaleDO selectByPrimaryKey(Long id);

    List<RoadSaleDO> selectByWarehouseNoAndSkus(@Param("warehouseNo") Integer warehouseNo, @Param("skus") List<String> skus);

    int updateByPrimaryKeySelective(RoadSaleDO record);

    int updateByPrimaryKey(RoadSaleDO record);
}