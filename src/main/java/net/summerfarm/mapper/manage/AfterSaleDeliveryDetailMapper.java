package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AfterSaleDeliveryDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AfterSaleDeliveryDetailMapper {


    void updateById(AfterSaleDeliveryDetail afterSaleDeliveryDetail);

    void updateInterceptFlagByIdList(@Param("afterSaleDeliveryDetailIdList") List<Integer> afterSaleDeliveryDetailIdList,
                                     @Param("interceptFlag") Integer interceptFlag,
                                     @Param("interceptTime")LocalDateTime interceptTime,
                                     @Param("showFlag")Integer showFlag);
}
