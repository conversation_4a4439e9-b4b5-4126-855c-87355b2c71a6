package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.domain.DynamicPriceField;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DynamicPriceFieldMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(DynamicPriceField record);

    
    int insertSelective(DynamicPriceField record);

    
    DynamicPriceField selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(DynamicPriceField record);

    
    int updateByPrimaryKey(DynamicPriceField record);

    List<DynamicPriceField> listAll();

    List<DynamicPriceField> listByIds(@Param("ids") List<Long> ids);
}