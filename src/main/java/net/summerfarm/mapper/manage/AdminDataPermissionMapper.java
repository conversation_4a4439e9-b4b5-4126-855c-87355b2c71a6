package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AdminDataPermission;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * @Package: net.summerfarm.mapper
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/9/29
 */
@Repository
public interface AdminDataPermissionMapper {
    List<AdminDataPermission> selectByAdminId(Integer adminId);

    List<AdminDataPermission> selectByWarehouseNo(@Param("warehouseNo") Integer warehouseNo, @Param("adminIds") List<Integer> adminIds);

    int deleteByAdminId(int adminId);

    int insert(AdminDataPermission adminDataPermission);

    List<Integer> selectDistinct(Set<Integer> dataPermission);

    /**
     * 判断是否有全部仓库权限
     * @param adminId adminId
     * @return t、f
     */
    Boolean hasAllDataPermission(@Param("adminId") Integer adminId);
}
