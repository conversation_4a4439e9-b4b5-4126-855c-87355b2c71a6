package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CrmBdArea;
import net.summerfarm.model.input.SalesDataInput;
import net.summerfarm.model.vo.AdminInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface CrmBdAreaMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(CrmBdArea record);

    CrmBdArea selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CrmBdArea record);

    int updateByPrimaryKey(CrmBdArea record);

    /**
     * 根据区域no获取bd信息,近一个月内已禁用销售仍可查询
     * @param salesDataInput 筛选条件
     * @return bd信息
     */
    List<AdminInfoVo> selectAdminByAreas(SalesDataInput salesDataInput);

    void insertBatch(@Param("areaCityIds") Set<Integer> areaCityIds, @Param("adminId") Integer adminId, @Param("creator") Integer creator);

    void deleteByAdminId(Integer adminId);
    /**
     * 复制信息并插入一条新的
     * @param copyIntInfo 被复制人id
     * @param intInfo 新增销售id
     * @param creator 操作人id
     */
    void copyAreaByAdminId(@Param("copyIntInfo") Integer copyIntInfo,@Param("intInfo") Integer intInfo,@Param("creator") Integer creator);

    /**
     * 获取销售的所负责的城市列表
     * @param adminId 销售id
     * @return 城市no
     */
    List<Integer> selectByAdminId(Integer adminId);
}