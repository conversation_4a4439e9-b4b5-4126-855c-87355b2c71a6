package net.summerfarm.dao.skushare;

import net.summerfarm.biz.skushare.UpdateOrderSkuShare;
import net.summerfarm.dao.skushare.dataobject.OrderSkuShareDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderSkuShareDAO {

    int deleteByPrimaryKey(Long id);

    int insert(OrderSkuShareDO record);

    int insertSelective(OrderSkuShareDO record);

    OrderSkuShareDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderSkuShareDO record);

    int updateByPrimaryKey(OrderSkuShareDO record);

    int updateOrderSkuShareById(UpdateOrderSkuShare updateOrderSkuShare);

    List<OrderSkuShareDO> selectNeedFilledOrderSkuShare(@Param("warehouseNo") Integer warehouseNo, @Param("transferOutSku") String transferOutSku,
                                                        @Param("transferInSku") String transferInSku);
}