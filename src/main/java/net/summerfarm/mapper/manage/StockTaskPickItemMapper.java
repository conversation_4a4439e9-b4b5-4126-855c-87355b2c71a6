package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockTaskPickItem;
import net.summerfarm.model.vo.StockTaskPickVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface StockTaskPickItemMapper {

    int insertBatch(List<StockTaskPickItem> list);

    List<StockTaskPickVO> selectList(@Param("type") Integer type, @Param("deliveryTime") LocalDate deliveryTime,
                                     @Param("storeNo") Integer storeNo, @Param("closeOrderTime") String closeOrderTime);
}
