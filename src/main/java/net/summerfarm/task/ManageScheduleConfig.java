package net.summerfarm.task;


import net.summerfarm.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;



/**
 * @Package: net.summerfarm.TimerSchedule
 * @Description: 定时任务配置类
 * @author: <EMAIL>
 * @Date: 2017/3/9
 */
@Component
public class ManageScheduleConfig {

    @Resource
    private CouponService couponService;
    @Resource
    private StockTaskService stockTaskService;
    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private BiPurchasesConfigService biPurchasesConfigService;

    @Resource
    private TransferService transferService;

    @Resource
    private SampleApplyService sampleApplyService;

    @Resource
    private PrepayInventoryService prepayInventoryService;
    @Resource
    private StockTaskPickService stockTaskPickService;
    @Resource
    private  OrderService orderService;

    @Resource
    private AdminService adminService;
    @Resource
    ChangeRecordService changeRecordService;
    @Resource
    private StockService stockService;

    @Resource
    RedPacketService redPacketService;

    @Resource
    private ProductsService productsService;

    @Resource
    private TraceService traceService;

    // 秒（0~59） 分（0~59） 时（0~23） 天（0~31） 月（0~11） 星期（1~7 1=SUN 或 SUN，MON，TUE，WED，THU，FRI，SAT） 年（1970－2099）
    private static final Logger logger = LoggerFactory.getLogger(ManageScheduleConfig.class);

//    /**
//     * 8点发消息
//     */
//    @Scheduled(cron = "00 00 8 * * ? ")
//    public void arrivalNotice() {
//        areaStoreService.autoArrivalNotice();
//    }


    /**
     * 无效优惠券模板尤其
     */
//    @Scheduled(cron = "00 00 00 * * ? ")
    public void couponStatus() {
        couponService.couponNot();
    }

    /**
     * 自动调拨
     */
//    @Scheduled(cron = "00 00 22 * * ? ")
    public void autoStock() {
        //暂时停止
//        allocationService.autoStock();
    }


//    /**
//     * 一键发货后第二天中午商城订单自动完结 每天12点
//     */
//    @Scheduled(cron = " 00 00 12 * * ? ")
//    public void autoConfirm(){
//        orderService.autoConfirm();
//    }

    /**
     * 自动更新采购信息(安全水位值)
     */
//    @Scheduled(cron = "00 15 23 * * ? ")
    public void purchasesConfigUpdate() {
        biPurchasesConfigService.biPurchasesConfigUpdate();
//          purchasesConfigService.autoUpdate();
//          purchaseCalculateService.runPurchaseTask();
    }


    /**
    * 出样出库任务 20点开始
    */
    // @Scheduled(cron = "00 15 20 * * ? ")
    public void createSampleTask() {
        sampleApplyService.createStockDemo();
    }

    /**
     * 预付商品到期提醒邮件
     */
//   @Scheduled(cron = "0 30 0 * * ?")
   public void sendPrepayInventoryExpireMail(){
       prepayInventoryService.sendExpireMail(LocalDate.now());
   }

    /**
     * 自动生成捡货任务 大客户
     */
    // @Scheduled(cron = "00 30 20 * * ? ")
    public void stockTaskPick() {
        stockTaskPickService.createStockTaskPick("20:00:00");
    }

    /**
     * 自动生成捡货任务 大客户
     */
//    @Scheduled(cron = "00 01 18 * * ? ")
    public void stockTaskMerchantPick() {
        stockTaskPickService.createStockTaskPick("18:00:00");
    }

    /**
    * 自动更新预留库存数据
    */
//    @Scheduled(cron = "00 00 05 * * ? ")
//    public void autoUpdateReserveQuantity(){
//        areaStoreService.autoUpdateReserveQuantity();
//    }

    /**
     * 自动货损、临保转换任务
     */
//    @Scheduled(cron = "00 30 05 * * ? ")
    public void autoDamageAndTransferTask(){
        //自动货损
        stockTaskService.autoDamageTask(LocalDate.now());

        //自动转换
        stockTaskService.autoTemporaryTransfer(LocalDate.now());
    }

    //@Scheduled(cron = "00 00 01 * * ? ")
    public void deleteSalesRecord(){
        stockService.deleteSalesRecord();
    }


    /**
    * 补货出库任务统一8点生成
    */
    //@Scheduled(cron = "00 10 20 * * ? ")
    public void createAfterSaleTask(){
        stockTaskService.createAfterSaleTask();
    }


    /**
    * 22：30 截单时间 退货入库任务
    */
    /*@Scheduled(cron = "00 20 02 * * ? ")*/
    public void autoAfterSaleIn(){
        stockTaskService.autoAfterSaleIn();
    }

    //@Scheduled(cron = "00 00 00 * * ?")
    public void autoUpdateCloseTime(){
        adminService.autoUpdateCloseTime();
    }

        /*   *//**
     * 佣金机制变更邮件发送
     */
    //@Scheduled(cron = "00 00 16 * * ?  00 00 16 * * ?")
    public void ChangeRecord(){
        changeRecordService.queryRecord();
    }

    /**
     * 生成红包.（下次注释）
     */
//    @Scheduled(cron = "0 0 21 18 6 ? ")
    public void executeRp(){
        redPacketService.execute();
    }
}
