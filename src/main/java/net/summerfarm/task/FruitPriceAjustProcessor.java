package net.summerfarm.task;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.SnowflakeUtil;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.mapper.manage.ActivityBasicInfoMapper;
import net.summerfarm.mapper.manage.InterestRateConfigMapper;
import net.summerfarm.mapper.manage.PriceAdjustmentPoolMapper;
import net.summerfarm.mapper.manage.PriceAdjustmentRuleAreaMapper;
import net.summerfarm.model.DTO.FruitTaskDTO;
import net.summerfarm.model.ProductCostChangeEvent;
import net.summerfarm.model.domain.CycleInventoryCost;
import net.summerfarm.model.domain.PriceAdjustment;
import net.summerfarm.model.vo.PriceAdjustmentPoolVO;
import net.summerfarm.mq.AutoChangePriceV2Listener;
import net.summerfarm.repository.price.CycleInventoryCostRepository;
import net.summerfarm.service.ActivityNewService;
import net.summerfarm.service.InterestRateRecordService;
import net.summerfarm.service.MajorPriceService;
import net.summerfarm.service.impl.PriceAdjustServiceImpl;
import net.xianmu.authentication.common.utils.SpringContextUtil;
import net.xianmu.redis.support.util.XmRedisUtils;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;

/**
 * 鲜果调价（一次性任务）
 *
 * @author: <EMAIL>
 * @create: 2023/2/1
 */
@Slf4j
@Component
public class FruitPriceAjustProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private DynamicConfig dynamicConfig;
    @Resource
    private InterestRateRecordService interestRateRecordService;
    @Resource
    private PriceAdjustmentRuleAreaMapper priceAdjustmentRuleAreaMapper;
    @Resource
    private PriceAdjustmentPoolMapper priceAdjustmentPoolMapper;
    @Resource
    private PriceAdjustServiceImpl priceAdjustService;
    @Resource
    private CycleInventoryCostRepository cycleInventoryCostRepository;
    @Resource
    private InterestRateConfigMapper interestRateConfigMapper;
    private final static String AREA_NO = "area_no";


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        try {
            log.info("[根据指定仓重跑鲜果任务]任务开始执行, context: 【{}】", JSON.toJSONString(context));
            String instanceParameters = context.getInstanceParameters();

            FruitTaskDTO fruitTaskDTO = JSON.parseObject(instanceParameters, FruitTaskDTO.class);
            if (fruitTaskDTO == null) {
                log.error("参数为空！");
                return new ProcessResult(true);
            }
            List<String> skus = fruitTaskDTO.getSkus();
            if (CollUtil.isEmpty(skus)) {
                log.error("sku参数为空！");
                return new ProcessResult(true);
            }
            Long warehouseNo = fruitTaskDTO.getWarehouseNo();
            for (String sku : skus) {
                ProductCostChangeEvent event = new ProductCostChangeEvent();
                event.setSku(sku);
                event.setWarehouseNo(warehouseNo);
                CycleInventoryCost cycleInventoryCost = cycleInventoryCostRepository.selectBySku(sku, warehouseNo.intValue());
                if(cycleInventoryCost == null) {
                    log.error("sku:{}, warehouseNo:{}对应的成本不存在!", sku, warehouseNo);
                    continue;
                }
                event.setCurrentCost(cycleInventoryCost.getFirstCycleCost());
                event.setPreviousCost(cycleInventoryCost.getEndCycleCost());
                this.handleMsg(event);
            }
            log.info("[根据指定仓重跑鲜果任务]执行完成!");
        } catch (Exception e) {
            log.error("[根据指定仓重跑鲜果任务]执行失败!", e);
        }
        return new ProcessResult(true);
    }


    public void handleMsg(ProductCostChangeEvent event) {
        try {
            FruitPriceAjustProcessor bean = SpringContextUtil.getBean("fruitPriceAjustProcessor", FruitPriceAjustProcessor.class);
            // 1.生成调价单
            Long businessId = SnowflakeUtil.nextId();
            bean.generatePriceAdjustmentPool(event, businessId);
            // 2.进行调价
            this.doPriceAdjust(businessId);

        } catch (Exception e) {
            log.error("【自动调价】失败,msg:{}", JSON.toJSONString(event), e);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void generatePriceAdjustmentPool(ProductCostChangeEvent event, Long businessId) {
        String sku = event.getSku();
        Integer warehouseNo = event.getWarehouseNo().intValue();
        BigDecimal costPrice = event.getCurrentCost();
        log.info("【自动调价】开始创建调价单,sku:{},warehouseNo:{}, businessId:{}", sku, warehouseNo, businessId);
        interestRateRecordService.changePriceByRate(warehouseNo, sku,
                event.getPreviousCost(), costPrice, null,
                null, businessId);
        log.info("【自动调价】创建调价单已完成,sku:{},warehouseNo:{}", sku, warehouseNo);
    }


    public void doPriceAdjust(Long businessId) {
        log.info("【自动调价】开始进行自动调价");
        // 1.取城市在规则中并且是待定时处理的的
        List<Integer> areaNos = priceAdjustmentRuleAreaMapper.selectByFieldName(AREA_NO);
        // 2.取当前批次需要处理的数据
        List<PriceAdjustmentPoolVO> priceAdjustmentPoolVOS = priceAdjustmentPoolMapper.selectByBusinessIdAndRuleFlag(businessId, 0);
        List<PriceAdjustment> poolList = priceAdjustService.handlePriceAdjustmentPool(areaNos, priceAdjustmentPoolVOS);
        priceAdjustService.handleAutoAdjustPrice(poolList);
        log.info("【自动调价】自动调价完成");
    }


}
