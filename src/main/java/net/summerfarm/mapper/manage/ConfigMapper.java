package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Config;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConfigMapper {

    List<Config> selectAll();

    Config selectOne(@Param("key") String key);

    List<Config> selectStartWith(@Param("key") String key);

    void update(Config config);

    /**
     * 修改value
     * @param key
     * @param value
     * @return
     */
    int updateValue(@Param("key") String key, @Param("value") String value);

    /**
     * 查询特殊大客户名称配置
     * @return
     */
    String selectKeyAccountName();

    int insert(Config config);

    int updateValueByKey(@Param("key") String releaseCard, @Param("value") String value);
}
