package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.WarehouseStockExt;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 库存信息扩展表
 * <AUTHOR> ct
 * create at:  2022/2/23  18:06
 */
@Repository
public interface WarehouseStockExtMapper {


    /**
     * 插入状态信息
     * @param warehouseStockExt
     * @return
     */
    int saveWarehouseStockExt(WarehouseStockExt warehouseStockExt);

    /**
     * 批量插入状态信息
     * @param warehouseStockExtList
     * @return
     */
    int saveWarehouseStockExtBatch(List<WarehouseStockExt> warehouseStockExtList);

    /**
     * 查询状态信息
     * @param warehouseStockExt
     * @return
     */
    WarehouseStockExt selectWarehouseStockExt(WarehouseStockExt warehouseStockExt);

    /**
     * 更新状态信息
     * @param warehouseStockExt
     * @return
     */
    int updateWarehouseStockExt(WarehouseStockExt warehouseStockExt);

    /**
     * 初始化sku状态信息
     * @param sku
     * @return
    */
    int initWarehouseStockExt(String sku);

    /**
     * 批量初始化sku状态信息
     * @param warehouseNo
     * @return
     */
    int initBatchWarehouseStockExt(Integer warehouseNo);

}
