package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.StockTakingDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wjd on 2017/9/27.
 */
@Repository
public interface StockTakingDetailMapper {

    List<StockTakingDetail> select(StockTakingDetail detail);

    int insert(StockTakingDetail stockTakingDetail);

    int insertBatch(List<StockTakingDetail> detailList);

    int update(StockTakingDetail detail);

    int delete(int id);

    StockTakingDetail selectById(int id);

    StockTakingDetail selectOne(StockTakingDetail detail);

    int updateNormal(int takingId);

    int updateUnNormal(int takingId);

    /**
     * 根据条件查询
     * @param sku sku
     * @param areaNo 库存仓
     * @return
     */
    int selectByCondition(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    @RequiresDataPermission(originalField = "s.area_no")
    List<StockTakingDetail> selectAll(Integer areaNo);

}
