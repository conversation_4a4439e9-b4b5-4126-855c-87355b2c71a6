package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DistributionRules;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DistributionRulesMapper {

    int deleteByPrimaryKey(Long id);

    int insert(DistributionRules record);

    int insertSelective(DistributionRules record);

    DistributionRules selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DistributionRules record);

    int updateByPrimaryKey(DistributionRules record);

    void batchInsert(@Param("list") List<DistributionRules> distributionRules);

    int deleteInfo(@Param("list") List<Long> typeIds, @Param("type") int type);
}