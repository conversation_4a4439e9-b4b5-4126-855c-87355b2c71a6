package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DeliveryCar;
import net.summerfarm.model.vo.DeliveryCarVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by wjd on 2018/4/24.
 */
@Repository
public interface DeliveryCarMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(DeliveryCar record);

    int insertSelective(DeliveryCar record);

    DeliveryCar selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DeliveryCar record);

    int updateByPrimaryKey(DeliveryCar record);

    List<DeliveryCar> select(DeliveryCar deliveryCar);

    DeliveryCar selectByPhone(String phone);

    /**
     * 将要查询的条件可以是部分信息作为查询条件，得到List<DeliveryCar>
     * @param deliveryCar
     * @return
     */
    List<DeliveryCar> selectByPartParam(DeliveryCar deliveryCar);

    /**
     * 根据条件查询车队管理
     * @param deliveryCar
     * @return
     */
    List<DeliveryCarVo> selectVo(DeliveryCar deliveryCar);

    /**
     * piliang
     * @param deliveryCarId
     * @return
     */
    DeliveryCar selectListByPrimaryKey(List<Integer> deliveryCarId);
}
