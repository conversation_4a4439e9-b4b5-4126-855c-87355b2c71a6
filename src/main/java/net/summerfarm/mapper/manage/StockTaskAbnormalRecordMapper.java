package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockTaskAbnormalRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/07/12 13:48
 */
@Repository
public interface StockTaskAbnormalRecordMapper {

    List<StockTaskAbnormalRecord> select(@Param("stockTaskId") Integer stockTaskId,@Param("sku") String sku);

    void insert(StockTaskAbnormalRecord record);
}
